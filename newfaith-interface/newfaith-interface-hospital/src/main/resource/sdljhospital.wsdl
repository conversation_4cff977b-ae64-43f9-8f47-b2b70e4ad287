<wsdl:definitions xmlns:soapenc12="http://www.w3.org/2003/05/soap-encoding" xmlns:tns="CommonFsJkServiceImp" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap11="http://schemas.xmlsoap.org/soap/envelope/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soapenc11="http://schemas.xmlsoap.org/soap/encoding/" xmlns:soap12="http://www.w3.org/2003/05/soap-envelope" targetNamespace="CommonFsJkServiceImp">
<wsdl:types>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="CommonFsJkServiceImp">
<xsd:element name="J2165">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2165Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2311">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2311Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2126">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2126Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2161">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2161Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2125">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2125Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2162">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2162Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2128">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2128Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2163">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2163Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2127">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2127Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2164">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2164Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2122">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2122Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2121">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2121Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2124">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2124Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2123">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2123Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2211">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2211Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2212">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2212Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2213">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2213Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2214">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2214Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2114">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2114Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2111">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2111Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2112">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2112Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2113">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2113Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2131">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2131Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2174">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2174Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2175">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2175Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2137">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2137Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2172">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2172Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2136">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2136Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2173">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2173Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2135">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2135Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2134">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2134Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2171">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2171Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2133">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2133Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2132">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2132Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="FsjkWxJob">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="FsjkWxJobResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2142">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2142Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2141">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2141Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J3335">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J3335Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2143">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2143Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2181">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2181Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2182">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2182Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2183">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2183Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2184">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2184Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J21113">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J21113Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J21114">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J21114Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J21111">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J21111Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J21112">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J21112Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2153">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2153Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2152">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2152Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2151">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2151Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2321">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2321Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2192">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2192Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2191">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2191Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J21101">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J21101Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J21102">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J21102Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="GetServerTime">
<xsd:complexType/>
</xsd:element>
<xsd:element name="GetServerTimeResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2222">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2222Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2221">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="J2221Response">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
</xsd:schema>
</wsdl:types>
<wsdl:message name="J2132Request">
<wsdl:part name="parameters" element="tns:J2132"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2173Response">
<wsdl:part name="parameters" element="tns:J2173Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2321Request">
<wsdl:part name="parameters" element="tns:J2321"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2181Request">
<wsdl:part name="parameters" element="tns:J2181"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2182Response">
<wsdl:part name="parameters" element="tns:J2182Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2165Request">
<wsdl:part name="parameters" element="tns:J2165"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J21102Request">
<wsdl:part name="parameters" element="tns:J21102"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2121Request">
<wsdl:part name="parameters" element="tns:J2121"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2111Response">
<wsdl:part name="parameters" element="tns:J2111Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2152Response">
<wsdl:part name="parameters" element="tns:J2152Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2212Response">
<wsdl:part name="parameters" element="tns:J2212Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2163Response">
<wsdl:part name="parameters" element="tns:J2163Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2112Response">
<wsdl:part name="parameters" element="tns:J2112Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2152Request">
<wsdl:part name="parameters" element="tns:J2152"> </wsdl:part>
</wsdl:message>
<wsdl:message name="FsjkWxJobResponse">
<wsdl:part name="parameters" element="tns:FsjkWxJobResponse"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2175Response">
<wsdl:part name="parameters" element="tns:J2175Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2125Request">
<wsdl:part name="parameters" element="tns:J2125"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2141Response">
<wsdl:part name="parameters" element="tns:J2141Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2161Response">
<wsdl:part name="parameters" element="tns:J2161Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2142Response">
<wsdl:part name="parameters" element="tns:J2142Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2211Response">
<wsdl:part name="parameters" element="tns:J2211Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2112Request">
<wsdl:part name="parameters" element="tns:J2112"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2122Request">
<wsdl:part name="parameters" element="tns:J2122"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2137Response">
<wsdl:part name="parameters" element="tns:J2137Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2126Request">
<wsdl:part name="parameters" element="tns:J2126"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2136Request">
<wsdl:part name="parameters" element="tns:J2136"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2132Response">
<wsdl:part name="parameters" element="tns:J2132Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2221Request">
<wsdl:part name="parameters" element="tns:J2221"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J3335Response">
<wsdl:part name="parameters" element="tns:J3335Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2214Request">
<wsdl:part name="parameters" element="tns:J2214"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2124Request">
<wsdl:part name="parameters" element="tns:J2124"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2142Request">
<wsdl:part name="parameters" element="tns:J2142"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2113Request">
<wsdl:part name="parameters" element="tns:J2113"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2123Request">
<wsdl:part name="parameters" element="tns:J2123"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J3335Request">
<wsdl:part name="parameters" element="tns:J3335"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2321Response">
<wsdl:part name="parameters" element="tns:J2321Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2135Request">
<wsdl:part name="parameters" element="tns:J2135"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2153Response">
<wsdl:part name="parameters" element="tns:J2153Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2174Response">
<wsdl:part name="parameters" element="tns:J2174Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2311Request">
<wsdl:part name="parameters" element="tns:J2311"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2212Request">
<wsdl:part name="parameters" element="tns:J2212"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2134Response">
<wsdl:part name="parameters" element="tns:J2134Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2175Request">
<wsdl:part name="parameters" element="tns:J2175"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2222Response">
<wsdl:part name="parameters" element="tns:J2222Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2126Response">
<wsdl:part name="parameters" element="tns:J2126Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2184Request">
<wsdl:part name="parameters" element="tns:J2184"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2184Response">
<wsdl:part name="parameters" element="tns:J2184Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2311Response">
<wsdl:part name="parameters" element="tns:J2311Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2143Request">
<wsdl:part name="parameters" element="tns:J2143"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2192Request">
<wsdl:part name="parameters" element="tns:J2192"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J21114Request">
<wsdl:part name="parameters" element="tns:J21114"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J21113Request">
<wsdl:part name="parameters" element="tns:J21113"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2113Response">
<wsdl:part name="parameters" element="tns:J2113Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2151Response">
<wsdl:part name="parameters" element="tns:J2151Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2173Request">
<wsdl:part name="parameters" element="tns:J2173"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2128Request">
<wsdl:part name="parameters" element="tns:J2128"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2164Response">
<wsdl:part name="parameters" element="tns:J2164Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2164Request">
<wsdl:part name="parameters" element="tns:J2164"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2141Request">
<wsdl:part name="parameters" element="tns:J2141"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2183Response">
<wsdl:part name="parameters" element="tns:J2183Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2111Request">
<wsdl:part name="parameters" element="tns:J2111"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J21101Response">
<wsdl:part name="parameters" element="tns:J21101Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2135Response">
<wsdl:part name="parameters" element="tns:J2135Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2131Request">
<wsdl:part name="parameters" element="tns:J2131"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2161Request">
<wsdl:part name="parameters" element="tns:J2161"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2125Response">
<wsdl:part name="parameters" element="tns:J2125Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2171Response">
<wsdl:part name="parameters" element="tns:J2171Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2114Request">
<wsdl:part name="parameters" element="tns:J2114"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2127Request">
<wsdl:part name="parameters" element="tns:J2127"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2122Response">
<wsdl:part name="parameters" element="tns:J2122Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J21112Request">
<wsdl:part name="parameters" element="tns:J21112"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2127Response">
<wsdl:part name="parameters" element="tns:J2127Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J21101Request">
<wsdl:part name="parameters" element="tns:J21101"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2172Request">
<wsdl:part name="parameters" element="tns:J2172"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2182Request">
<wsdl:part name="parameters" element="tns:J2182"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2191Response">
<wsdl:part name="parameters" element="tns:J2191Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2114Response">
<wsdl:part name="parameters" element="tns:J2114Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2137Request">
<wsdl:part name="parameters" element="tns:J2137"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2162Response">
<wsdl:part name="parameters" element="tns:J2162Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2165Response">
<wsdl:part name="parameters" element="tns:J2165Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J21112Response">
<wsdl:part name="parameters" element="tns:J21112Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="GetServerTimeResponse">
<wsdl:part name="parameters" element="tns:GetServerTimeResponse"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J21114Response">
<wsdl:part name="parameters" element="tns:J21114Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2214Response">
<wsdl:part name="parameters" element="tns:J2214Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2191Request">
<wsdl:part name="parameters" element="tns:J2191"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2128Response">
<wsdl:part name="parameters" element="tns:J2128Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2133Request">
<wsdl:part name="parameters" element="tns:J2133"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J21113Response">
<wsdl:part name="parameters" element="tns:J21113Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2183Request">
<wsdl:part name="parameters" element="tns:J2183"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J21102Response">
<wsdl:part name="parameters" element="tns:J21102Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2124Response">
<wsdl:part name="parameters" element="tns:J2124Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2213Response">
<wsdl:part name="parameters" element="tns:J2213Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="GetServerTimeRequest">
<wsdl:part name="parameters" element="tns:GetServerTime"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2151Request">
<wsdl:part name="parameters" element="tns:J2151"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2222Request">
<wsdl:part name="parameters" element="tns:J2222"> </wsdl:part>
</wsdl:message>
<wsdl:message name="FsjkWxJobRequest">
<wsdl:part name="parameters" element="tns:FsjkWxJob"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2172Response">
<wsdl:part name="parameters" element="tns:J2172Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2123Response">
<wsdl:part name="parameters" element="tns:J2123Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2136Response">
<wsdl:part name="parameters" element="tns:J2136Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2192Response">
<wsdl:part name="parameters" element="tns:J2192Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2121Response">
<wsdl:part name="parameters" element="tns:J2121Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2134Request">
<wsdl:part name="parameters" element="tns:J2134"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2211Request">
<wsdl:part name="parameters" element="tns:J2211"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2163Request">
<wsdl:part name="parameters" element="tns:J2163"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2171Request">
<wsdl:part name="parameters" element="tns:J2171"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2133Response">
<wsdl:part name="parameters" element="tns:J2133Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2143Response">
<wsdl:part name="parameters" element="tns:J2143Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2181Response">
<wsdl:part name="parameters" element="tns:J2181Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2174Request">
<wsdl:part name="parameters" element="tns:J2174"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2153Request">
<wsdl:part name="parameters" element="tns:J2153"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J21111Request">
<wsdl:part name="parameters" element="tns:J21111"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2213Request">
<wsdl:part name="parameters" element="tns:J2213"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2221Response">
<wsdl:part name="parameters" element="tns:J2221Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2131Response">
<wsdl:part name="parameters" element="tns:J2131Response"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J2162Request">
<wsdl:part name="parameters" element="tns:J2162"> </wsdl:part>
</wsdl:message>
<wsdl:message name="J21111Response">
<wsdl:part name="parameters" element="tns:J21111Response"> </wsdl:part>
</wsdl:message>
<wsdl:portType name="ICommonFsJkServicePortType">
<wsdl:operation name="J2165">
<wsdl:input name="J2165Request" message="tns:J2165Request"> </wsdl:input>
<wsdl:output name="J2165Response" message="tns:J2165Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2311">
<wsdl:input name="J2311Request" message="tns:J2311Request"> </wsdl:input>
<wsdl:output name="J2311Response" message="tns:J2311Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2126">
<wsdl:input name="J2126Request" message="tns:J2126Request"> </wsdl:input>
<wsdl:output name="J2126Response" message="tns:J2126Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2161">
<wsdl:input name="J2161Request" message="tns:J2161Request"> </wsdl:input>
<wsdl:output name="J2161Response" message="tns:J2161Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2125">
<wsdl:input name="J2125Request" message="tns:J2125Request"> </wsdl:input>
<wsdl:output name="J2125Response" message="tns:J2125Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2162">
<wsdl:input name="J2162Request" message="tns:J2162Request"> </wsdl:input>
<wsdl:output name="J2162Response" message="tns:J2162Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2128">
<wsdl:input name="J2128Request" message="tns:J2128Request"> </wsdl:input>
<wsdl:output name="J2128Response" message="tns:J2128Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2163">
<wsdl:input name="J2163Request" message="tns:J2163Request"> </wsdl:input>
<wsdl:output name="J2163Response" message="tns:J2163Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2127">
<wsdl:input name="J2127Request" message="tns:J2127Request"> </wsdl:input>
<wsdl:output name="J2127Response" message="tns:J2127Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2164">
<wsdl:input name="J2164Request" message="tns:J2164Request"> </wsdl:input>
<wsdl:output name="J2164Response" message="tns:J2164Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2122">
<wsdl:input name="J2122Request" message="tns:J2122Request"> </wsdl:input>
<wsdl:output name="J2122Response" message="tns:J2122Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2121">
<wsdl:input name="J2121Request" message="tns:J2121Request"> </wsdl:input>
<wsdl:output name="J2121Response" message="tns:J2121Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2124">
<wsdl:input name="J2124Request" message="tns:J2124Request"> </wsdl:input>
<wsdl:output name="J2124Response" message="tns:J2124Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2123">
<wsdl:input name="J2123Request" message="tns:J2123Request"> </wsdl:input>
<wsdl:output name="J2123Response" message="tns:J2123Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2211">
<wsdl:input name="J2211Request" message="tns:J2211Request"> </wsdl:input>
<wsdl:output name="J2211Response" message="tns:J2211Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2212">
<wsdl:input name="J2212Request" message="tns:J2212Request"> </wsdl:input>
<wsdl:output name="J2212Response" message="tns:J2212Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2213">
<wsdl:input name="J2213Request" message="tns:J2213Request"> </wsdl:input>
<wsdl:output name="J2213Response" message="tns:J2213Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2214">
<wsdl:input name="J2214Request" message="tns:J2214Request"> </wsdl:input>
<wsdl:output name="J2214Response" message="tns:J2214Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2114">
<wsdl:input name="J2114Request" message="tns:J2114Request"> </wsdl:input>
<wsdl:output name="J2114Response" message="tns:J2114Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2111">
<wsdl:input name="J2111Request" message="tns:J2111Request"> </wsdl:input>
<wsdl:output name="J2111Response" message="tns:J2111Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2112">
<wsdl:input name="J2112Request" message="tns:J2112Request"> </wsdl:input>
<wsdl:output name="J2112Response" message="tns:J2112Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2113">
<wsdl:input name="J2113Request" message="tns:J2113Request"> </wsdl:input>
<wsdl:output name="J2113Response" message="tns:J2113Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2131">
<wsdl:input name="J2131Request" message="tns:J2131Request"> </wsdl:input>
<wsdl:output name="J2131Response" message="tns:J2131Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2174">
<wsdl:input name="J2174Request" message="tns:J2174Request"> </wsdl:input>
<wsdl:output name="J2174Response" message="tns:J2174Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2175">
<wsdl:input name="J2175Request" message="tns:J2175Request"> </wsdl:input>
<wsdl:output name="J2175Response" message="tns:J2175Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2137">
<wsdl:input name="J2137Request" message="tns:J2137Request"> </wsdl:input>
<wsdl:output name="J2137Response" message="tns:J2137Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2172">
<wsdl:input name="J2172Request" message="tns:J2172Request"> </wsdl:input>
<wsdl:output name="J2172Response" message="tns:J2172Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2136">
<wsdl:input name="J2136Request" message="tns:J2136Request"> </wsdl:input>
<wsdl:output name="J2136Response" message="tns:J2136Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2173">
<wsdl:input name="J2173Request" message="tns:J2173Request"> </wsdl:input>
<wsdl:output name="J2173Response" message="tns:J2173Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2135">
<wsdl:input name="J2135Request" message="tns:J2135Request"> </wsdl:input>
<wsdl:output name="J2135Response" message="tns:J2135Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2134">
<wsdl:input name="J2134Request" message="tns:J2134Request"> </wsdl:input>
<wsdl:output name="J2134Response" message="tns:J2134Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2171">
<wsdl:input name="J2171Request" message="tns:J2171Request"> </wsdl:input>
<wsdl:output name="J2171Response" message="tns:J2171Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2133">
<wsdl:input name="J2133Request" message="tns:J2133Request"> </wsdl:input>
<wsdl:output name="J2133Response" message="tns:J2133Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2132">
<wsdl:input name="J2132Request" message="tns:J2132Request"> </wsdl:input>
<wsdl:output name="J2132Response" message="tns:J2132Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="FsjkWxJob">
<wsdl:input name="FsjkWxJobRequest" message="tns:FsjkWxJobRequest"> </wsdl:input>
<wsdl:output name="FsjkWxJobResponse" message="tns:FsjkWxJobResponse"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2142">
<wsdl:input name="J2142Request" message="tns:J2142Request"> </wsdl:input>
<wsdl:output name="J2142Response" message="tns:J2142Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2141">
<wsdl:input name="J2141Request" message="tns:J2141Request"> </wsdl:input>
<wsdl:output name="J2141Response" message="tns:J2141Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J3335">
<wsdl:input name="J3335Request" message="tns:J3335Request"> </wsdl:input>
<wsdl:output name="J3335Response" message="tns:J3335Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2143">
<wsdl:input name="J2143Request" message="tns:J2143Request"> </wsdl:input>
<wsdl:output name="J2143Response" message="tns:J2143Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2181">
<wsdl:input name="J2181Request" message="tns:J2181Request"> </wsdl:input>
<wsdl:output name="J2181Response" message="tns:J2181Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2182">
<wsdl:input name="J2182Request" message="tns:J2182Request"> </wsdl:input>
<wsdl:output name="J2182Response" message="tns:J2182Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2183">
<wsdl:input name="J2183Request" message="tns:J2183Request"> </wsdl:input>
<wsdl:output name="J2183Response" message="tns:J2183Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2184">
<wsdl:input name="J2184Request" message="tns:J2184Request"> </wsdl:input>
<wsdl:output name="J2184Response" message="tns:J2184Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J21113">
<wsdl:input name="J21113Request" message="tns:J21113Request"> </wsdl:input>
<wsdl:output name="J21113Response" message="tns:J21113Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J21114">
<wsdl:input name="J21114Request" message="tns:J21114Request"> </wsdl:input>
<wsdl:output name="J21114Response" message="tns:J21114Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J21111">
<wsdl:input name="J21111Request" message="tns:J21111Request"> </wsdl:input>
<wsdl:output name="J21111Response" message="tns:J21111Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J21112">
<wsdl:input name="J21112Request" message="tns:J21112Request"> </wsdl:input>
<wsdl:output name="J21112Response" message="tns:J21112Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2153">
<wsdl:input name="J2153Request" message="tns:J2153Request"> </wsdl:input>
<wsdl:output name="J2153Response" message="tns:J2153Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2152">
<wsdl:input name="J2152Request" message="tns:J2152Request"> </wsdl:input>
<wsdl:output name="J2152Response" message="tns:J2152Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2151">
<wsdl:input name="J2151Request" message="tns:J2151Request"> </wsdl:input>
<wsdl:output name="J2151Response" message="tns:J2151Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2321">
<wsdl:input name="J2321Request" message="tns:J2321Request"> </wsdl:input>
<wsdl:output name="J2321Response" message="tns:J2321Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2192">
<wsdl:input name="J2192Request" message="tns:J2192Request"> </wsdl:input>
<wsdl:output name="J2192Response" message="tns:J2192Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2191">
<wsdl:input name="J2191Request" message="tns:J2191Request"> </wsdl:input>
<wsdl:output name="J2191Response" message="tns:J2191Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J21101">
<wsdl:input name="J21101Request" message="tns:J21101Request"> </wsdl:input>
<wsdl:output name="J21101Response" message="tns:J21101Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J21102">
<wsdl:input name="J21102Request" message="tns:J21102Request"> </wsdl:input>
<wsdl:output name="J21102Response" message="tns:J21102Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="GetServerTime">
<wsdl:input name="GetServerTimeRequest" message="tns:GetServerTimeRequest"> </wsdl:input>
<wsdl:output name="GetServerTimeResponse" message="tns:GetServerTimeResponse"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2222">
<wsdl:input name="J2222Request" message="tns:J2222Request"> </wsdl:input>
<wsdl:output name="J2222Response" message="tns:J2222Response"> </wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2221">
<wsdl:input name="J2221Request" message="tns:J2221Request"> </wsdl:input>
<wsdl:output name="J2221Response" message="tns:J2221Response"> </wsdl:output>
</wsdl:operation>
</wsdl:portType>
<wsdl:binding name="ICommonFsJkServiceHttpBinding" type="tns:ICommonFsJkServicePortType">
<wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
<wsdl:operation name="J2165">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2165Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2165Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2311">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2311Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2311Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2126">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2126Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2126Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2161">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2161Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2161Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2125">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2125Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2125Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2162">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2162Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2162Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2128">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2128Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2128Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2163">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2163Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2163Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2127">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2127Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2127Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2164">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2164Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2164Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2122">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2122Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2122Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2121">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2121Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2121Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2124">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2124Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2124Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2123">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2123Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2123Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2211">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2211Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2211Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2212">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2212Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2212Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2213">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2213Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2213Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2214">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2214Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2214Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2114">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2114Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2114Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2111">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2111Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2111Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2112">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2112Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2112Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2113">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2113Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2113Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2131">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2131Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2131Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2174">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2174Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2174Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2175">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2175Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2175Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2137">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2137Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2137Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2172">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2172Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2172Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2136">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2136Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2136Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2173">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2173Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2173Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2135">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2135Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2135Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2134">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2134Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2134Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2171">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2171Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2171Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2133">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2133Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2133Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2132">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2132Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2132Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="FsjkWxJob">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="FsjkWxJobRequest">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="FsjkWxJobResponse">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2142">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2142Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2142Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2141">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2141Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2141Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J3335">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J3335Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J3335Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2143">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2143Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2143Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2181">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2181Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2181Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2182">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2182Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2182Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2183">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2183Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2183Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2184">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2184Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2184Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J21113">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J21113Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J21113Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J21114">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J21114Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J21114Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J21111">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J21111Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J21111Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J21112">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J21112Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J21112Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2153">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2153Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2153Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2152">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2152Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2152Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2151">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2151Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2151Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2321">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2321Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2321Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2192">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2192Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2192Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2191">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2191Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2191Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J21101">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J21101Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J21101Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J21102">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J21102Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J21102Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="GetServerTime">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="GetServerTimeRequest">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="GetServerTimeResponse">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2222">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2222Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2222Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="J2221">
<wsdlsoap:operation soapAction=""/>
<wsdl:input name="J2221Request">
<wsdlsoap:body use="literal"/>
</wsdl:input>
<wsdl:output name="J2221Response">
<wsdlsoap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
</wsdl:binding>
<wsdl:service name="ICommonFsJkService">
<wsdl:port name="ICommonFsJkServiceHttpPort" binding="tns:ICommonFsJkServiceHttpBinding">
<wsdlsoap:address location="https://www.sdljhospital.com/QAZ/services/ICommonFsJkService"/>
</wsdl:port>
</wsdl:service>
</wsdl:definitions>