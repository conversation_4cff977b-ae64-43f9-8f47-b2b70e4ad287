package com.foshan.form.hospital.response;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.hospital.DoctorForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="患者信息查询返回对象(GetPatientRes)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetPatientRes extends BasePageResponse {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 9138999966037595174L;
	@ApiModelProperty(value = "姓名")
	private String patName;
	@ApiModelProperty(value = "性别 M：男，F：女")
	private String patSex;
	@ApiModelProperty(value = "年龄")
	private String patAge;
	@ApiModelProperty(value = "出生日期")
	private String patBirth;
	@ApiModelProperty(value = "地址")
	private String patAddress;
	@ApiModelProperty(value = "电话")
	private String patMobile;
	@ApiModelProperty(value = "证件类型 1：二代身份证2：港澳居民身份证3：台湾居民身份证4：护照")
	private String patIdType;
	@ApiModelProperty(value = "证件号码")
	private String patIdNo;
	@ApiModelProperty(value = "诊疗卡类型 1：院内诊疗卡2：社保卡3：医保卡4：佛山健康卡")
	private String patCardType;
	@ApiModelProperty(value = "诊疗卡号码")
	private String patCardNo;

}
