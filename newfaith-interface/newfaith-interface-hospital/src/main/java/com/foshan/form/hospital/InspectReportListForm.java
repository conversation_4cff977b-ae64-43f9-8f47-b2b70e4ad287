package com.foshan.form.hospital;

import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="检验报告列表查询返回对象(InspectReportListForm)")
public class InspectReportListForm implements IForm{

	/**
	 * 检验报告列表查询返回对象
	 */
	private static final long serialVersionUID = -8992899145308754627L;


	@ApiModelProperty(value = "检查ID")
	private String inspectId;
	@ApiModelProperty(value = "检查名称")
	private String inspectName;
	@ApiModelProperty(value = "检查时间 格式：YYYY-MM-DD 24HH:MI:SS")
	private String inspectTime;
	@ApiModelProperty(value = "检查状态 1-未出报告 2-已出报告，未审核 3-已出报告，已审核")
	private String inspectStatus;
	@ApiModelProperty(value = "报告时间 格式：YYYY-MM-DD 24HH:MI:SS")
	private String reportTime;
	@ApiModelProperty(value = "审核时间 格式：YYYY-MM-DD 24HH:MI:SS")
	private String examTime;
	@ApiModelProperty(value = "送检科室代码")
	private String deptCode;
	@ApiModelProperty(value = "送检科室")
	private String deptName;
	@ApiModelProperty(value = "送检医生代码")
	private String doctorCode;
	@ApiModelProperty(value = "送检医生")
	private String doctorName;
	@ApiModelProperty(value = "执行科室代码")
	private String exeDeptCode;
	@ApiModelProperty(value = "执行科室")
	private String exeDeptName;
	@ApiModelProperty(value = "报告人")
	private String reporter;
	@ApiModelProperty(value = "审核人")
	private String auditor;

	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
	

}
