package com.foshan.form.hospital.request;

import com.foshan.form.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="公卫接口请求对象(PublicHealthRequest)")
public class PublicHealthRequest extends BaseRequest {
	private static final long serialVersionUID = -5455009566349293136L;
	/**
	 * 
	 */
	@ApiModelProperty(value = "年份", example = "2023")
	private String year;
	@ApiModelProperty(value = "身份证")
    private String idCard;
	@ApiModelProperty(value = "手机号")
	private String phone;

}
