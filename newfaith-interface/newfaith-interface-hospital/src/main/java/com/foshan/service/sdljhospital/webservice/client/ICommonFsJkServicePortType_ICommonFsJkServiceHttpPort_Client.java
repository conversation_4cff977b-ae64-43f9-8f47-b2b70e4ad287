
package com.foshan.service.sdljhospital.webservice.client;

import java.io.ByteArrayInputStream;

/**
 * Please modify this class to meet your needs
 * This class is not complete
 */

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.namespace.QName;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import com.foshan.util.DigestUtil;
import com.foshan.util.XmlUtil;

/**
 * This class was generated by Apache CXF 3.5.5
 * 2023-02-20T17:32:12.204+08:00
 * Generated source version: 3.5.5
 *
 */
public final class ICommonFsJkServicePortType_ICommonFsJkServiceHttpPort_Client {

    private static final QName SERVICE_NAME = new QName("CommonFsJkServiceImp", "ICommonFsJkService");

    private ICommonFsJkServicePortType_ICommonFsJkServiceHttpPort_Client() {
    }
    
    
    
	public static  Map<String, Object> xmlToMap(String strXML)
			throws ParserConfigurationException, UnsupportedEncodingException, SAXException, IOException {
		 Map<String,Object> data = new HashMap<>();
		DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
		DocumentBuilder documentBuilder = documentBuilderFactory.newDocumentBuilder();
		InputStream stream = new ByteArrayInputStream(strXML.getBytes("UTF-8"));
		org.w3c.dom.Document doc = documentBuilder.parse(stream);
		doc.getDocumentElement().normalize();
		NodeList nodeList = doc.getDocumentElement().getChildNodes();
		for (int idx = 0; idx < nodeList.getLength(); ++idx) {
		    Node node = nodeList.item(idx);
		    NodeList childNodesList = node.getChildNodes();
		    if (node.getNodeType() == Node.ELEMENT_NODE ) {
		        org.w3c.dom.Element element = (org.w3c.dom.Element) node;
		        if(null!=element.getChildNodes() && element.getChildNodes().getLength()>0) {
		        	getChildNodes(childNodesList,data);
		        }else {  
		        	data.put(element.getNodeName(), element.getTextContent());
		        }
		    }else if(null!=childNodesList && childNodesList.getLength()>0) {
		    	getChildNodes(childNodesList,data);
		    }
		}
		try {
		    stream.close();
		} catch (Exception ex) {
		    // do nothing
		}
		return data;
	}
	
	public static void getChildNodes(NodeList nodeList,Map<String,Object> data) {
		List<Map<String,Object>> dataList = null;
		Map<String,Object>  childMap = new HashMap<>();
		boolean state =false;
		for (int idx = 0; idx < nodeList.getLength(); ++idx) {
		    Node node = nodeList.item(idx);
		    if(node.getParentNode().getNodeName().equals("item")) {
		    	state = true;
		    	if(data.containsKey("item")){
		    		dataList = (List<Map<String, Object>>) data.get("item");
		    	}else {
		    		dataList = new ArrayList<>();
		    	}
			    if (node.getNodeType() == Node.ELEMENT_NODE ) {
			        org.w3c.dom.Element element = (org.w3c.dom.Element) node;
			        if(null!=element.getChildNodes() && element.getChildNodes().getLength()>0) {
			        	getChildNodes(element.getChildNodes(),childMap);
			        }else {
			        	childMap.put(element.getNodeName(), element.getTextContent());
			        }
			    }else{
			    	childMap.put(node.getParentNode().getNodeName(), node.getTextContent());
			    }
		    }else {
			    if (node.getNodeType() == Node.ELEMENT_NODE ) {
			        org.w3c.dom.Element element = (org.w3c.dom.Element) node;
			        if(null!=element.getChildNodes() && element.getChildNodes().getLength()>0) {
			        	getChildNodes(element.getChildNodes(),data);
			        }else {
			        	data.put(element.getNodeName(), element.getTextContent());
			        }
			    }else{
			    	data.put(node.getParentNode().getNodeName(), node.getTextContent());
			    }
		    }
		}
		if(state) {
	    	dataList.add(childMap);
	    	data.put("item", dataList);
		}
	}
	


    public static void main(String args[]) throws java.lang.Exception {
        URL wsdlURL = ICommonFsJkService.WSDL_LOCATION;
        if (args.length > 0 && args[0] != null && !"".equals(args[0])) {
            File wsdlFile = new File(args[0]);
            try {
                if (wsdlFile.exists()) {
                    wsdlURL = wsdlFile.toURI().toURL();
                } else {
                    wsdlURL = new URL(args[0]);
                }
            } catch (MalformedURLException e) {
                e.printStackTrace();
            }
        }

        ICommonFsJkService ss = new ICommonFsJkService(wsdlURL, SERVICE_NAME);
        ICommonFsJkServicePortType port = ss.getICommonFsJkServiceHttpPort();

        String partnerId = "SDQLJYY";
        String timeStamp = System.currentTimeMillis()+"";
        
        String secretKey = "7cf75a31-d1c0-424f-a1f7-b34ee2f80a2a";
        String password = DigestUtil.getMD5Str(partnerId+secretKey+timeStamp,"");
//        System.out.println(timeStamp);
//        java.lang.String _j21113_in0 ="<Request><serviceCode>OutpatientPaymentNotice</serviceCode><partnerId>"+partnerId+"</partnerId>"
//        		+ "<timeStamp>"+timeStamp+"</timeStamp><password>"+password+"</password>"
//        		+ "<deptCode></deptCode><doctorCode></doctorCode></Request>";
//        String s = port.j2123(_j21113_in0);
//        Map<String,Object> map = new HashMap<>();
//        map=xmlToMap(s);
//        System.out.println(s);
        
//        {
//        System.out.println("Invoking j21113...");
//        java.lang.String _j21113_in0 = "";
//        java.lang.String _j21113__return = port.j21113(_j21113_in0);
//        System.out.println("j21113.result=" + _j21113__return);
//
//
//        }
//        {
//        System.out.println("Invoking j21114...");
//        java.lang.String _j21114_in0 = "";
//        java.lang.String _j21114__return = port.j21114(_j21114_in0);
//        System.out.println("j21114.result=" + _j21114__return);
//
//
//        }
//        {
//        System.out.println("Invoking j21111...");
//        java.lang.String _j21111_in0 = "";
//        java.lang.String _j21111__return = port.j21111(_j21111_in0);
//        System.out.println("j21111.result=" + _j21111__return);
//
//
//        }
//        {
//        System.out.println("Invoking j21112...");
//        java.lang.String _j21112_in0 = "";
//        java.lang.String _j21112__return = port.j21112(_j21112_in0);
//        System.out.println("j21112.result=" + _j21112__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2181...");
//        java.lang.String _j2181_in0 = "";
//        java.lang.String _j2181__return = port.j2181(_j2181_in0);
//        System.out.println("j2181.result=" + _j2181__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2182...");
//        java.lang.String _j2182_in0 = "";
//        java.lang.String _j2182__return = port.j2182(_j2182_in0);
//        System.out.println("j2182.result=" + _j2182__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2141...");
//        java.lang.String _j2141_in0 = "";
//        java.lang.String _j2141__return = port.j2141(_j2141_in0);
//        System.out.println("j2141.result=" + _j2141__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2142...");
//        java.lang.String _j2142_in0 = "";
//        java.lang.String _j2142__return = port.j2142(_j2142_in0);
//        System.out.println("j2142.result=" + _j2142__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2183...");
//        java.lang.String _j2183_in0 = "";
//        java.lang.String _j2183__return = port.j2183(_j2183_in0);
//        System.out.println("j2183.result=" + _j2183__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2184...");
//        java.lang.String _j2184_in0 = "";
//        java.lang.String _j2184__return = port.j2184(_j2184_in0);
//        System.out.println("j2184.result=" + _j2184__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2222...");
//        java.lang.String _j2222_in0 = "";
//        java.lang.String _j2222__return = port.j2222(_j2222_in0);
//        System.out.println("j2222.result=" + _j2222__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2143...");
//        java.lang.String _j2143_in0 = "";
//        java.lang.String _j2143__return = port.j2143(_j2143_in0);
//        System.out.println("j2143.result=" + _j2143__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2221...");
//        java.lang.String _j2221_in0 = "";
//        java.lang.String _j2221__return = port.j2221(_j2221_in0);
//        System.out.println("j2221.result=" + _j2221__return);
//
//
//        }
//        {
//        System.out.println("Invoking j21102...");
//        java.lang.String _j21102_in0 = "";
//        java.lang.String _j21102__return = port.j21102(_j21102_in0);
//        System.out.println("j21102.result=" + _j21102__return);
//
//
//        }
//        {
//        System.out.println("Invoking j21101...");
//        java.lang.String _j21101_in0 = "";
//        java.lang.String _j21101__return = port.j21101(_j21101_in0);
//        System.out.println("j21101.result=" + _j21101__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2192...");
//        java.lang.String _j2192_in0 = "";
//        java.lang.String _j2192__return = port.j2192(_j2192_in0);
//        System.out.println("j2192.result=" + _j2192__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2191...");
//        java.lang.String _j2191_in0 = "";
//        java.lang.String _j2191__return = port.j2191(_j2191_in0);
//        System.out.println("j2191.result=" + _j2191__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2152...");
//        java.lang.String _j2152_in0 = "";
//        java.lang.String _j2152__return = port.j2152(_j2152_in0);
//        System.out.println("j2152.result=" + _j2152__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2153...");
//        java.lang.String _j2153_in0 = "";
//        java.lang.String _j2153__return = port.j2153(_j2153_in0);
//        System.out.println("j2153.result=" + _j2153__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2151...");
//        java.lang.String _j2151_in0 = "";
//        java.lang.String _j2151__return = port.j2151(_j2151_in0);
//        System.out.println("j2151.result=" + _j2151__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2112...");
//        java.lang.String _j2112_in0 = "";
//        java.lang.String _j2112__return = port.j2112(_j2112_in0);
//        System.out.println("j2112.result=" + _j2112__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2113...");
//        java.lang.String _j2113_in0 = "";
//        java.lang.String _j2113__return = port.j2113(_j2113_in0);
//        System.out.println("j2113.result=" + _j2113__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2311...");
//        java.lang.String _j2311_in0 = "";
//        java.lang.String _j2311__return = port.j2311(_j2311_in0);
//        System.out.println("j2311.result=" + _j2311__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2111...");
//        java.lang.String _j2111_in0 = "";
//        java.lang.String _j2111__return = port.j2111(_j2111_in0);
//        System.out.println("j2111.result=" + _j2111__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2114...");
//        java.lang.String _j2114_in0 = "";
//        java.lang.String _j2114__return = port.j2114(_j2114_in0);
//        System.out.println("j2114.result=" + _j2114__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2163...");
//        java.lang.String _j2163_in0 = "";
//        java.lang.String _j2163__return = port.j2163(_j2163_in0);
//        System.out.println("j2163.result=" + _j2163__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2164...");
//        java.lang.String _j2164_in0 = "";
//        java.lang.String _j2164__return = port.j2164(_j2164_in0);
//        System.out.println("j2164.result=" + _j2164__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2161...");
//        java.lang.String _j2161_in0 = "";
//        java.lang.String _j2161__return = port.j2161(_j2161_in0);
//        System.out.println("j2161.result=" + _j2161__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2162...");
//        java.lang.String _j2162_in0 = "";
//        java.lang.String _j2162__return = port.j2162(_j2162_in0);
//        System.out.println("j2162.result=" + _j2162__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2123...");
//        java.lang.String _j2123_in0 = "";
//        java.lang.String _j2123__return = port.j2123(_j2123_in0);
//        System.out.println("j2123.result=" + _j2123__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2321...");
//        java.lang.String _j2321_in0 = "";
//        java.lang.String _j2321__return = port.j2321(_j2321_in0);
//        System.out.println("j2321.result=" + _j2321__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2124...");
//        java.lang.String _j2124_in0 = "";
//        java.lang.String _j2124__return = port.j2124(_j2124_in0);
//        System.out.println("j2124.result=" + _j2124__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2121...");
//        java.lang.String _j2121_in0 = "";
//        java.lang.String _j2121__return = port.j2121(_j2121_in0);
//        System.out.println("j2121.result=" + _j2121__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2165...");
//        java.lang.String _j2165_in0 = "";
//        java.lang.String _j2165__return = port.j2165(_j2165_in0);
//        System.out.println("j2165.result=" + _j2165__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2122...");
//        java.lang.String _j2122_in0 = "";
//        java.lang.String _j2122__return = port.j2122(_j2122_in0);
//        System.out.println("j2122.result=" + _j2122__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2127...");
//        java.lang.String _j2127_in0 = "";
//        java.lang.String _j2127__return = port.j2127(_j2127_in0);
//        System.out.println("j2127.result=" + _j2127__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2128...");
//        java.lang.String _j2128_in0 = "";
//        java.lang.String _j2128__return = port.j2128(_j2128_in0);
//        System.out.println("j2128.result=" + _j2128__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2125...");
//        java.lang.String _j2125_in0 = "";
//        java.lang.String _j2125__return = port.j2125(_j2125_in0);
//        System.out.println("j2125.result=" + _j2125__return);
//
//
//        }
//        {
//        System.out.println("Invoking j3335...");
//        java.lang.String _j3335_in0 = "";
//        java.lang.String _j3335__return = port.j3335(_j3335_in0);
//        System.out.println("j3335.result=" + _j3335__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2126...");
//        java.lang.String _j2126_in0 = "";
//        java.lang.String _j2126__return = port.j2126(_j2126_in0);
//        System.out.println("j2126.result=" + _j2126__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2171...");
//        java.lang.String _j2171_in0 = "";
//        java.lang.String _j2171__return = port.j2171(_j2171_in0);
//        System.out.println("j2171.result=" + _j2171__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2174...");
//        java.lang.String _j2174_in0 = "";
//        java.lang.String _j2174__return = port.j2174(_j2174_in0);
//        System.out.println("j2174.result=" + _j2174__return);
//
//
//        }
//        {
        System.out.println("Invoking getServerTime...");
        java.lang.String _getServerTime__return = port.getServerTime();
        System.out.println("getServerTime.result=" + _getServerTime__return);


//        }
//        {
//        System.out.println("Invoking j2175...");
//        java.lang.String _j2175_in0 = "";
//        java.lang.String _j2175__return = port.j2175(_j2175_in0);
//        System.out.println("j2175.result=" + _j2175__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2131...");
//        java.lang.String _j2131_in0 = "";
//        java.lang.String _j2131__return = port.j2131(_j2131_in0);
//        System.out.println("j2131.result=" + _j2131__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2172...");
//        java.lang.String _j2172_in0 = "";
//        java.lang.String _j2172__return = port.j2172(_j2172_in0);
//        System.out.println("j2172.result=" + _j2172__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2173...");
//        java.lang.String _j2173_in0 = "";
//        java.lang.String _j2173__return = port.j2173(_j2173_in0);
//        System.out.println("j2173.result=" + _j2173__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2211...");
//        java.lang.String _j2211_in0 = "";
//        java.lang.String _j2211__return = port.j2211(_j2211_in0);
//        System.out.println("j2211.result=" + _j2211__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2134...");
//        java.lang.String _j2134_in0 = "";
//        java.lang.String _j2134__return = port.j2134(_j2134_in0);
//        System.out.println("j2134.result=" + _j2134__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2212...");
//        java.lang.String _j2212_in0 = "";
//        java.lang.String _j2212__return = port.j2212(_j2212_in0);
//        System.out.println("j2212.result=" + _j2212__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2135...");
//        java.lang.String _j2135_in0 = "<Request><serviceCode>OutpatientPaymentNotice</serviceCode><partnerId>"+partnerId+"</partnerId>"
//        		+ "<timeStamp>"+timeStamp+"</timeStamp><password>"+password+"</password>"
//        		+"<psOrdNum>20230301084909</psOrdNum><deptCode>235</deptCode>"
//        		+ "<doctorCode>672</doctorCode><scheduleDate>2023-03-02</scheduleDate><timeFlag>1</timeFlag>"
//        		+ "<beginTime>08:02</beginTime><endTime>08:05</endTime><regFee></regFee><patType></patType>"
//        		+ "<patName></patName><patSex></patSex><patAge></patAge><patIdType></patIdType>"
//        		+ "<patIdNo></patIdNo><patCardType></patCardType>1<patCardNo>**********</patCardNo>"
//        		+ "<patMobile></patMobile><patAddress></patAddress><guardName></guardName>"
//        		+ "<guardIdType></guardIdType><guardIdNo></guardIdNo></Request>";
//
//        java.lang.String _j2135__return = port.j2135(_j2135_in0);
//        Map<String,Object> map = xmlToMap(_j2135__return);
//        System.out.println("");
//        System.out.println("调用j2135接口，请求参数："+_j2135_in0);
//        
//        System.out.println("");
//        System.out.println("j2135接口返回参数：" + _j2135__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2132...");
//        java.lang.String _j2132_in0 = "";
//        java.lang.String _j2132__return = port.j2132(_j2132_in0);
//        System.out.println("j2132.result=" + _j2132__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2133...");
//        java.lang.String _j2133_in0 = "";
//        java.lang.String _j2133__return = port.j2133(_j2133_in0);
//        System.out.println("j2133.result=" + _j2133__return);
//
//
//        }
//        {
//        System.out.println("Invoking fsjkWxJob...");
//        java.lang.String _fsjkWxJob_in0 = "";
//        java.lang.String _fsjkWxJob__return = port.fsjkWxJob(_fsjkWxJob_in0);
//        System.out.println("fsjkWxJob.result=" + _fsjkWxJob__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2213...");
//        java.lang.String _j2213_in0 = "";
//        java.lang.String _j2213__return = port.j2213(_j2213_in0);
//        System.out.println("j2213.result=" + _j2213__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2136...");
//        java.lang.String _j2136_in0 = "";
//        java.lang.String _j2136__return = port.j2136(_j2136_in0);
//        System.out.println("j2136.result=" + _j2136__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2214...");
//        java.lang.String _j2214_in0 = "";
//        java.lang.String _j2214__return = port.j2214(_j2214_in0);
//        System.out.println("j2214.result=" + _j2214__return);
//
//
//        }
//        {
//        System.out.println("Invoking j2137...");
//        java.lang.String _j2137_in0 = "";
//        java.lang.String _j2137__return = port.j2137(_j2137_in0);
//        System.out.println("j2137.result=" + _j2137__return);
//
//
//        }

        System.exit(0);
    }

}
