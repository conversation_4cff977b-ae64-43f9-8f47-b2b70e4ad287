package com.foshan.entity.hospital;

import java.sql.Timestamp;

import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AccountEntity;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_appointment_order")
@org.hibernate.annotations.Table(appliesTo = "t_appointment_order",comment="预约订单") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class AppointmentOrderEntity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6984671807756880077L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '时间'")
	protected Timestamp createTime;
	@Column(columnDefinition = "varchar(64) comment '订单号'")
	private String orderNum;
	@Column(columnDefinition = "varchar(30) comment '智能卡号'")
	private String smartcardId;
	@Column(columnDefinition = "varchar(3000) comment 'JSON类型的数据'")
	private String dataJson;
	@Column(columnDefinition = "int(1) default 1 comment '状态 0--无效数据  1--有效数据'")
	protected Integer state;
	@Column(columnDefinition = "int(2) default 1 comment '预约状态 0-已取消 1-已预约 '")
	private Integer status;
	@Column(columnDefinition = "varchar(3000) comment 'JSON类型的数据'")
	private String responseJson;
	@Column(columnDefinition = "varchar(1000) comment '取消原因'")
	private String cancelReason;
//	@ManyToOne(targetEntity = AccountEntity.class, fetch = FetchType.LAZY)
//	@JoinColumn(name = "accountId", referencedColumnName = "id", nullable = false)
//	@JsonIgnore
//	private AccountEntity account;

}
