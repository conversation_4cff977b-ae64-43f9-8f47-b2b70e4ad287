package com.foshan.form.kingdee.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="基类分页请求对象(BasePageRequest)")
public class BasePageRequest extends BaseRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = 3781728997876065661L;
	@ApiModelProperty(value = "请求页码",example="1")
	public Integer requestPage=1;
	@ApiModelProperty(value = "页面大小",example="1")
	public Integer pageSize=10;


}
