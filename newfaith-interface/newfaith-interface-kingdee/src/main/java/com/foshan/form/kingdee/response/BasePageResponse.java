package com.foshan.form.kingdee.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="基本分页返回对象(BasePageResponse)")
@JsonInclude(Include.NON_NULL)
public  class BasePageResponse extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 7037857497681593312L;
	@ApiModelProperty(value = "当前页码",example="1")
	protected int currentPage;
	@ApiModelProperty(value = "页面大小",example="1")
	protected int pageSize;
	@ApiModelProperty(value = "总页数",example="1")
	protected int total;
	@ApiModelProperty(value = "总记录条数",example="1")
	protected int totalResult;

	public BasePageResponse(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public BasePageResponse(int currentPage, int pageSize, int total, int totalResult) {
		super();
		this.currentPage = currentPage;
		this.pageSize = pageSize;
		this.total = total;
		this.totalResult = totalResult;
	}

	

	@Override
	public String toString() {
		return "BasePageResponse [currentPage=" + currentPage + ", pageSize=" + pageSize + ", total=" + total
				+ ", totalResult=" + totalResult + "]";
	}

}
