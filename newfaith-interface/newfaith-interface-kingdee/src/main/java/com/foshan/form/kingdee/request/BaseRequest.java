package com.foshan.form.kingdee.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="基类请求对象(BaseRequest)")
public abstract class BaseRequest implements IRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = 5692765103265238671L;
	@ApiModelProperty(value = "智能卡号",hidden=true)
	protected String smartcardId;
	@ApiModelProperty(value = "用户编码",hidden=true)
	protected String userCode;
	@ApiModelProperty(value = "机顶盒区域码",hidden=true)
	protected String regionCode;
	@ApiModelProperty(value = "IP地址",hidden=true)
	protected String ipAddr;
	@ApiModelProperty(value = "电话",hidden=true)
	protected String phone;
	@ApiModelProperty(value = "MAC地址",hidden=true)
	protected String macAddr;

	

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
