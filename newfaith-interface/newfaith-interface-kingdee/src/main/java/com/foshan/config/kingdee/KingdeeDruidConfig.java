package com.foshan.config.kingdee;


import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
public class KingdeeDruidConfig {

	@Bean(name = "kingdeeDataSource")
	@ConfigurationProperties(prefix = "spring.datasource.druid.kingdee")
	public DruidDataSource kingdeeDataSource() {
		log.info("kingdeeDataSource构建完成！！！");
		return DruidDataSourceBuilder.create().build();
	}
}
