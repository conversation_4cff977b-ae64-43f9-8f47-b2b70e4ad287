package com.foshan.config.kingdee;

import java.util.Properties;

import javax.annotation.Resource;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.orm.hibernate5.LocalSessionFactoryBean;


@Configuration
public class KingdeeHibernateConfig {
    
	@Value("${spring.jpa.properties.hibernate.current_session_context_class}")
    public String current_session_context_class;
	
	@Value("${spring.jpa.hibernate.ddl-auto}")
    public String hibernate_ddl_auto;
    
	@Resource(name = "kingdeeDataSource")
    private DataSource kingdeeDataSource;
	
    
	@Primary
    @Bean(name="kingdeeSessionFactory")
    public LocalSessionFactoryBean kingdeeSessionFactory() {
       LocalSessionFactoryBean sessionFactory = new LocalSessionFactoryBean();
       sessionFactory.setDataSource(kingdeeDataSource);
       sessionFactory.setPackagesToScan("com.foshan.entity");
       sessionFactory.setHibernateProperties(getDBProperties());
       return sessionFactory;
    }

    
    private Properties getDBProperties() {
        Properties properties = new Properties();
        properties.setProperty("hibernate.current_session_context_class", current_session_context_class);
        properties.setProperty("hibernate.hbm2ddl.auto",hibernate_ddl_auto);
        return properties;
    }
    
 
}
