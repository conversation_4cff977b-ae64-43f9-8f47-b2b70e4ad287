package com.foshan.form.gzlmzn;

import com.foshan.form.IForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class GzlmznTokenForm implements IForm {

	/**
	 * 广州老米token对象
	 */
	private static final long serialVersionUID = -6591535467220992395L;
	private String token;

	@Override
	public int compareTo(Object arg0) {
		// TODO Auto-generated method stub
		return 0;
	}

}
