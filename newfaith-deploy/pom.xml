<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.foshan</groupId>
		<artifactId>newfaith</artifactId>
		<version>V200R001B03</version>
	</parent>

	<artifactId>newfaith-deploy</artifactId>
	<packaging>pom</packaging>

	<modules>
	<!-- 
		<module>newfaith-deploy-base</module>
		<module>newfaith-deploy-bidding</module>
		<module>newfaith-deploy-community</module>
		<module>newfaith-deploy-datatrans</module>
		<module>newfaith-deploy-education</module>
		<module>newfaith-deploy-gzlmzn</module>
		<module>newfaith-deploy-health</module>
		<module>newfaith-deploy-kingdee</module>
		<module>newfaith-deploy-party</module>
		<module>newfaith-deploy-shop</module>
		<module>newfaith-deploy-sms</module>
		<module>newfaith-deploy-token</module>
		<module>newfaith-deploy-vote</module>
    -->
<!--		<module>newfaith-deploy-education</module>-->
<!--		<module>newfaith-deploy-community</module>-->
<!--		<module>newfaith-deploy-kingdee</module>-->
<!--		<module>newfaith-deploy-shop</module>-->
<!--		<module>newfaith-deploy-vote</module>-->
<!--		<module>newfaith-deploy-health</module>-->

		<module>newfaith-deploy-cultureCloud</module>
<!--        		<module>newfaith-deploy-permission</module>-->
<!--		  <module>newfaith-deploy-businessOpportunity</module>-->


<!--				<module>newfaith-deploy-generalApi</module>-->
	</modules>

</project>