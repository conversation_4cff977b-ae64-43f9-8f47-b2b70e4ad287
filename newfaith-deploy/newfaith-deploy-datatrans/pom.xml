<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.foshan</groupId>
		<artifactId>newfaith-deploy</artifactId>
		<version>V200R001B03</version>
	</parent>
	<artifactId>newfaith-deploy-datatrans</artifactId>
	<dependencies>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>newfaith-exp-datatrans</artifactId>
			<version>${project.version}</version>
		</dependency>
		
	</dependencies>

	<build>
		<finalName>datatrans</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>
</project>