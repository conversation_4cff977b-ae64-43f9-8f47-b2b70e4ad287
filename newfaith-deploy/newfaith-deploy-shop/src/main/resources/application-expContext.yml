shop: 
  #各种SN的前缀配置
  sn:
    orderPrefix: ODOD
    orderPaymentPrefix: ODPM
    orderRefundsPrefix: ODRF
    orderReturnsPrefix: ODRT
    orderShippingPrefix: ODSP
    paymentSessionPrefix: ODPS
    productPrefix: PROD
  #微信支付商户API证书存放根目录 实际部署时配置 /data/resource/wildfly-shop/certFiles
  #certFileRootDir: /data/resource/wildfly-shop/certFiles

  #默认产品价格有效期,单位：年
  defaultProductPriceValidPeriod: 5
  
  #客服测试环境webService地址
  customerServiceUrl: http://*************:8083/cc-server/services/WorkAssignService?wsdl
  #客服webService用户名,测试环境
  customerServiceName: cc_datang
  #客服webService密码，测试环境
  customerServicePasswd: bjdv1234

  #客服正式版webService地址
  #customerServiceUrl: http://*************:8080/cc-server/services/WorkAssignService?wsdl
  #客服webService用户名,正式环境：cc_datang
  #customerServiceName: cc_datang
  #客服webService密码，正式环境：26b2def2a6a5f1ef1a0672d380f931ea
  #customerServicePasswd: 26b2def2a6a5f1ef1a0672d380f931ea

  #订单过期时间，单位小时
  orderExpirationTime: 24
  #订单发货后，系统自动确认订单时间，单位天,默认14天
  autoConfirmReceiptTime: 14
  
  #产品规格信息获取开关
  #Brand
  brand:
    auditFlag: true
    specificationsFlag: true
    templeteFlag: true
    skuFlag: true
  #ProductCategory
  category:
    auditFlag: false
    specificationsFlag: true
    templeteFlag: true
    skuFlag: true
  #store
  store:
    auditFlag: false
    specificationsFlag: true
    templeteFlag: true
    skuFlag: true
  #Column
  column:
    auditFlag: false
    specificationsFlag: true
    templeteFlag: true
    skuFlag: false
  #ProductGroup
  group:
    auditFlag: false
    specificationsFlag: true
    templeteFlag: true
    skuFlag: true
  #TV
  tv:
    auditFlag: false
    specificationsFlag: false
    templeteFlag: true
    skuFlag: false
  #Mobile
  mobile:
    auditFlag: false
    specificationsFlag: true
    templeteFlag: true
    skuFlag: true

  #航天开发票--测试--地址
  invoiceServiceUrl: http://www.aisinogz.com:19876/AisinoFp-test/eliWebService.ws?wsdl
  #航天开发票--正式(域名)--地址
  #invoiceServiceUrl: http://www.aisinogz.com:19876/AisinoFp_gdgdwl/eliWebService.ws?wsdl
  #航天开发票--正式(IP)--地址
  #invoiceServiceUrl: http://*************:29876/AisinoFp_gdgdwl/eliWebService.ws?wsdl
  
  #UTVGO演唱会服务地址
  utvgoLiveSyncUrl: http://*************/activity-client-web/activity/freeCardController/syncLiveCard.utvgo
  utvgoLiveCancelUrl: http://*************/activity-client-web/activity/freeCardController/cancelLiveCard.utvgo
  utvgoGetLiveInfoUrl: http://*************/activity-client-web/activity/freeCardController/findLiveCardDetails.utvgo
  #点播视频产品购买后的有效播放时间,单位：天
  videoValidTime: 3
  #自营分级审核
  firstLevelRefundAuditUserId: 171
  secondLevelRefundAuditUserId: 172
  thirdLevelRefundAuditUserId: 173
  #测试环境
  #boss.integrateUrl = http://************:7003/boss2_task/integrate/integate!bossRequest
  boss.integrateUrl: http://localhost:5666/boss2_task/integrate/integate!bossRequest
  #正式环境
  #boss.integrateUrl = http://***********:80/boss2_task/integrate/integate!bossRequest
  boss:
    system: CHANNEL
    deptid: 6755999
    clientcode: FSXBGL
    clientpwd: a4ba8b1de2647f978c5604ba8432a14d
    version: 1
  #1：直接对BOSS；2：对接FAITH，再由FAITH对BOSS
  dockingBoss: 2
  #支付模块付款服务地址
  paymentServiceUrl: http://127.0.0.1:8080/community/payment/pay
  refundServiceUrl: http://127.0.0.1:8080/community/payment/refund
  queryPaymentResultServiceUrl: http://127.0.0.1:8080/community/payment/query
  queryRefundResultServiceUrl: http://127.0.0.1:8080/community/payment/queryRefundResult
  paymentMerchantCode: puhuameiju_yjnsq
  paymentSecret: yjnGcable#86
  #支付模块付款结果通知地址
  paymentNotifyUrl: http://127.0.0.1:8080/community/paymentNotify
  refundNotifyUrl: http://127.0.0.1:8080/community/refundNotify
  paymentReturnUrl: /paymentNotify