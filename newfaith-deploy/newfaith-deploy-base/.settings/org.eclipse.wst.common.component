<?xml version="1.0" encoding="UTF-8"?><project-modules id="moduleCoreId" project-version="1.5.0">
        
    <wb-module deploy-name="faith">
                
        <wb-resource deploy-path="/" source-path="/target/m2e-wtp/web-resources"/>
                
        <wb-resource deploy-path="/" source-path="/src/main/webapp" tag="defaultRootSource"/>
                
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/src/main/java"/>
                
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/src/main/resources"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/target/generated-sources/annotations"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/target/generated-test-sources/test-annotations"/>
                
        <dependent-module archiveName="newfaith-core-controller-V200R001.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/newfaith-core-controller/newfaith-core-controller">
                        
            <dependency-type>uses</dependency-type>
                    
        </dependent-module>
                
        <dependent-module archiveName="newfaith-core-service-V200R001.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/newfaith-core-service/newfaith-core-service">
                        
            <dependency-type>uses</dependency-type>
                    
        </dependent-module>
                
        <dependent-module archiveName="newfaith-core-dao-V200R001.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/newfaith-core-dao/newfaith-core-dao">
                        
            <dependency-type>uses</dependency-type>
                    
        </dependent-module>
                
        <dependent-module archiveName="newfaith-core-entity-V200R001.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/newfaith-core-entity/newfaith-core-entity">
                        
            <dependency-type>uses</dependency-type>
                    
        </dependent-module>
                
        <dependent-module archiveName="newfaith-core-form-V200R001.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/newfaith-core-form/newfaith-core-form">
                        
            <dependency-type>uses</dependency-type>
                    
        </dependent-module>
                
        <dependent-module archiveName="newfaith-config-V200R001.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/newfaith-config/newfaith-config">
                        
            <dependency-type>uses</dependency-type>
                    
        </dependent-module>
                
        <property name="context-root" value="faith"/>
                
        <property name="java-output-path" value="/newfaith-deploy-base/target/classes"/>
            
    </wb-module>
    
</project-modules>
