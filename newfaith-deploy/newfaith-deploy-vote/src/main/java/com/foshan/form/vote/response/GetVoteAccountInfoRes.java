package com.foshan.form.vote.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.account.GetAccountInfoRes;
import com.foshan.form.vote.VoteMemberForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class GetVoteAccountInfoRes extends GetAccountInfoRes{

	/**
	 * 
	 */
	private static final long serialVersionUID = -8361494449799152773L;
	private VoteMemberForm memberForm;
	
	public GetVoteAccountInfoRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO 自动生成的构造函数存根
	}
}
