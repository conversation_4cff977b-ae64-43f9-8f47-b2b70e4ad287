spring:
  #定时任务
  quartz:
    #任务数据存取方式：数据库
    job-store-type: jdbc
    #初始化表结构
    jdbc:
      initialize-schema: never
    #相关属性配置
    properties:
      org:
        quartz:
          scheduler:
            instanceName: clusteredScheduler
            instanceId: AUTO
          jobStore:
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 1000
            clusterCheckinInterval: 10000
            useProperties: false
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 100
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
  #Jackson配置
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  #缓存配置
  hazelcast:
    #config: classpath:hazelcast.xml
    config: classpath:hazelcast.yaml
  main:
    allow-circular-references: true
    
    
#swagger2自动生成文档配置      
knife4j:
  enable: true
  basic:
    enable: true
    username: admin
    password: FScatv@2016