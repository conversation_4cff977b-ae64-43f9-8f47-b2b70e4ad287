<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.foshan</groupId>
		<artifactId>newfaith</artifactId>
		<version>V200R001B03</version>
	</parent>

	<artifactId>newfaith-util</artifactId>
	<name>newfaith-util</name>
	<url>http://maven.apache.org</url>
	<dependencies>
		<!--ZXING QRCODE -->
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
		</dependency>

		<!-- 图片处理 -->
		<dependency>
			<groupId>net.coobird</groupId>
			<artifactId>thumbnailator</artifactId>
		</dependency>
		<!-- excel -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
		</dependency>
		<!-- PinYin -->
		<dependency>
			<groupId>com.belerweb</groupId>
			<artifactId>pinyin4j</artifactId>
		</dependency>
		<!-- Http Client -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.oltu.oauth2</groupId>
			<artifactId>org.apache.oltu.oauth2.client</artifactId>
		</dependency>

		<!-- Apache commons -->
		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
		</dependency>
	    <dependency>
		    <groupId>org.apache.commons</groupId>
		    <artifactId>commons-compress</artifactId>
		</dependency>

		<!-- CXF -->
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-frontend-jaxws</artifactId>
		</dependency>
		<!-- 医家webservice用到SOAP的传输协议是基于http协议的,需要相关依赖-->
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-transports-http-jetty</artifactId>
		</dependency>
		
		<dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
        </dependency>  
       	<dependency>
		    <groupId>org.freemarker</groupId>
		    <artifactId>freemarker</artifactId>
		</dependency>
		<dependency>
		    <groupId>com.itextpdf.tool</groupId>
		    <artifactId>xmlworker</artifactId>
		</dependency>
        <dependency>
            <groupId>net.sf.jtidy</groupId>
            <artifactId>jtidy</artifactId>
        </dependency>
		
			<!-- alipay -->
		<dependency>
		    <groupId>com.alipay.sdk</groupId>
		    <artifactId>alipay-sdk-java</artifactId>
		</dependency>
	</dependencies>
</project>
