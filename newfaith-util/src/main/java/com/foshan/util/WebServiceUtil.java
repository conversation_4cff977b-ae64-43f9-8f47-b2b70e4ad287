package com.foshan.util;

import java.util.HashMap;
import java.util.Map;

import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;

/**
 * cxf webservice 工具类
 * 1、采用cxf方式请求webService服务
 */
public class WebServiceUtil {
	
	/**
	 * 采用cxf方式请求webService服务
	 * @param wsdlUrl wsdl描述URL地址
	 * @param operationName  操作名称
	 * @param param 请求参数
	 * @return  结果报文Map<String,String>
	 */
	public static Map<String, String> getCustomerWebService(String wsdlUrl, String operationName, String param) {

		Map<String, String> map = new HashMap<String, String>();
		try {
			JaxWsDynamicClientFactory factroy = JaxWsDynamicClientFactory.newInstance();
			Client client = factroy.createClient(wsdlUrl);

			Object[] results = client.invoke(operationName, param);
			String result = results[0].toString();
			XmlUtil.xmlToMap(result, map);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return map;
	}
}
