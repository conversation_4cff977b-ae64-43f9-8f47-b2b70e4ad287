package com.foshan.util;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.MalformedURLException;
import java.net.SocketTimeoutException;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.URLConnection;
import java.net.UnknownHostException;
import java.nio.Buffer;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.activation.MimetypesFileTypeMap;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.*;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;

public class HttpClientUtil {

	private static final PoolingHttpClientConnectionManager HTTP_CLIENT_CONNECTION_MANAGER;

	/**
	 * CloseableHttpClient
	 */
	private static final CloseableHttpClient HTTP_CLIENT;
	private final static Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);
	static {
		HTTP_CLIENT_CONNECTION_MANAGER = new PoolingHttpClientConnectionManager(RegistryBuilder
				.<ConnectionSocketFactory>create().register("http", PlainConnectionSocketFactory.getSocketFactory())
				.register("https", SSLConnectionSocketFactory.getSocketFactory()).build());
		HTTP_CLIENT_CONNECTION_MANAGER.setDefaultMaxPerRoute(100);
		HTTP_CLIENT_CONNECTION_MANAGER.setMaxTotal(200);
		RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(60000).setConnectTimeout(60000)
				.setSocketTimeout(60000).build();
		HTTP_CLIENT = HttpClientBuilder.create().setConnectionManager(HTTP_CLIENT_CONNECTION_MANAGER)
				.setDefaultRequestConfig(requestConfig).build();
	}

	public static Map<String, String> post(String url, String reuqest, String encoding) throws Exception {
		Map<String, String> map = new HashMap<String, String>();
		map.put("status", "false");
		OutputStreamWriter out = null;
		BufferedReader br = null;
		try {
			URL httpUrl = new URL(url);
			URLConnection con = httpUrl.openConnection();
			con.setDoOutput(true);
//			con.setRequestProperty("Pragma:", "no-cache");
			con.setRequestProperty("Cache-Control", "no-cache");
			con.setRequestProperty("Content-Type", "application/json");// text/xml
			HttpURLConnection http = (HttpURLConnection) con;
			http.setRequestMethod("POST");
			http.setConnectTimeout(3000);
			http.setReadTimeout(60000);
			out = new OutputStreamWriter(con.getOutputStream(), encoding);
			out.write(reuqest);
			out.flush();
			if (http.getResponseCode() == HttpURLConnection.HTTP_OK) {
				String response = "";
				br = new BufferedReader(new InputStreamReader(con.getInputStream(), encoding));
				String line = "";
				for (line = br.readLine(); line != null; line = br.readLine()) {
					response += line + "\r\n";
				}
				map.put("status", "success");
				map.put("response", response);
			} else {
				map.put("response", "HTTP状态码" + String.valueOf(http.getResponseCode()));
			}
			http.disconnect();
		} catch (MalformedURLException e) {
			e.printStackTrace();
			map.put("status", "faile");
			map.put("response", "url格式错误！");
		} catch (SocketTimeoutException e) {
			e.printStackTrace();
			map.put("status", "faile");
			map.put("response", "请求连接超时！");
		} catch (IOException e) {
			e.printStackTrace();
			map.put("status", "faile");
			map.put("response", "IO异常！");
		} finally {
			try {
				if (null != out) {
					out.close();
				}
				if (null != br) {
					br.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
				map.put("status", "faile");
				map.put("response", "流关闭异常！");
			}
		}
		return map;
	}

	public static String getPostString(String url, String reuqest, String encoding) throws Exception {
		OutputStreamWriter out = null;
		BufferedReader br = null;

		URL httpUrl = new URL(url);
		URLConnection con = httpUrl.openConnection();
		con.setDoOutput(true);
		con.setRequestProperty("Cache-Control", "no-cache");
		con.setRequestProperty("Content-Type", "text/xml");
		HttpURLConnection http = (HttpURLConnection) con;
		http.setRequestMethod("POST");
		http.setConnectTimeout(2000);
		http.setReadTimeout(60000);
		out = new OutputStreamWriter(con.getOutputStream(), encoding);
		out.write(reuqest);
		out.flush();
		if (http.getResponseCode() == HttpURLConnection.HTTP_OK) {
			String response = "";
			br = new BufferedReader(new InputStreamReader(con.getInputStream(), encoding));
			String line = "";
			for (line = br.readLine(); line != null; line = br.readLine()) {
				response += line + "\r\n";
			}
			return response;
		} else {
			logger.error("http请求数据失败：responseCode:{}；responseMessage:{}", http.getResponseCode(),
					http.getResponseMessage());
			return null;
		}
	}

	public static byte[] getPostString(String url, String reuqest) {
		InputStream inputStream = null;
		byte[] data = null;
		// 创建默认的httpClient实例.
		CloseableHttpClient httpclient = HttpClients.createDefault();
		// 创建httppost
		HttpPost httppost = new HttpPost(url);
		httppost.addHeader("Content-type", "application/json; charset=utf-8");
		httppost.setHeader("Accept", "application/json");
		try {
			StringEntity s = new StringEntity(reuqest, Charset.forName("UTF-8"));
			s.setContentEncoding("UTF-8");
			httppost.setEntity(s);
			CloseableHttpResponse response = httpclient.execute(httppost);
			try {
				// 获取相应实体
				HttpEntity entity = response.getEntity();
				if (entity != null) {
					inputStream = entity.getContent();
					data = readInputStream(inputStream);
				}
				return data;
			} finally {
				response.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			// 关闭连接,释放资源
			try {
				httpclient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return data;
	}

	/**
	 * 将流 保存为数据数组
	 * 
	 * @param inStream
	 * @return
	 * @throws Exception
	 */
	public static byte[] readInputStream(InputStream inStream) throws Exception {
		ByteArrayOutputStream outStream = new ByteArrayOutputStream();
		// 创建一个Buffer字符串
		byte[] buffer = new byte[1024];
		// 每次读取的字符串长度，如果为-1，代表全部读取完毕
		int len = 0;
		// 使用一个输入流从buffer里把数据读取出来
		while ((len = inStream.read(buffer)) != -1) {
			// 用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
			outStream.write(buffer, 0, len);
		}
		// 关闭输入流
		inStream.close();
		// 把outStream里的数据写入内存
		return outStream.toByteArray();
	}

	/**
	 * POST请求
	 * 
	 * @param url          URL
	 * @param parameterMap 请求参数
	 * @return 返回结果
	 */
	public static String post(String url, Map<String, Object> parameterMap) {
		Assert.hasText(url, "url不能为空");

		String result = null;
		try {
			List<NameValuePair> nameValuePairs = new ArrayList<>();
			if (parameterMap != null) {
				for (Map.Entry<String, Object> entry : parameterMap.entrySet()) {
					String name = entry.getKey();
					String value = ConvertUtils.convert(entry.getValue());
					if (StringUtils.isNotEmpty(name)) {
						nameValuePairs.add(new BasicNameValuePair(name, value));
					}
				}
			}
			HttpPost httpPost = new HttpPost(url);
			httpPost.setEntity(new UrlEncodedFormEntity(nameValuePairs, "UTF-8"));

			CloseableHttpResponse httpResponse = HTTP_CLIENT.execute(httpPost);
			try {
				HttpEntity httpEntity = httpResponse.getEntity();
				if (httpEntity != null) {
					result = EntityUtils.toString(httpEntity, "UTF-8");
					EntityUtils.consume(httpEntity);
				}
			} finally {
				try {
					httpResponse.close();
				} catch (IOException e) {
				}
			}
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e.getMessage(), e);
		} catch (ClientProtocolException e) {
			throw new RuntimeException(e.getMessage(), e);
		} catch (ParseException e) {
			throw new RuntimeException(e.getMessage(), e);
		} catch (IOException e) {
			throw new RuntimeException(e.getMessage(), e);
		}
		return result;
	}

	/**
	 * GET请求
	 * 
	 * @param url          URL
	 * @param parameterMap 请求参数
	 * @return 返回结果
	 */
	public static String get(String url, Map<String, Object> parameterMap) {
		Assert.hasText(url, "url不能为空");

		String result = null;
		try {
			List<NameValuePair> nameValuePairs = new ArrayList<>();
			if (parameterMap != null) {
				for (Map.Entry<String, Object> entry : parameterMap.entrySet()) {
					String name = entry.getKey();
					String value = ConvertUtils.convert(entry.getValue());
					if (StringUtils.isNotEmpty(name)) {
						nameValuePairs.add(new BasicNameValuePair(name, value));
					}
				}
			}
			HttpGet httpGet = new HttpGet(url + (StringUtils.contains(url, "?") ? "&" : "?")
					+ EntityUtils.toString(new UrlEncodedFormEntity(nameValuePairs, "UTF-8")));
			CloseableHttpResponse httpResponse = HTTP_CLIENT.execute(httpGet);
			try {
				HttpEntity httpEntity = httpResponse.getEntity();
				if (httpEntity != null) {
					result = EntityUtils.toString(httpEntity);
					EntityUtils.consume(httpEntity);
				}
			} finally {
				try {
					httpResponse.close();
				} catch (IOException e) {
				}
			}
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e.getMessage(), e);
		} catch (ParseException e) {
			throw new RuntimeException(e.getMessage(), e);
		} catch (ClientProtocolException e) {
			throw new RuntimeException(e.getMessage(), e);
		} catch (IOException e) {
			throw new RuntimeException(e.getMessage(), e);
		}
		return result;
	}

	/**
	 * GET请求
	 * 
	 * @param url          URL
	 * @param keyValuePair 请求参数字符串
	 * @return 返回结果
	 */
	public static String get(String url, String keyValuePair) {
		Assert.hasText(url, "Url不能为空串");

		String result = null;
		String targetUrl = url;
		try {
			if (!StringUtils.isEmpty(keyValuePair)) {
				targetUrl = targetUrl + (StringUtils.contains(targetUrl, "?") ? "&" : "?") + keyValuePair;
			}
			HttpGet httpGet = new HttpGet(targetUrl);
			CloseableHttpResponse httpResponse = HTTP_CLIENT.execute(httpGet);
			try {
				HttpEntity httpEntity = httpResponse.getEntity();
				if (httpEntity != null) {
					result = EntityUtils.toString(httpEntity);
					EntityUtils.consume(httpEntity);
				}
			} finally {
				try {
					httpResponse.close();
				} catch (IOException e) {
				}
			}
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e.getMessage(), e);
		} catch (ParseException e) {
			throw new RuntimeException(e.getMessage(), e);
		} catch (ClientProtocolException e) {
			throw new RuntimeException(e.getMessage(), e);
		} catch (IOException e) {
			throw new RuntimeException(e.getMessage(), e);
		}
		return result;
	}

	/**
	 * GET请求
	 *
	 * @param url          URL
	 * @param keyValuePair 请求参数字符串
	 * @return 返回结果
	 */
	public static String get(String url, String keyValuePair,Map<String, String> headers) {
		Assert.hasText(url, "Url不能为空串");

		String result = null;
		String targetUrl = url;
		try {
			if (!StringUtils.isEmpty(keyValuePair)) {
				targetUrl = targetUrl + (StringUtils.contains(targetUrl, "?") ? "&" : "?") + keyValuePair;
			}
			HttpGet httpGet = new HttpGet(targetUrl);
			// 将params作为请求头添加
			if (headers != null && !headers.isEmpty()) {
				for (Map.Entry<String, String> entry : headers.entrySet()) {
					String name = entry.getKey();
					String value = entry.getValue();
					if (StringUtils.isNotEmpty(name)) {
						httpGet.addHeader(name, value);
					}
				}
			}
			CloseableHttpResponse httpResponse = HTTP_CLIENT.execute(httpGet);
			try {
				HttpEntity httpEntity = httpResponse.getEntity();
				if (httpEntity != null) {
					result = EntityUtils.toString(httpEntity);
					EntityUtils.consume(httpEntity);
				}
			} finally {
				try {
					httpResponse.close();
				} catch (IOException e) {
				}
			}
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e.getMessage(), e);
		} catch (ParseException e) {
			throw new RuntimeException(e.getMessage(), e);
		} catch (ClientProtocolException e) {
			throw new RuntimeException(e.getMessage(), e);
		} catch (IOException e) {
			throw new RuntimeException(e.getMessage(), e);
		}
		return result;
	}

	/**
	 * GET请求
	 * 
	 * @param url URL
	 * @return 返回结果
	 */
	public static String get(String url) {
		return get(url, "");
	}

	/**
	 * POST请求
	 * 
	 * @param url URL
	 * @param xml XML
	 * @return 返回结果
	 */
	public static String post(String url, String xml) {
		Assert.hasText(url, "url不能为空");

		String result = null;
		try {
			HttpPost httpPost = new HttpPost(url);
			httpPost.setEntity(new StringEntity(xml, "UTF-8"));
			CloseableHttpResponse httpResponse = HTTP_CLIENT.execute(httpPost);
			try {
				HttpEntity httpEntity = httpResponse.getEntity();
				if (httpEntity != null) {
					result = EntityUtils.toString(httpEntity, "UTF-8");
					EntityUtils.consume(httpEntity);
				}
			} finally {
				try {
					httpResponse.close();
				} catch (IOException e) {
				}
			}
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
			throw new RuntimeException(e.getMessage(), e);
		} catch (ClientProtocolException e) {
			e.printStackTrace();
			throw new RuntimeException(e.getMessage(), e);
		} catch (ParseException e) {
			e.printStackTrace();
			throw new RuntimeException(e.getMessage(), e);
		} catch (IOException e) {
			e.printStackTrace();
			throw new RuntimeException(e.getMessage(), e);
		}
		return result;
	}

	/**
	 * 判断当前请求来源于移动端还是PC端浏览器。
	 * 
	 * @param request
	 * @return: boolean true 移动端浏览器；false PC端浏览器
	 */
	public static boolean isMobile(HttpServletRequest request) {

		List<String> mobileAgents = Arrays.asList("ipad", "iphone os", "rv:*******", "ucweb", "android", "windows ce",
				"windows mobile", "uapp");
		String ua = request.getHeader("User-Agent").toLowerCase();
		logger.info("access host user-agent:{}", ua);
		for (String sua : mobileAgents) {
			if (ua.indexOf(sua) > -1) {
				return true;// 手机端
			}
		}
		return false;// PC端
	}

	/**
	 * 是否微信浏览器
	 * 
	 * @param request
	 * @return: boolean
	 */
	public static boolean isWechat(HttpServletRequest request) {
		String ua = request.getHeader("User-Agent").toLowerCase();
		if (ua.indexOf("micromessenger") > -1) {
			return true;// 微信
		}
		return false;// 非微信手机浏览器
	}

	/**
	 * 是否微信小程序
	 * 
	 * @param request
	 * @return: boolean
	 */
	public static boolean isWxMiniProgram(HttpServletRequest request) {
//		String ua = request.getHeader("User-Agent").toLowerCase();
//		if (ua.indexOf("miniProgram") > -1) {
//			return true;// 微信小程序
//		}
//		return false;// 非微信小程序
		Object miniPromgramRequest = request.getAttribute("wxMiniProgramCheck");
		return miniPromgramRequest != null && miniPromgramRequest.toString().equals("true");
	}

	/**
	 * 是否支付宝浏览器
	 * 
	 * @param request
	 * @return: boolean
	 */
	public static boolean isAlipay(HttpServletRequest request) {
		String ua = request.getHeader("User-Agent").toLowerCase();
		if (ua.indexOf("alipay") > -1) {
			return true;// 支付宝浏览器
		}
		return false;// 非支付宝手机浏览器

	}

	/**
	 * 获取HTTP请求客户端的实际ip
	 * 
	 * @param request
	 * @return
	 */
	public static String getIpAddr(HttpServletRequest request) {
		System.out.println("x-forwarded-for:" + request.getHeader("x-forwarded-for"));
		System.out.println("Proxy-Client-IP:" + request.getHeader("Proxy-Client-IP"));
		System.out.println("WL-Proxy-Client-IP:" + request.getHeader("WL-Proxy-Client-IP"));
		System.out.println("getRemoteAddr:" + request.getRemoteAddr());
		System.out.println("X-Real-IP:" + request.getHeader("X-Real-IP"));

		String ipAddress = request.getHeader("x-forwarded-for");
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("Proxy-Client-IP");
			System.out.println("Proxy-Client-IP:" + request.getHeader("Proxy-Client-IP"));
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getRemoteAddr();
			if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
				// 根据网卡取本机配置的IP
				InetAddress inet = null;
				try {
					inet = InetAddress.getLocalHost();
				} catch (UnknownHostException e) {
					e.printStackTrace();
				}
				ipAddress = inet.getHostAddress();
			}
		}
		// 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
		if (ipAddress != null && ipAddress.length() > 15) { // "***.***.***.***".length()
															// = 15
			if (ipAddress.indexOf(",") > 0) {
				ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
			}
		}
		return ipAddress;
	}

	public static Map<String, Object> formData(String postUrl, Map<String, Object> postParam) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Assert.hasText(postUrl, "postUrl不能为空");
		try {
			List<NameValuePair> nameValuePairs = new ArrayList<>();
			if (postParam != null) {
				for (Map.Entry<String, Object> entry : postParam.entrySet()) {
					String name = entry.getKey();
					String value = ConvertUtils.convert(entry.getValue());
					if (StringUtils.isNotEmpty(name)) {
						nameValuePairs.add(new BasicNameValuePair(name, value));
					}
				}
			}
			HttpPost httpPost = new HttpPost(postUrl);
			httpPost.setEntity(new UrlEncodedFormEntity(nameValuePairs, "UTF-8"));
			CloseableHttpResponse httpResponse = HTTP_CLIENT.execute(httpPost);
			try {
				resultMap.put("statusCode", httpResponse.getStatusLine().getStatusCode());
				HttpEntity httpEntity = httpResponse.getEntity();
				if (httpEntity != null) {
					// result = EntityUtils.toString(httpEntity, "UTF-8");
					resultMap.put("data", EntityUtils.toString(httpEntity, Charset.forName("UTF-8")));
					EntityUtils.consume(httpEntity);
				}
			} finally {
				try {
					httpResponse.close();
				} catch (IOException e) {
				}
			}
		} catch (UnsupportedEncodingException e) {
			// throw new RuntimeException(e.getMessage(), e);
		} catch (ClientProtocolException e) {
			// throw new RuntimeException(e.getMessage(), e);
		} catch (ParseException e) {
			// throw new RuntimeException(e.getMessage(), e);
		} catch (IOException e) {
			// throw new RuntimeException(e.getMessage(), e);

		}
		return resultMap;
	}
	
	public static Map<String, Object> formData(String postUrl, Map<String, Object> postParam,String cookies) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Assert.hasText(postUrl, "postUrl不能为空");
		try {
			List<NameValuePair> nameValuePairs = new ArrayList<>();
			if (postParam != null) {
				for (Map.Entry<String, Object> entry : postParam.entrySet()) {
					String name = entry.getKey();
					String value = ConvertUtils.convert(entry.getValue());
					if (StringUtils.isNotEmpty(name)) {
						nameValuePairs.add(new BasicNameValuePair(name, value));
					}
				}
			}
			HttpPost httpPost = new HttpPost(postUrl);
			httpPost.setEntity(new UrlEncodedFormEntity(nameValuePairs, "UTF-8"));
			if(StringUtils.isNotEmpty(cookies)) {
				httpPost.setHeader("cookie", cookies);
			}
			CloseableHttpResponse httpResponse = HTTP_CLIENT.execute(httpPost);
			try {
				resultMap.put("statusCode", httpResponse.getStatusLine().getStatusCode());
				HttpEntity httpEntity = httpResponse.getEntity();
				if (httpEntity != null) {
					// result = EntityUtils.toString(httpEntity, "UTF-8");
					resultMap.put("data", EntityUtils.toString(httpEntity, Charset.forName("UTF-8")));
					EntityUtils.consume(httpEntity);
				}
			} finally {
				try {
					httpResponse.close();
				} catch (IOException e) {
				}
			}
		} catch (UnsupportedEncodingException e) {
			// throw new RuntimeException(e.getMessage(), e);
		} catch (ClientProtocolException e) {
			// throw new RuntimeException(e.getMessage(), e);
		} catch (ParseException e) {
			// throw new RuntimeException(e.getMessage(), e);
		} catch (IOException e) {
			// throw new RuntimeException(e.getMessage(), e);

		}
		return resultMap;
	}

	/**
	 * 发送post请求
	 * 
	 * @param url     请求url
	 * @param charset 编码
	 * @param payload 请求中的消息体载荷
	 * @param params  参数，作为请求头
	 * @return
	 * @throws ClientProtocolException
	 * @throws IOException
	 */
	public static String jsonPost(String url, String charset, String payload, Map<String, String> params)
			throws ClientProtocolException, IOException {
		// 设置超时时间
		RequestConfig defaultRequestConfig = RequestConfig.custom().setSocketTimeout(20000000).setConnectTimeout(20000000)
				.setConnectionRequestTimeout(20000000).build();

		CloseableHttpClient httpClient = HttpClients.custom().setDefaultRequestConfig(defaultRequestConfig).build();
		httpClient = HttpClients.createDefault();

		String encoderJson = new String(payload.getBytes(), charset);// URLEncoder.encode(payload, charset);

		HttpPost post = new HttpPost(url);
		// 将params作为请求头添加
		if (params != null && !params.isEmpty()) {
			for (Map.Entry<String, String> entry : params.entrySet()) {
				String name = entry.getKey();
				String value = entry.getValue();
				if (StringUtils.isNotEmpty(name)) {
					post.addHeader(name, value);
				}
			}
		}
		
		post.setConfig(defaultRequestConfig);
		StringEntity stringEntity = new StringEntity(encoderJson, "UTF-8");
		stringEntity.setContentType("application/json");
		stringEntity.setContentEncoding(new BasicHeader(HTTP.CONTENT_ENCODING, "UTF-8"));
		post.setEntity(stringEntity);

		ResponseHandler<String> responseHandler = new ResponseHandler<String>() {
			public String handleResponse(HttpResponse response) throws ClientProtocolException, IOException {
				int status = response.getStatusLine().getStatusCode();
				if (status >= 200 && status < 300) {
					HttpEntity entity = (HttpEntity) response.getEntity();
					return entity != null ? EntityUtils.toString(entity) : null;
				} else {
					throw new ClientProtocolException("Unexpected response status: " + status);
				}
			}
		};

		return httpClient.execute(post, responseHandler);
	}
	
	public static String jsonPost(String url, String charset, String payload, Map<String, String> params,Map<String, String> headerMap)
			throws ClientProtocolException, IOException {
		// 设置超时时间
		RequestConfig defaultRequestConfig = RequestConfig.custom().setSocketTimeout(2000000).setConnectTimeout(2000000)
				.setConnectionRequestTimeout(2000000).build();

		CloseableHttpClient httpClient = HttpClients.custom().setDefaultRequestConfig(defaultRequestConfig).build();
		httpClient = HttpClients.createDefault();

		String encoderJson = new String(payload.getBytes(), charset);// URLEncoder.encode(payload, charset);

		HttpPost post = new HttpPost(url);
		post.addHeader("Content-type", "application/json; charset=utf-8");
		if (headerMap != null) {
			for (Map.Entry<String, String> entry : headerMap.entrySet()) {
				String name = entry.getKey();
				String value = ConvertUtils.convert(entry.getValue());
				if (StringUtils.isNotEmpty(name)) {
					post.addHeader(name, value);
				}
			}
		}
		post.setConfig(defaultRequestConfig);
		StringEntity stringEntity = new StringEntity(encoderJson, "UTF-8");
		stringEntity.setContentType("application/json");
		stringEntity.setContentEncoding(new BasicHeader(HTTP.CONTENT_ENCODING, "UTF-8"));
		post.setEntity(stringEntity);

		ResponseHandler<String> responseHandler = new ResponseHandler<String>() {
			public String handleResponse(HttpResponse response) throws ClientProtocolException, IOException {
				int status = response.getStatusLine().getStatusCode();
				if (status >= 200 && status < 300) {
					HttpEntity entity = (HttpEntity) response.getEntity();
					return entity != null ? EntityUtils.toString(entity) : null;
				} else {
					throw new ClientProtocolException("Unexpected response status: " + status);
				}
			}
		};

		return httpClient.execute(post, responseHandler);
	}
	
	/**
	 * 发送post请求
	 * 
	 * @param url     请求url
	 * @param charset 编码，未使用
	 * @param payload 请求中的消息体载荷
	 * @param params  参数，未使用
	 * @return
	 * @throws ClientProtocolException
	 * @throws IOException
	 */
	public static String jsonPost(String url, String charset, String payload, Map<String, String> params,String cookie)
			throws ClientProtocolException, IOException {
		// 设置超时时间
		RequestConfig defaultRequestConfig = RequestConfig.custom().setSocketTimeout(200000).setConnectTimeout(200000)
				.setConnectionRequestTimeout(200000).build();

		CloseableHttpClient httpClient = HttpClients.custom().setDefaultRequestConfig(defaultRequestConfig).build();
		httpClient = HttpClients.createDefault();

		String encoderJson = new String(payload.getBytes(), charset);// URLEncoder.encode(payload, charset);

		HttpPost post = new HttpPost(url);
		post.setConfig(defaultRequestConfig);
		StringEntity stringEntity = new StringEntity(encoderJson, "UTF-8");
		stringEntity.setContentType("application/json");
		stringEntity.setContentEncoding(new BasicHeader(HTTP.CONTENT_ENCODING, "UTF-8"));
		post.setEntity(stringEntity);
		if(StringUtils.isNotEmpty(cookie)) {
			post.setHeader("cookie", cookie);
		}
		

		ResponseHandler<String> responseHandler = new ResponseHandler<String>() {
			public String handleResponse(HttpResponse response) throws ClientProtocolException, IOException {
				int status = response.getStatusLine().getStatusCode();
				if (status >= 200 && status < 300) {
					HttpEntity entity = (HttpEntity) response.getEntity();
					return entity != null ? EntityUtils.toString(entity) : null;
				} else {
					throw new ClientProtocolException("Unexpected response status: " + status);
				}
			}
		};

		return httpClient.execute(post, responseHandler);
	}
     
    /**
         * Post
         * 登陆成功后返回cookie
         * @param json
         * @param outPath
         * @return
         * @throws IOException
         */
        public static String getCookieLogin(JSONObject json, String outPath) throws IOException{
            URL newURL = new URL(outPath);
            HttpURLConnection conn = (HttpURLConnection) newURL.openConnection();
            conn.setRequestProperty("Connection","keep-alive");
            conn.setRequestProperty("User-Agent",
                    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.107 Safari/537.36");
            conn.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            conn.setDoInput(true);
            conn.setDoOutput(true);
            OutputStreamWriter out =null;
            try {
            	out = new OutputStreamWriter(conn.getOutputStream(),"utf-8");
            	out.write( String.valueOf( json ) );
                out.flush();
                out.close();
                InputStream inputStream = conn.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "utf-8"));
                reader.close();
                String headerName = null;
                StringBuilder cookies = new StringBuilder();
                for(int i =1;(headerName= conn.getHeaderFieldKey(i))!=null;i++){
                    if(headerName.equals("Set-Cookie")){
                        String c = conn.getHeaderField(i);
                        c = c.substring(0, c.indexOf(";"));
                        String cookieName = c.substring(0, c.indexOf("="));
                        String cookieValue = c.substring(c.indexOf("=") + 1, c.length());
                        cookies.append(cookieName + "=");
                        cookies.append(cookieValue+";");
                    }
                }
                return cookies.toString();
            } catch (IOException e1) {
    			e1.printStackTrace();
    		} finally {
    			if(null!=out) {
    				out.close();
    			}
    		}
            return null;
        }
    
	/**
	 * 发送get请求
	 * 
	 * @param url    请求url
	 * @param params 参数
	 * @return
	 * @throws URISyntaxException
	 * @throws ClientProtocolException
	 * @throws IOException
	 */
	public static String paramsGet(String url, Map<String, String> params)
			throws URISyntaxException, ClientProtocolException, IOException {
		RequestConfig defaultRequestConfig = RequestConfig.custom().setSocketTimeout(5000).setConnectTimeout(5000)
				.setConnectionRequestTimeout(5000).build();
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultRequestConfig(defaultRequestConfig).build();
		httpClient = HttpClients.createDefault();

		URIBuilder builder = new URIBuilder(url);
		if (null != params) {
			Set<String> set = params.keySet();
			for (String key : set) {
				builder.setParameter(key, params.get(key));
			}
		}

		HttpGet get = new HttpGet(builder.build());

		ResponseHandler<String> responseHandler = new ResponseHandler<String>() {
			public String handleResponse(HttpResponse response) throws ClientProtocolException, IOException {
				int status = response.getStatusLine().getStatusCode();
				if (status >= 200 && status < 300) {
					HttpEntity entity = (HttpEntity) response.getEntity();
					return entity != null ? EntityUtils.toString(entity) : null;
				} else {
					throw new ClientProtocolException("Unexpected response status: " + status);
				}
			}
		};
		logger.debug("发送get请求：" + get.getURI());
		return httpClient.execute(get, responseHandler);
	}

	/**
	 * 发送post请求
	 * 
	 * @param url 请求url
	 * @return
	 * @throws ClientProtocolException
	 * @throws IOException
	 */
	public static String postUrl(String url) throws ClientProtocolException, IOException {
		// 设置超时时间
		RequestConfig defaultRequestConfig = RequestConfig.custom().setSocketTimeout(5000).setConnectTimeout(5000)
				.setConnectionRequestTimeout(5000).build();
		CloseableHttpClient httpClient = HttpClients.custom().setDefaultRequestConfig(defaultRequestConfig).build();
		httpClient = HttpClients.createDefault();
		HttpPost post = new HttpPost(url);
		post.setConfig(defaultRequestConfig);
		ResponseHandler<String> responseHandler = new ResponseHandler<String>() {
			public String handleResponse(HttpResponse response) throws ClientProtocolException, IOException {
				int status = response.getStatusLine().getStatusCode();
				if (status >= 200 && status < 300) {
					HttpEntity entity = (HttpEntity) response.getEntity();
					return entity != null ? EntityUtils.toString(entity) : null;
				} else {
					throw new ClientProtocolException("Unexpected response status: " + status);
				}
			}
		};
		return httpClient.execute(post, responseHandler);
	}

	/**
	 * 上传
	 * 
	 * @param urlStr
	 * @param textMap
	 * @param fileMap
	 * @param contentType 没有传入文件类型默认采用application/octet-stream
	 *                    contentType非空采用filename匹配默认的图片类型
	 * @return 返回response数据
	 */
	@SuppressWarnings("rawtypes")
	public static String formUpload(String urlStr, Map<String, String> textMap, Map<String, String> fileMap,
			String contentType) {
		String res = "";
		HttpURLConnection conn = null;
		// boundary就是request头和上传文件内容的分隔符
		String BOUNDARY = "---------------------------123821742118716";
		try {
			URL url = new URL(urlStr);
			conn = (HttpURLConnection) url.openConnection();
			conn.setConnectTimeout(72000);
			conn.setReadTimeout(72000);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Connection", "Keep-Alive");
			// conn.setRequestProperty("User-Agent","Mozilla/5.0 (Windows; U; Windows NT
			// 6.1; zh-CN; rv:*******)");
			conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);
			OutputStream out = new DataOutputStream(conn.getOutputStream());
			// text
			if (textMap != null) {
				StringBuffer strBuf = new StringBuffer();
				Iterator iter = textMap.entrySet().iterator();
				while (iter.hasNext()) {
					Map.Entry entry = (Map.Entry) iter.next();
					String inputName = (String) entry.getKey();
					String inputValue = (String) entry.getValue();
					if (inputValue == null) {
						continue;
					}
					strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
					strBuf.append("Content-Disposition: form-data; name=\"" + inputName + "\"\r\n\r\n");
					strBuf.append(inputValue);
				}
				out.write(strBuf.toString().getBytes());
			}
			// file
			if (fileMap != null) {
				Iterator iter = fileMap.entrySet().iterator();
				while (iter.hasNext()) {
					Map.Entry entry = (Map.Entry) iter.next();
					String inputName = (String) entry.getKey();
					String inputValue = (String) entry.getValue();
					if (inputValue == null) {
						continue;
					}
					File file = new File(inputValue);
					String filename = file.getName();

					// 没有传入文件类型，同时根据文件获取不到类型，默认采用application/octet-stream
					contentType = new MimetypesFileTypeMap().getContentType(file);
					// contentType非空采用filename匹配默认的图片类型
					if (!"".equals(contentType)) {
						if (filename.endsWith(".png")) {
							contentType = "image/png";
						} else if (filename.endsWith(".jpg") || filename.endsWith(".jpeg")
								|| filename.endsWith(".jpe")) {
							contentType = "image/jpeg";
						} else if (filename.endsWith(".gif")) {
							contentType = "image/gif";
						} else if (filename.endsWith(".ico")) {
							contentType = "image/image/x-icon";
						}else if(filename.endsWith(".tar")) {
							contentType = "application/x-tar";
						}
					}
					if (contentType == null || "".equals(contentType)) {
						contentType = "application/octet-stream";
					}
					StringBuffer strBuf = new StringBuffer();
					strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
					strBuf.append("Content-Disposition: form-data; name=\"" + inputName + "\"; filename=\"" + filename
							+ "\"\r\n");
					strBuf.append("Content-Type:" + contentType + "\r\n\r\n");
					out.write(strBuf.toString().getBytes());
					DataInputStream in = new DataInputStream(new FileInputStream(file));
					int bytes = 0;
					byte[] bufferOut = new byte[1024];
					while ((bytes = in.read(bufferOut)) != -1) {
						out.write(bufferOut, 0, bytes);
					}
					in.close();
				}
			}
			byte[] endData = ("\r\n--" + BOUNDARY + "--\r\n").getBytes();
			out.write(endData);
			out.flush();
			out.close();
			// 读取返回数据
			StringBuffer strBuf = new StringBuffer();
			BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
			String line = null;
			while ((line = reader.readLine()) != null) {
				strBuf.append(line).append("\n");
			}
			res = strBuf.toString();
			reader.close();
			reader = null;
		} catch (Exception e) {
			System.out.println("发送POST请求出错。" + urlStr);
			e.printStackTrace();
		} finally {
			if (conn != null) {
				conn.disconnect();
				conn = null;
			}
		}
		return res;
	}

	/**
	 * 
	 * @Title: 获取http请求BODY的内容的字符串
	 * @Description: 获取http请求BODY的内容的字符串
	 * @param request HTTP请求
	 * @return BODY的内容的字符串，读取异常则返回null
	 */
	public static String readHttpRequestContent(HttpServletRequest request) {
		String reuslt = null;
		try (java.io.StringWriter writer = new java.io.StringWriter();) {
			BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()));
			int read;
			char[] buf = new char[1024 * 8];
			while ((read = reader.read(buf)) != -1) {
				writer.write(buf, 0, read);
			}
			reuslt = writer.getBuffer().toString();
		} catch (Exception e) {
			e.printStackTrace();
		}

		return reuslt;
	}

}
