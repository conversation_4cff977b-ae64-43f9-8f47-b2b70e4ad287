package com.foshan.util;

import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.*;
import java.security.spec.InvalidParameterSpecException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.util.Assert;
import org.bouncycastle.crypto.Digest;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;

/**
 * 加密或摘要工具类，含国密方法 1、用MD5算法生成摘要，结果为16位十六进制字符表示 2、用MD5算法生成摘要，结果为16位纯数字字符表示
 * 3、用SHA1算法生成安全摘要 4、AES算法解密 5、 用国密sm3生成摘要
 */
public class DigestUtil {
	public static final String PAY_SIGN_PARAM_NAME = "sign";

	static {
		Security.addProvider(new BouncyCastleProvider());
	}

	/**
	 * 用MD5算法生成摘要
	 * 
	 * @param str 被摘要的内容
	 * @return 返回摘要，结果为16位十六进制字符表示
	 */
	public static String getMD5Str(String str) {
		byte[] digest = null;
		try {
			MessageDigest md5 = MessageDigest.getInstance("md5");
			digest = md5.digest(str.getBytes("utf-8"));
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		// 16是表示转换为16进制数
		String md5Str = new BigInteger(1, digest).toString(16);
		return md5Str;
	}
	
	/**
	 * 用MD5加密
	 * 
	 * @param str 被摘要的内容
	 * @param format 传“16”，返回16位字符串；也可默认不传或其他返回32字符串；
	 * @return 返回结果为十六进制字符串
	 */
	public static String getMD5Str(String str,String format) {
		String result = "";
        try {
        	MessageDigest md = MessageDigest.getInstance("MD5");
        	md.update(str.getBytes());
        	byte b[] = md.digest();
        	int i;
        	StringBuffer buf = new StringBuffer("");
        	for (int offset = 0; offset < b.length; offset++) {
        		i = b[offset];
        		if (i < 0)
        			i += 256;
        		if (i < 16)
        			buf.append("0");
        		buf.append(Integer.toHexString(i));
        	}
        	
        	if(StringUtils.isNotEmpty(format) && format.equals("16")) {
        		result =buf.toString().substring(8, 24);
        	}else {
        		result = buf.toString();
        	}
        } catch (NoSuchAlgorithmException e) {
        	e.printStackTrace();
        }
        return result;
	}

	/**
	 * 用MD5算法生成纯数字摘要
	 * 
	 * @param str 被摘要的内容
	 * @return 返回摘要，结果为16位纯数字字符表示
	 */
	public static String getMD5Number(String str) {
		String s = null;
		char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9' };
		try {
			String md5Str = getMD5Str(str);
			byte[] digest = md5Str.getBytes("utf-8");
			char result[] = new char[16];
			int k = 0;
			for (int i = 0; i < 16; i++) {
				byte byte0 = digest[i];
				// 只取高位
				result[k++] = hexDigits[(byte0 >>> 4 & 0xf) % 10];
			}
			s = new String(result); // 换后的结果转换为字符串
		} catch (Exception e) {
			e.printStackTrace();
		}
		return s;
	}

	/**
	 * 用SHA1算法生成安全摘要
	 * 
	 * @param signtureContent 被摘要内容
	 * @return 安全摘要
	 */
	public static String getSHA1(String signtureContent) {
		try {
			String str = signtureContent;
			// SHA1签名生成a
			MessageDigest md = MessageDigest.getInstance("SHA-1");
			md.update(str.getBytes());
			byte[] digest = md.digest();

			StringBuffer hexstr = new StringBuffer();
			String shaHex = "";
			for (int i = 0; i < digest.length; i++) {
				shaHex = Integer.toHexString(digest[i] & 0xFF);
				if (shaHex.length() < 2) {
					hexstr.append(0);
				}
				hexstr.append(shaHex);
			}
			return hexstr.toString();
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * AES算法解密
	 * 
	 * @param mode           // 算法+填充，可选：AES/CBC/PKCS7Padding, AES/ECB/PKCS7Padding,
	 *                       AES/CBC/PKCS5Padding 等
	 * @param dataByte       //密文，被加密的数据
	 * @param keyByte        //秘钥
	 * @param ivByte         //初始向量
	 * @param encodingFormat //解密后的结果需要进行的编码
	 * @return 明文
	 * @throws Exception
	 */
	public static String decrypt(String mode, byte[] dataByte, byte[] keyByte, byte[] ivByte, String encodingFormat)
			throws Exception {
		try {
			Cipher cipher = Cipher.getInstance(mode);
			SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
			AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
			parameters.init(new IvParameterSpec(ivByte));
			cipher.init(Cipher.DECRYPT_MODE, spec, parameters);// 初始化
			byte[] resultByte = cipher.doFinal(dataByte);
			if (null != resultByte && resultByte.length > 0) {
				String result = new String(resultByte, encodingFormat);
				return result;
			}
			return null;
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		} catch (NoSuchPaddingException e) {
			e.printStackTrace();
		} catch (InvalidParameterSpecException e) {
			e.printStackTrace();
		} catch (InvalidKeyException e) {
			e.printStackTrace();
		} catch (InvalidAlgorithmParameterException e) {
			e.printStackTrace();
		} catch (IllegalBlockSizeException e) {
			e.printStackTrace();
		} catch (BadPaddingException e) {
			e.printStackTrace();
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return null;
	}

	// **********************************国密算法******************************************

	/**
	 * 国密sm3 摘要
	 * 
	 * @param msg 被加密字符串
	 * @return 长度为64个小写字符的字符串（256位长度）
	 * @throws UnsupportedEncodingException
	 */
	public static String sm3Digest(String msg) throws UnsupportedEncodingException {
		Digest md = new SM3Digest();
		byte[] msgByte = msg.getBytes("UTF-8");

		md.update(msgByte, 0, msgByte.length);
		byte[] digest = new byte[md.getDigestSize()];
		md.doFinal(digest, 0);
		// byte to String
		String hex = "";
		if (digest != null) {
			for (Byte b : digest) {
				hex += String.format("%02X", b.intValue() & 0xFF);
			}
		}
		return hex.toLowerCase();
	}

	/**
	 * 生成签名，Map中的参数名按字母升序排序，
	 * 对排序后的参数，按此形式进行签名paramKey1=paramValue1&paramKey2=paramValue2&...&key=API密钥。
	 * 参数值为空的字段不参与签名
	 * @param data     待签名数据Map<String, String>
	 * @param key      API密钥
	 * @param signType 签名方式:SM3，SHA1，MD5
	 * @return 签名
	 */
	public static String generateSignature(final Map<String, String> data, String key, String signType)
			throws Exception {
		Assert.notNull(signType, "签名算法不能为空！");
		Set<String> keySet = data.keySet();
		String[] keyArray = keySet.toArray(new String[keySet.size()]);
		Arrays.sort(keyArray);
		StringBuilder sb = new StringBuilder();
		for (String k : keyArray) {
			if (k.equals(PAY_SIGN_PARAM_NAME)) {
				continue;
			}
			if (data.get(k) != null && data.get(k).trim().length() > 0) // 参数值为空，则不参与签名
				sb.append(k).append("=").append(data.get(k).trim()).append("&");
		}
		sb.append("key=").append(key);
		if (signType.toUpperCase().equals("SM3")) {
			return sm3Digest(sb.toString()).toUpperCase();
		} else if (signType.toUpperCase().equals("SHA1")) {
			return getSHA1(sb.toString());
		} else if (signType.toUpperCase().equals("MD5")) {
			return getMD5Str(sb.toString());
		} else {
			throw new Exception(String.format("Invalid sign_type: %s", signType));
		}
	}

	/**
	 * 
	 * @Title: 为请求参数增加merchantCode、nonce_str，sign参数
	 * @Description: 对参数加了secret，merchantCode，nonceStr后，进行国密SM3签名，增加sign字段
	 * @param reqData
	 * @param merchantCode
	 * @param secret
	 * @throws Exception
	 */
	public static void fillSignatureParam(Map<String, String> reqData, String merchantCode, String secret)
			throws Exception {
		reqData.put("merchantCode", merchantCode);
		reqData.put("nonceStr", UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32));
		reqData.put("sign", generateSignature(reqData, secret, "SM3"));
	}

	/**
	 * 判断签名是否正确，必须包含sign字段，否则返回false。
	 * 参数值为空字段不参与签名验证
	 * @param data     Map类型数据
	 * @param key      API密钥
	 * @param signType 签名方式:SM3，SHA1，MD5
	 * @return 签名是否正确
	 * @throws Exception
	 */
	public static boolean isSignatureValid(Map<String, String> data, String key, String signType) throws Exception {
		if (!data.containsKey(PAY_SIGN_PARAM_NAME)) {
			return false;
		}
		String sign = data.get(PAY_SIGN_PARAM_NAME);
		return generateSignature(data, key, signType).equals(sign);
	}

	/**
	 * 
	 * @Title: json对象转MAP
	 * @Description:
	 * @param jsonObj json对象
	 * @return Map<String, String>
	 */
	public static Map<String, String> jsonObjToMap(Object jsonObj) {
		ObjectMapper mapper = new ObjectMapper();
		mapper.setSerializationInclusion(Include.NON_NULL);
		try {
			return mapper.readValue(mapper.writeValueAsString(jsonObj), new TypeReference<Map<String, String>>() {
			});
		} catch (JsonProcessingException e) {
			e.printStackTrace();
			return new HashMap<String, String>();
		}

	}


	/**
	 * sm4加密整合
	 */

	private static final String ENCODING = "UTF-8";
	public static final String ALGORITHM_NAME = "SM4";
	// 加密算法/分组加密模式/分组填充方式
	// PKCS5Padding-以8个字节为一组进行分组加密
	// 定义分组加密模式使用：PKCS5Padding
	public static final String ALGORITHM_NAME_ECB_PADDING = "SM4/ECB/PKCS5Padding";

	/**
	 * 生成ECB暗号
	 * @explain ECB模式（电子密码本模式：Electronic codebook）
	 * @param algorithmName
	 *            算法名称
	 * @param mode
	 *            模式
	 * @param key
	 * @return
	 * @throws Exception
	 */
	private static Cipher generateEcbCipher(String algorithmName, int mode, byte[] key) throws Exception {
		Cipher cipher = Cipher.getInstance(algorithmName, BouncyCastleProvider.PROVIDER_NAME);
		Key sm4Key = new SecretKeySpec(key, ALGORITHM_NAME);
		cipher.init(mode, sm4Key);
		return cipher;
	}


	/**
	 * sm4加密
	 * @explain 加密模式：ECB
	 *          密文长度不固定，会随着被加密字符串长度的变化而变化
	 * @param hexKey
	 *            16进制密钥（忽略大小写）
	 * @param paramStr
	 *            待加密字符串
	 * @return 返回16进制的加密字符串
	 * @throws Exception
	 */
	public static String encryptEcb(String hexKey, String paramStr) throws Exception {
		String cipherText = "";
		// 16进制字符串-->byte[]
		byte[] keyData = ByteUtils.fromHexString(hexKey);
		// String-->byte[]
		byte[] srcData = paramStr.getBytes(ENCODING);
		// 加密后的数组
		byte[] cipherArray = encrypt_Ecb_Padding(keyData, srcData);
		// byte[]-->hexString
		cipherText = ByteUtils.toHexString(cipherArray);
		return cipherText;
	}

	/**
	 * 加密模式之Ecb
	 * @explain
	 * @param key
	 * @param data
	 * @return
	 * @throws Exception
	 */
	public static byte[] encrypt_Ecb_Padding(byte[] key, byte[] data) throws Exception {
		Cipher cipher = generateEcbCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.ENCRYPT_MODE, key);
		return cipher.doFinal(data);
	}

	/**
	 * sm4解密
	 * @explain 解密模式：采用ECB
	 * @param hexKey
	 *            16进制密钥
	 * @param cipherText
	 *            16进制的加密字符串（忽略大小写）
	 * @return 解密后的字符串
	 * @throws Exception
	 */
	public static String decryptEcb(String hexKey, String cipherText) throws Exception {
		// 用于接收解密后的字符串
		String decryptStr = "";
		// hexString-->byte[]
		byte[] keyData = ByteUtils.fromHexString(hexKey);
		// hexString-->byte[]
		byte[] cipherData = ByteUtils.fromHexString(cipherText);
		// 解密
		byte[] srcData = decrypt_Ecb_Padding(keyData, cipherData);
		// byte[]-->String
		decryptStr = new String(srcData, ENCODING);
		return decryptStr;
	}

	/**
	 * 解密
	 * @explain
	 * @param key
	 * @param cipherText
	 * @return
	 * @throws Exception
	 */
	public static byte[] decrypt_Ecb_Padding(byte[] key, byte[] cipherText) throws Exception {
		Cipher cipher = generateEcbCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.DECRYPT_MODE, key);
		return cipher.doFinal(cipherText);
	}

	/**
	 * 校验加密前后的字符串是否为同一数据
	 * @explain
	 * @param hexKey
	 *            16进制密钥（忽略大小写）
	 * @param cipherText
	 *            16进制加密后的字符串
	 * @param paramStr
	 *            加密前的字符串
	 * @return 是否为同一数据
	 * @throws Exception
	 */
	public static boolean verifyEcb(String hexKey, String cipherText, String paramStr) throws Exception {
		// 用于接收校验结果
		boolean flag = false;
		// hexString-->byte[]
		byte[] keyData = ByteUtils.fromHexString(hexKey);
		// 将16进制字符串转换成数组
		byte[] cipherData = ByteUtils.fromHexString(cipherText);
		// 解密
		byte[] decryptData = decrypt_Ecb_Padding(keyData, cipherData);
		// 将原字符串转换成byte[]
		byte[] srcData = paramStr.getBytes(ENCODING);
		// 判断2个数组是否一致
		flag = Arrays.equals(decryptData, srcData);
		return flag;
	}



//	public static void main(String[] args) {
//    	System.out.print(getMD5Str("123456"));
//	    String mode = "AES/CBC/PKCS7Padding";
//	    String key = "IPTLs90URRMZ0RV4XIICaQ==";
//	    String encryptedData = "bOQ5RKeckC9JC5wlCNXWkk2MPj82Rju99v9Z78/EwlwkUV8rtLJiiyulWWkeYMZRqgQIgn1dp1Lik44vCg2m1DQ+0g/DVJpnqhC/dUK/LAWAlvSJZVBWxGfzDjlaIoMyTgFfNTLNhVj09XnOc9Uom4nw6L2X/vAnqbYOYJ9MbSRMDa2mORnWgGjsaSMnMxANtV/752t/n8fEsQc3pxEd0w==";
//	    String iv = "sehcFvw8nEohnF52Z5a0Pg==";
//	    Base64.Decoder decoder = Base64.getDecoder();
//	    System.out.println(decrypt(mode, decoder.decode(encryptedData), decoder.decode(key), decoder.decode(iv), "UTF-8"));
//	    
//		System.out.println(DigestUtil.getMD5Str("123"));
//	}
}
