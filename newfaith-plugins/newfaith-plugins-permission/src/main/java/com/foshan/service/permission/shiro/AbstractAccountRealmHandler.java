package com.foshan.service.permission.shiro;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.subject.Subject;
import org.hibernate.query.Query;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Autowired;

import com.foshan.dao.IAccountDao;
import com.foshan.dao.IDepartmentDao;
import com.foshan.dao.IRoleDao;
import com.foshan.dao.IWxServiceDao;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.RoleForm;
import com.foshan.form.permission.request.AccountLoginReq;
import com.foshan.form.response.IResponse;
import com.foshan.util.ContextInfo;

/**
 * 登录鉴权处理抽象类，其实现类根据具体逻辑实现登录凭证处理和身份校验信息获取，实际逻辑处理器的实现所在包：com.foshan.service.shop.shiro.realmhandler.handler
 * <AUTHOR>
 *
 */
public abstract class AbstractAccountRealmHandler {
	
	@Autowired
	protected IAccountDao accountDao;
	@Autowired
	protected IDepartmentDao departmentDao;
	@Autowired
	protected IRoleDao roleDao;
	@Autowired
	protected ContextInfo contextInfo;
	@Autowired
	protected IWxServiceDao wxServiceDao;
	
	public abstract SimpleAuthenticationInfo handleAuthenticationInfo(AuthenticationToken token ,String realmName);
	public abstract IResponse login(AccountLoginReq req, HttpServletRequest request, Subject curUser);
	
	/**
	 * 根据公众用户id获取角色列表
	 * @param principalId-公众用户id
	 * @return 拼装的formlist
	 */
	public List<RoleForm> getRoleFormList(Integer principalId) {
		List<RoleForm> rfl = new ArrayList<RoleForm>();
		String roleSql = "SELECT r.`id` AS 'roleId', r.`roleName` AS 'roleName', r.`displayName` AS 'roleDisplayName' "
				+ "FROM t_member_role mr " + "  LEFT JOIN t_account a ON mr.`memberId` = a.`id` "
				+ "LEFT JOIN t_role r ON mr.`roleId`=r.`id` " 
				+ "WHERE a.userState = '" + EntityContext.RECORD_STATE_VALID + "' "
				+ "and a.`id` = '" + principalId + "'";
		@SuppressWarnings("rawtypes")
		Query query = roleDao.createSQLQuery(roleSql);
		@SuppressWarnings({ "deprecation", "rawtypes" })
		List resultList = query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
		@SuppressWarnings("unchecked")
		ArrayList<Map<String, Object>> roleList = (ArrayList<Map<String, Object>>) resultList.stream()
				.collect(Collectors.collectingAndThen(
						Collectors.toCollection(() -> new TreeSet<Map<String, Object>>(
								Comparator.comparing(m -> (String) ((Map) m).get("roleName")))),
						ArrayList::new));
		roleList.forEach(r -> {
			RoleForm rf = new RoleForm();
			rf.setRoleId((Integer) ((Map)r).get("roleId"));
			rf.setRoleName((String) ((Map)r).get("roleName"));
			rf.setDisplayName((String) ((Map)r).get("roleDisplayName"));
			rf.setPermissionList(null);
			rf.setPermissionGroupList(null);
			rfl.add(rf);
		});
		return rfl;
	}
}
