package com.foshan.service.permission.shiro.filter;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.foshan.entity.WhiteListEntity;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.util.IpV4Util;
import com.foshan.util.SpringHandler;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.authz.PermissionsAuthorizationFilter;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.query.Query;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;
import java.io.IOException;
import java.util.List;

/**
 * 白名单过滤器，ip所在白名单才能访问对应资源。白名单管理的接口不进行权限管理（不校验校验权限标志）
 * 根据白名单组id（groupid）区分不同对象的白名单，需要在配置文件配置filterChainDefinitions，指定白名单过滤的url
 * 配置例子(whitelistFilter[]的行)：
 * <bean id="shiroDefinitionsService"
 * 		class="com.foshan.service.permission.impl.ShiroDefinitionsServiceImpl">
 * 		<property name="filterChainDefinitions">
 * 			<value>
 * 				/login = anon
 * 				/accountLogin = anon
 * 				/logout = anon
 * 				/getVerifyCode = anon
 * 				/unauthorized = anon
 * 				/unLogin = anon
 * 				/member/getMemberAppToken = whitelistFilter[1]
 * 				/getLoginAuthQrCode = whitelistFilter[2]
 * 				/member/getStbMemberList = whitelistFilter[2]
 * 				/getAccountLoginTicket = whitelistFilter[2]
 * 			</value>
 * 		</property>
 * 	</bean>
 * <AUTHOR>
 *
 */
public class WhitelistFilter extends PermissionsAuthorizationFilter {

	private final static Logger logger = LoggerFactory.getLogger(WhitelistFilter.class);

	protected ObjectMapper mapper = new ObjectMapper();
	
	@Override
	@Transactional
	public boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue)
			throws IOException {
		String[] mValue = (String[]) mappedValue;
		Integer groupId = Integer.valueOf(mValue[0]);// 约定一个		
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
        Session session = sessionFactory.openSession();
        Query query = session.createQuery("from WhiteListEntity where groupId ='" + groupId + "'");
        List<WhiteListEntity> whiteList = query.getResultList();
		// 标志在不在ip白名单内
		boolean isPermit = false;
		String srcIP = IpV4Util.getIpAddr((HttpServletRequest) request);
		for (WhiteListEntity w : whiteList) {
			if ((IpV4Util.compareIpV4s(srcIP, w.getStartIP()) > -1)
					&& (IpV4Util.compareIpV4s(w.getEndIP(), srcIP) > -1)) {
				isPermit = true;
			}
		}
		logger.info(Thread.currentThread().getStackTrace()[1].getMethodName()+"=====ip【" + srcIP +"】被白名单（groupId=" + groupId+ "）"+"过滤，校验结果：" + isPermit);
		session.close();
		return isPermit;
	}

	@Override
	protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws IOException {
		mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
		mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		Subject subject = getSubject(request, response);
		GenericResponse res = new GenericResponse();
		response.setContentType("application/json;charset=UTF-8");
		res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
		res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
		response.getWriter().write(mapper.writeValueAsString(res));
		response.getWriter().flush();
		return false;
	}

}
