package com.foshan.service.permission.shiro;


import org.apache.shiro.config.Ini;
import org.springframework.beans.factory.FactoryBean;

import com.foshan.service.permission.IShiroDefinitionsService;

/**
 * 定义资源（url）权限映射链表
 * 存储访问资源所需的权限关系，shiro根据url进行拦截，然后进行后续处理
 * <AUTHOR>
 *
 */
public class CoreChainDefinitions implements FactoryBean<Ini.Section> {

	private String filterChainDefinitions;
	private IShiroDefinitionsService shiroDefinitionsService;
	
	public String getFilterChainDefinitions() {
		return filterChainDefinitions;
	}
	public void setFilterChainDefinitions(String filterChainDefinitions) {
		this.filterChainDefinitions = filterChainDefinitions;
	}
	
	public IShiroDefinitionsService getShiroDefinitionsService() {
		return shiroDefinitionsService;
	}
	public void setShiroDefinitionsService(IShiroDefinitionsService shiroDefinitionsService) {
		this.shiroDefinitionsService = shiroDefinitionsService;
	}

	@Override
	public Ini.Section getObject() throws Exception {

		Ini.Section section = shiroDefinitionsService.loadFilterChainDefinitions();
		return section;
	}

	@Override
	public Class<?> getObjectType() {
		return this.getClass();
	}

	@Override
	public boolean isSingleton() {
		return false;
	}

	
	
	
}
