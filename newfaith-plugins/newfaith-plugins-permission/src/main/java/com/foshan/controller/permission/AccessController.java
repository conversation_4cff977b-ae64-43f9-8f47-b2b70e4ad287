package com.foshan.controller.permission;

import java.awt.image.RenderedImage;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.foshan.util.CodeUtil;
import com.wf.captcha.ArithmeticCaptcha;
import com.wf.captcha.ChineseGifCaptcha;
import com.wf.captcha.GifCaptcha;
import com.wf.captcha.SpecCaptcha;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.Cache;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.permission.request.AccountLoginReq;
import com.foshan.form.permission.request.ConsumerReq;
import com.foshan.form.permission.request.GetAccountLoginTicketReq;
import com.foshan.form.permission.request.LoginReq;
import com.foshan.form.permission.response.AccountLoginRes;
import com.foshan.form.permission.response.GetAccountLoginTicketRes;
import com.foshan.form.permission.response.LoginRes;
import com.foshan.form.request.UserReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.permission.shiro.ShiroHazelcastCache;
import com.foshan.util.DigestUtil;
import com.foshan.util.QRCodeUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "融合平台--权限模块")
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
public class AccessController extends BasePermissionController {
	private final static Logger logger = LoggerFactory.getLogger(AccessController.class);

	// 登录
	@ApiOperation(value = "平台管理员登录(login)", httpMethod = "POST", notes = "平台管理员登录<p>1:userName和userPassword不能为空；")
	@PostMapping(value = "/login", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public LoginRes login(@RequestBody LoginReq req, HttpServletRequest request) throws JsonProcessingException {
		LoginRes res = (LoginRes) userAccessService.login(req, request);
		return res;
	}

	// 登录
	@ApiOperation(value = "会员登录(accountLogin)", httpMethod = "POST", notes = "公众用户登录。type（验证方式）必填，其他根据下属type填所需的参数：“phone-password”-电话+密码，“sms”-短信验证码，3-微信，4-第三方平台获取token，“miniprogram”-微信小程序，“tv”-电视客户端登录(需要通过手机登录后授权)，7-微信小程序企业号授权登录"
			+ "<p><p>phone-password：密码方式通过手机号和密码进行用户校验。phone、password必填，密码用sm3 hash。"
			+ "<p><p>sms-未实现：短信验证码方式，需要用户先调用获取短信接口获取验证码，然后通过手机号和短信验证码进行校验，phone、messageCode必填。"
			+ "<p><p>3-未实现：微信公众号网页授权方式，需要前端获取code后，通过code传到后台进行校验。后台通过code与微信公众号平台服务端通信后获取用户openid，再通过openid获取用户手机号，手机号和微信公众号openid作为校验凭证。需要用户先绑定openid才能正常登录，否则提示用户注册/绑定，wechatCode必填。"
			+ "<p><p>4-未实现：第三方平台获取token方式，前端通过调用第三方平台接口获取token接口，第三方平台通过调用我方后台接口获取token接口（此时如果用户未注册则自动注册）。前端将手机号和从第三方平台获取到的token传到后台进行校验，phone、appToken必填。"
			+ "<p><p>miniprogram：微信小程序授权登录，前端获取code后，通过code传到后台进行校验。如果参数带有加密数据和初始向量则进行用户自动注册/绑定（如果openid已经被其他电话号码绑定则不能绑定），后台在注册/绑定后继续完成登录流程。wechatCode必填。"
			+ "<p>微信小程序登录处理逻辑：1、直接登录（只传code），2、注册/关联小程序openid后登录（传code+加密数据和初始向量）\r\n"
			+ "先判断加密数据和初始向量是否非空，非空则判断是否执行注册后登录流程（需要后端配置是否自动注册标识），空则执行直接登录流程\r\n" + "<p>1、直接登录（只传code）\r\n" + "前端先向微信获取code，\r\n"
			+ "调用登录接口时传递code到后端，后端通过code获取用户小程序openid，\r\n"
			+ "后端根据小程序openid进行身份校验：先根据小程序openid查询用户数据，如果用户不存在则返回信息引导用户注册。\r\n"
			+ "查询得到的phone作为校验token的id，小程序openid作为校验token的password\r\n" + "<p>2、注册/关联小程序openid后登录（传code+加密数据和初始向量）\r\n"
			+ "前端先向微信获取code，加密数据和初始向量\r\n" + "调用登录接口时传递code到后端，后端通过code获取用户小程序openid，\r\n"
			+ "后端判断加密数据和初始向量非空则注册/关联小程序openid后登录，后端根据小程序openid进行身份校验：先根据小程序openid和微信返回的电话号码查询用户数据，判断用户属于未注册还是未关联小程序openid，如未注册则注册，如未关联则关联。\r\n"
			+ "最后将查询得到的phone作为校验token的id，小程序openid作为校验token的password"
			+ "<p><p>tv：电视客户端登录，登录票据获取接口只向电视客户端等内网终端提供，通过ip地址进行限制。前端先通过getAccountLoginTicket接口获取loginTicket，再通过本接口传入loginTicket进行校验，具体登录流程如下：机顶传入卡号后通过/getAccountLoginTicket接口获取loginTicket（两分钟内有效），然后根据loginTicket通过getLoginAuthQrCode接口获取授权二维码，向用户展示二维码；手机客户端登录后调用loginAuthentication接口进行授权；机顶盒再调用本接口进行登录验证，loginTicket必填。"
			+ "<p><p>7-未实现：微信小程序企业号授权登录。需要前端获取code后，通过code传到后台进行校验。wechatCode必填。")
    @PostMapping(value = "/accountLogin", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AccountLoginRes accountLogin(@RequestBody AccountLoginReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AccountLoginRes res = (AccountLoginRes) accountAccessService.login(req, request);
		return res;
	}

	// 注销
	@ApiOperation(value = "注销(logout)", httpMethod = "POST", notes = "注销接口")
	@PostMapping(value = "/logout", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse logout(HttpServletRequest request) throws JsonProcessingException {
		Subject curUser = SecurityUtils.getSubject();
		PrincipalCollection principals = curUser.getPrincipals();
		if (null != principals && !principals.isEmpty()) {
			@SuppressWarnings("unchecked")
			List<Object> principalList = principals.asList();
			Object principal = (null != principalList.get(1) ? principalList.get(1) : null);
			Cache cache =  hazelcastCacheManager.getCache("userObjectCache");
			cache.evict(principal);
		}
		String userName = (String) curUser.getPrincipal();
		curUser.logout();
		logger.info(userName + "用户注销");
		GenericResponse res = new GenericResponse(ResponseContext.RES_SUCCESS_CODE, userName + "已注销。");
		return res;
	}

	@GetMapping(value = "/unauthorized",produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse unauthorized(HttpServletRequest request) {
		GenericResponse res = new GenericResponse(ResponseContext.RES_PERM_UNAUTHORIZED_CODE,
				ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
		return res;
	}

	@GetMapping(value = "/unLogin", produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse unLogin(HttpServletRequest request, HttpServletResponse response) {
		GenericResponse res = new GenericResponse(ResponseContext.RES_PERM_UNLOGIN_CODE,
				ResponseContext.RES_PERM_UNLOGIN_INFO);
		return res;
	}

	@ApiOperation(value = "获取授权登录票据（getAccountLoginTicket）", httpMethod = "POST", notes = "获取授权登录二票据（有效时间2分钟），用于生成二维码，手机端登录后通过授权接口给电视端授权，手机端授权后才能根据登录票据通过校验。<p>smartcardId必填<p>")
	@PostMapping(value = "/getAccountLoginTicket",produces = MediaType.APPLICATION_JSON_VALUE)
	public GetAccountLoginTicketRes getAccountLoginTicket(@RequestBody GetAccountLoginTicketReq req,
			HttpServletRequest request, HttpServletResponse response) {

		GetAccountLoginTicketRes res = new GetAccountLoginTicketRes();
		HttpSession session = request.getSession();
		String state = "";
		try {
			state = DigestUtil.sm3Digest(session.toString());
		} catch (UnsupportedEncodingException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		String loginTicket = req.getSmartcardId() + state;
		ShiroHazelcastCache.loginTicketCache.put(loginTicket, Optional.ofNullable(null));
		res.setLoginTicket(loginTicket);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		logger.info(req.getSmartcardId() + "的loginTicket是：" + loginTicket);
		return res;
	}

	@ApiOperation(value = "tv端获取授权登录二维码（getLoginAuthQrCode）", httpMethod = "GET", notes = "电视端获取授权登录二维码（根据loginTicket生成），手机端登录后通过授权接口给电视端授权，手机端授权后电视端才能通过校验。<p>loginTicket、smartcardId必填<p>")
	@GetMapping(value = "/getLoginAuthQrCode",produces = MediaType.IMAGE_JPEG_VALUE)
	public void getLoginAuthQrCode(GetAccountLoginTicketReq req, HttpServletRequest request,
			HttpServletResponse response) {
		response.setHeader("Pragma", "no-cache");
		response.setHeader("Cache-Control", "no-cache");
		response.setDateHeader("Expires", -1);
		response.setContentType("image/jpeg");

		ServletOutputStream sos;
		try {
			sos = response.getOutputStream();
			String url = contextInfo.getLoginAuthenticationUrlHtml() + req.getLoginTicket() + "&smartcardId="
					+ req.getSmartcardId();
			try {
				ImageIO.write(QRCodeUtil.createImage(url, "", false), "jpeg", sos);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			sos.close();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	@ApiOperation(value = "授权登录（loginAuthentication）", httpMethod = "POST", notes = "授权登录，参数{}")
	@PostMapping(value = "/loginAuthentication",  consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AccountLoginRes loginAuthentication(@RequestBody AccountLoginReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AccountLoginRes res = (AccountLoginRes) accountAccessService.loginAuthentication(req, request);
		return res;
	}
	
	// 修改用户密码
	@ApiOperation(value = "修改用户密码(modifyPassword)", httpMethod = "POST", notes = "修改用户密码接口<p>1:consumerId和type不能为空;")
	@ResponseBody
	@PostMapping(value = "/modifyPassword", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyPassword(@RequestBody ConsumerReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) userAccessService.modifyPassword(req);
		return res;
	}

	// 修改用户密码
	@ApiOperation(value = "修改用户密码(changePassword)", httpMethod = "POST", notes = "修改用户密码接口,要填原密码<p>1:consumerId和type不能为空;")
	@ResponseBody
	@PostMapping(value = "/changePassword", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse changePassword(@RequestBody ConsumerReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) userAccessService.changePassword(req);
		return res;
	}
	
	// 解锁用户
	@ApiOperation(value = "解锁用户(unlockUser)", httpMethod = "POST", notes = "解锁用户接口<p>1:userName不能为空;")
	@PostMapping(value = "/unlockUser",consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse unlockUser(@RequestBody UserReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) userAccessService.unlockUser(req);
		return res;
	}

	// 获取图片验证码
	@ApiOperation(value = "获取图片验证码(GetVerifyCode)", httpMethod = "POST", notes = "获取图片验证码")
	@RequestMapping(value = "/getVerifyCode", method = { RequestMethod.POST,RequestMethod.GET })
	@ResponseBody
	public void getVerifyCode(HttpServletRequest request, HttpServletResponse response) {
		long start = System.currentTimeMillis();

		// 调用工具类生成的验证码和验证码图片
		Map<String, Object> codeMap = CodeUtil.generateCodeAndPic();

		// 算术类型（长，宽，几个数的运算）
		ArithmeticCaptcha captcha = new ArithmeticCaptcha(110, 30);
		captcha.setLen(2);  // 几位数运算，默认是两位
//		System.out.println(captcha.getArithmeticString()); // 获取运算公式 5x0+5=?
//		System.out.println(captcha.text()); // 获取验证码结果
		logger.info("验证公式是："+ captcha.getArithmeticString() + "验证码是：" + captcha.text());
		// 将四位数字的验证码保存到Session中。
		HttpSession session = request.getSession();
//		session.setAttribute("imageCode", codeMap.get("code").toString());

		// 将运算公式的结果保存到Session中。
		session.setAttribute("imageCode", captcha.text());

		response.setHeader("Pragma", "no-cache");
		response.setHeader("Cache-Control", "no-cache");
		response.setDateHeader("Expires", -1);
		response.setContentType("image/jpg");

		ServletOutputStream sos;
		try {

			// 图片英语字母数字类型
//			SpecCaptcha captcha = new SpecCaptcha(90, 20);

			// 英语字母数字gif类型的
//			GifCaptcha captcha = new GifCaptcha(130, 48,5);

			// 中文类型的
			//ChineseCaptcha captcha = new ChineseCaptcha(130, 48,3);

			// 中文gif类型
//			ChineseGifCaptcha captcha = new ChineseGifCaptcha(130, 48,4);
			captcha.out(response.getOutputStream());

// 原始简单图片验证码
//			sos = response.getOutputStream();
//			ImageIO.write((RenderedImage) codeMap.get("codePic"), "jpeg", sos);
//			sos.close();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		long end = System.currentTimeMillis();
		logger.info(
				"==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==处理时间:" + (end - start) + "毫秒!");

	}


	public static void main(String[] args) {
//		ArithmeticCaptcha captcha = new ArithmeticCaptcha(130, 48);
		SpecCaptcha captcha = new SpecCaptcha(90, 20);;
		captcha.setLen(3);  // 几位数运算，默认是两位
//		System.out.println(captcha.getArithmeticString()); // 获取运算公式 5x0+5=?
		System.out.println(captcha.text()); // 获取验证码结果

		// 图片英语字母数字类型
		//SpecCaptcha captcha = new SpecCaptcha(130, 48);

		// 英语字母数字gif类型的
		//GifCaptcha captcha = new GifCaptcha(130, 48,4);

		// 中文类型的
		//ChineseCaptcha captcha = new ChineseCaptcha(130, 48,3);

		// 中文gif类型
		//ChineseGifCaptcha captcha = new ChineseGifCaptcha(130, 48,4);

	}

}
