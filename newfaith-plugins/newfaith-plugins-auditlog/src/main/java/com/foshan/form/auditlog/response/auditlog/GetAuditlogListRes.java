package com.foshan.form.auditlog.response.auditlog;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.auditlog.AuditlogForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "获取审计日志列表返回对象(GetAuditlogListRes)")
@JsonInclude(Include.NON_NULL)
public class GetAuditlogListRes extends BasePageResponse {

	/**
	 * 审计日志响应
	 */
	private static final long serialVersionUID = -8754776485896549626L;

	@ApiModelProperty(value = "审计日志列表")
	private List<AuditlogForm> auditlogList;

	public List<AuditlogForm> getAuditlogList() {
		return auditlogList;
	}

	public void setAuditlogList(List<AuditlogForm> auditlogList) {
		this.auditlogList = auditlogList;
	}

}
