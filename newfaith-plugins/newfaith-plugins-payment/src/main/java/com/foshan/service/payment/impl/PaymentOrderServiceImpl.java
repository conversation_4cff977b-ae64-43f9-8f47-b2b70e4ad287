package com.foshan.service.payment.impl;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

import com.foshan.dao.generic.Page;
import com.foshan.domain.payment.PaymentResultNotifyMessage;
import com.foshan.domain.payment.RefundResultNotifyMessage;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.payment.PaymentMerchantEntity;
import com.foshan.entity.payment.PaymentOrderEntity;
import com.foshan.entity.payment.PaymentRefundRecordEntity;
import com.foshan.entity.payment.PaymentSessionEntity;
import com.foshan.form.payment.PaymentOrderForm;
import com.foshan.form.payment.request.PaymentOrderReq;
import com.foshan.form.payment.request.PaymentQueryReq;
import com.foshan.form.payment.request.PaymentSubmitReq;
import com.foshan.form.payment.response.PaymentErrorView;
import com.foshan.form.payment.response.paymentOrder.AddPaymentOrderRes;
import com.foshan.form.payment.response.paymentOrder.GetPaymentOrderInfoRes;
import com.foshan.form.payment.response.paymentOrder.GetPaymentOrderListRes;
import com.foshan.form.payment.response.paymentOrder.ModifyPaymentOrderRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.plugin.PaymentPlugin;
import com.foshan.service.annotation.Audit;
import com.foshan.service.payment.IPaymentOrderService;
import com.foshan.service.payment.IPaymentPluginService;
import com.foshan.service.payment.IPaymentSessionService;
import com.foshan.util.CodeUtil;
import com.foshan.util.DateUtil;
import com.foshan.util.DigestUtil;
import com.foshan.util.HttpClientUtil;
import com.hazelcast.collection.IQueue;
import com.hazelcast.spring.cache.HazelcastCacheManager;

@Transactional
@Service("paymentOrderService")
public class PaymentOrderServiceImpl extends GenericPaymentService implements IPaymentOrderService {

	@Resource(name = "paymentSessionService")
	protected IPaymentSessionService paymentSessionService;
	@Resource(name = "paymentPluginService")
	protected IPaymentPluginService paymentPluginService;
	@Resource
	private HazelcastCacheManager cacheManager;

	private final static Logger logger = LoggerFactory.getLogger(PaymentOrderServiceImpl.class);

	@Override
	public IResponse getPaymentOrderList(PaymentOrderReq req) {
		GetPaymentOrderListRes res = new GetPaymentOrderListRes();
		Page<PaymentOrderEntity> page = new Page<PaymentOrderEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from PaymentOrderEntity a where 1=1 ");
		hql.append("");
		hql.append(" ORDER BY a.id desc");
		page = paymentOrderDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			PaymentOrderForm paymentOrderForm = new PaymentOrderForm();
			paymentOrderForm.setPaymentOrderId(o.getId());
			paymentOrderForm.setCancelReason(o.getCancelReason());
			paymentOrderForm.setDeveloper(o.getDeveloper());
			paymentOrderForm.setDiscountAmount(null != o.getDiscountAmount() ? o.getDiscountAmount().toString() : "");
			paymentOrderForm.setInvoiceTitle(o.getInvoiceTitle());
			paymentOrderForm.setIsReminder(o.getIsReminder());
			paymentOrderForm.setNeedInvoice(o.getNeedInvoice());
			paymentOrderForm.setOrderCode(o.getOrderCode());
			paymentOrderForm.setOrderMemo(o.getOrderMemo());
			paymentOrderForm.setOrderState(o.getOrderState());
			paymentOrderForm
					.setOrderTotalAmount(null != o.getOrderTotalAmount() ? o.getOrderTotalAmount().toString() : "");
			paymentOrderForm.setPaidAmount(null != o.getPaidAmount() ? o.getPaidAmount().toString() : "");
			paymentOrderForm.setPaytaxNo(o.getPaytaxNo());
			paymentOrderForm.setProductOrderType(o.getProductOrderType());
			paymentOrderForm.setRefundAmount(null != o.getRefundAmount() ? o.getRefundAmount().toString() : "");
			paymentOrderForm
					.setReminderTime(null != o.getReminderTime() ? DateUtil.formatLongFormat(o.getReminderTime()) : "");
			paymentOrderForm.setSmartcardId(o.getSmartcardId());

			res.getPaymentOrderList().add(paymentOrderForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse addPaymentOrder(PaymentOrderReq req) {
		AddPaymentOrderRes res = new AddPaymentOrderRes();
		PaymentOrderEntity paymentOrder = new PaymentOrderEntity();

		paymentOrder.setCancelReason(req.getCancelReason());
		paymentOrder.setCompanyName(req.getCompanyName());
		paymentOrder.setCouponDiscountAmount(
				StringUtils.isNotEmpty(req.getCouponDiscountAmount()) ? new BigDecimal(req.getCouponDiscountAmount())
						: null);
		paymentOrder.setDeveloper(req.getDeveloper());
		paymentOrder.setDiscountAmount(
				StringUtils.isNotEmpty(req.getDiscountAmount()) ? new BigDecimal(req.getDiscountAmount()) : null);
		paymentOrder.setInvoiceTitle(req.getInvoiceTitle());
		paymentOrder.setIsReminder(req.getIsReminder());
		paymentOrder.setNeedInvoice(req.getNeedInvoice());
		paymentOrder.setOrderCode(req.getOrderCode());
		paymentOrder.setOrderMemo(req.getOrderMemo());
		paymentOrder.setOrderState(req.getOrderState());
		paymentOrder.setOrderTotalAmount(
				StringUtils.isNotEmpty(req.getOrderTotalAmount()) ? new BigDecimal(req.getOrderTotalAmount()) : null);
		paymentOrder.setPaidAmount(
				StringUtils.isNotEmpty(req.getPaidAmount()) ? new BigDecimal(req.getPaidAmount()) : null);
		paymentOrder.setPaytaxNo(req.getPaytaxNo());
		paymentOrder.setProductOrderType(req.getProductOrderType());
		paymentOrder.setRefundAmount(
				StringUtils.isNotEmpty(req.getRefundAmount()) ? new BigDecimal(req.getRefundAmount()) : null);
		try {
			paymentOrder.setReminderTime(StringUtils.isNotEmpty(req.getReminderTime())
					? new Timestamp(DateUtil.parseLongFormat(req.getReminderTime()).getTime())
					: null);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		paymentOrder.setSmartcardId(req.getSmartcardId());

		paymentOrderDao.save(paymentOrder);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		return res;
	}

	@Override
	public IResponse modifyPaymentOrder(PaymentOrderReq req) {
		ModifyPaymentOrderRes res = new ModifyPaymentOrderRes();
		if (null != req.getPaymentOrderId()) {
			PaymentOrderEntity paymentOrder = paymentOrderDao.get(req.getPaymentOrderId());
			if (null != paymentOrder) {
				paymentOrder.setCancelReason(req.getCancelReason());
				paymentOrder.setCompanyName(req.getCompanyName());
				paymentOrder.setCouponDiscountAmount(StringUtils.isNotEmpty(req.getCouponDiscountAmount())
						? new BigDecimal(req.getCouponDiscountAmount())
						: null);
				paymentOrder.setDeveloper(req.getDeveloper());
				paymentOrder.setDiscountAmount(
						StringUtils.isNotEmpty(req.getDiscountAmount()) ? new BigDecimal(req.getDiscountAmount())
								: null);
				paymentOrder.setInvoiceTitle(req.getInvoiceTitle());
				paymentOrder.setIsReminder(req.getIsReminder());
				paymentOrder.setNeedInvoice(req.getNeedInvoice());
				paymentOrder.setOrderCode(req.getOrderCode());
				paymentOrder.setOrderMemo(req.getOrderMemo());
				paymentOrder.setOrderState(req.getOrderState());
				paymentOrder.setOrderTotalAmount(
						StringUtils.isNotEmpty(req.getOrderTotalAmount()) ? new BigDecimal(req.getOrderTotalAmount())
								: null);
				paymentOrder.setPaidAmount(
						StringUtils.isNotEmpty(req.getPaidAmount()) ? new BigDecimal(req.getPaidAmount()) : null);
				paymentOrder.setPaytaxNo(req.getPaytaxNo());
				paymentOrder.setProductOrderType(req.getProductOrderType());
				paymentOrder.setRefundAmount(
						StringUtils.isNotEmpty(req.getRefundAmount()) ? new BigDecimal(req.getRefundAmount()) : null);
				try {
					paymentOrder.setReminderTime(StringUtils.isNotEmpty(req.getReminderTime())
							? new Timestamp(DateUtil.parseLongFormat(req.getReminderTime()).getTime())
							: null);
				} catch (ParseException e) {
					e.printStackTrace();
				}
				paymentOrder.setSmartcardId(req.getSmartcardId());
				res.setPaymentOrderId(paymentOrder.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse deletePaymentOrder(PaymentOrderReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getPaymentOrderId()) {
			PaymentOrderEntity paymentOrder = paymentOrderDao.get(req.getPaymentOrderId());
			if (null != paymentOrder) {
				paymentOrderDao.deleteById(req.getPaymentOrderId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getPaymentOrderInfo(PaymentOrderReq req) {
		GetPaymentOrderInfoRes res = new GetPaymentOrderInfoRes();
		if (null != req.getPaymentOrderId()) {
			PaymentOrderEntity paymentOrder = paymentOrderDao.get(req.getPaymentOrderId());
			if (null != paymentOrder) {
				PaymentOrderForm paymentOrderForm = new PaymentOrderForm();
				paymentOrderForm.setPaymentOrderId(paymentOrder.getId());
				paymentOrderForm.setCancelReason(paymentOrder.getCancelReason());
				paymentOrderForm.setCompanyName(paymentOrder.getCompanyName());
				paymentOrderForm.setDeveloper(paymentOrder.getDeveloper());
				paymentOrderForm.setDiscountAmount(
						null != paymentOrder.getDiscountAmount() ? paymentOrder.getDiscountAmount().toString() : "");
				paymentOrderForm.setInvoiceTitle(paymentOrder.getInvoiceTitle());
				paymentOrderForm.setIsReminder(paymentOrder.getIsReminder());
				paymentOrderForm.setNeedInvoice(paymentOrder.getNeedInvoice());
				paymentOrderForm.setOrderCode(paymentOrder.getOrderCode());
				paymentOrderForm.setOrderMemo(paymentOrder.getOrderMemo());
				paymentOrderForm.setOrderState(paymentOrder.getOrderState());
				paymentOrderForm.setOrderTotalAmount(
						null != paymentOrder.getOrderTotalAmount() ? paymentOrder.getOrderTotalAmount().toString()
								: "");
				paymentOrderForm.setPaidAmount(
						null != paymentOrder.getPaidAmount() ? paymentOrder.getPaidAmount().toString() : "");
				paymentOrderForm.setPaytaxNo(paymentOrder.getPaytaxNo());
				paymentOrderForm.setProductOrderType(paymentOrder.getProductOrderType());
				paymentOrderForm.setRefundAmount(
						null != paymentOrder.getRefundAmount() ? paymentOrder.getRefundAmount().toString() : "");
				paymentOrderForm.setReminderTime(null != paymentOrder.getReminderTime()
						? DateUtil.formatLongFormat(paymentOrder.getReminderTime())
						: "");
				paymentOrderForm.setSmartcardId(paymentOrder.getSmartcardId());
				res.setPaymentOrderForm(paymentOrderForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Audit(operate = "支付插件 -发起支付")
	@Override
	public ModelAndView pay(PaymentSubmitReq req) throws Exception {

		ModelAndView modelAndView = new ModelAndView();
		// 读取商户号
		String merchantCode = req.getMerchantCode();
		if (StringUtils.isEmpty(req.getMerchantCode())) {
			return fillJsonView(new GenericResponse(ResponseContext.RES_NULL_ERROR_CODE,
					ResponseContext.RES_NULL_ERROR_INFO + "商户号不能为空！"));
		}

		if (StringUtils.isEmpty(req.getPaymentScene())) {
			return fillJsonView(new GenericResponse(ResponseContext.RES_NULL_ERROR_CODE,
					ResponseContext.RES_NULL_ERROR_INFO + "支付场景不能为空！"));
		}

		// 查询商户信息
		Object merchantObj = paymentMerchantDao
				.findUnique("from PaymentMerchantEntity where merchantCode = '" + merchantCode + "'");
		if (null == merchantObj) {
			return fillJsonView(new GenericResponse(ResponseContext.RES_DATA_NULL_CODE,
					ResponseContext.RES_DATA_NULL_INFO + "找不到该商户！"));
		}
		PaymentMerchantEntity merchantEntity = (PaymentMerchantEntity) merchantObj;
		if (merchantEntity.getStatus().intValue() != 1) {
			return fillJsonView(new GenericResponse(ResponseContext.RES_PERM_LOCK_CODE,
					ResponseContext.RES_PERM_LOCK_INFO + "该商户已经失效！"));
		}
		// 获取secret
		// 验收报文签名
		Map<String, String> reqMap = DigestUtil.jsonObjToMap(req);
		boolean signatureValid = false;
		try {
			signatureValid = DigestUtil.isSignatureValid(reqMap, merchantEntity.getSecret(), "SM3");
		} catch (Exception e) {
			logger.error("[{}]验证支付请求消息的签名时发生异常！{}", req.getClientTradeNo(), e);
			return fillJsonView(new GenericResponse(ResponseContext.RES_INVALID_PARAM_CODE,
					ResponseContext.RES_INVALID_PARAM_INFO + "验证支付请求消息的签名时发生异常！"));
		}

		if (signatureValid == false) {
			logger.error("[{}]验证支付请求消息的签名失败！", req.getClientTradeNo());
			return fillJsonView(new GenericResponse(ResponseContext.RES_INVALID_PARAM_CODE,
					ResponseContext.RES_INVALID_PARAM_INFO + "验证支付请求消息的签名失败！"));
		}

		// 创建本地订单
		PaymentOrderEntity paymentOrder = new PaymentOrderEntity();

		// 计算订单超时时间
		Date now = new Date();
		paymentOrder.setCreateTime(new Timestamp(now.getTime()));

		paymentOrder.setOrderCode(CodeUtil.getSNCode("PO"));
		String orderMemo = URLDecoder.decode((StringUtils.isEmpty(req.getOrderMemo()) ? "" : req.getOrderMemo()),
				"UTF-8");
		paymentOrder.setOrderMemo(orderMemo);
		paymentOrder.setOrderState(0);
		paymentOrder.setOrderTotalAmount(req.getOrderTotalAmount());
		paymentOrder.setProductOrderType(req.getOrderType() == null ? 0 : req.getOrderType());
		paymentOrder.setPaymentMerchant(merchantEntity);
		// 创建支付平台订单
		paymentOrderDao.save(paymentOrder);

		// 构造返回结果（签名）

		PaymentPlugin paymentPlugin = paymentPluginService.getPaymentPlugin(paymentOrder, req.getPaymentPluginId());
		if (paymentPlugin == null || !paymentPlugin.getIsEnabled()) {
			return fillJsonView(new GenericResponse(ResponseContext.RES_DATA_NULL_CODE,
					ResponseContext.RES_DATA_NULL_INFO + "没有找到所需的支付插件或支付插件状态不可用"));
		}

		PaymentSessionEntity paymentSession = paymentSessionService.addPaymentSession(paymentPlugin,
				new PaymentSessionEntity.OrderPaymentLineItem(paymentOrder), req.getReturnUrl(), req.getNotifyUrl(),
				req.getClientTradeNo(), paymentOrder, req.getPaymentScene());

		paymentPlugin.payHandle(paymentPlugin, paymentSession, req.getPaymentScene(), req.getClientIp(), orderMemo,
				getParam(req), req.getAppId(), modelAndView);

		TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
			@Override
			public void afterCommit() {
				// 事务提交后，支付结果放入队列异步通知。
				if (null != modelAndView.getModelMap().get("noNotify")) {
					IQueue<PaymentResultNotifyMessage> queue = cacheManager.getHazelcastInstance()
							.getQueue("paymentResultNotifyQueue");

					PaymentResultNotifyMessage notifyMessage = new PaymentResultNotifyMessage(paymentSession.getSn(),
							modelAndView.getModelMap(), new Date());
					try {

						queue.put(notifyMessage);
						logger.info("paymentSesstion.success:" + paymentSession.getIsSuccess() + ",支付结果：{}，成功放入异步通知队列！",
								modelAndView.getModelMap().toString());
					} catch (InterruptedException e) {
						// 支付结果通知失败
						logger.error("支付结果：{}，放入异步通知队列失败！", modelAndView.getModelMap().toString());
					}
				}

			}
		});

		return modelAndView;
	}

	@Audit(operate = "支付插件 -处理第三方支付结果通知")
	@Override
	public ModelAndView afterPay(String psSn, String param, HttpServletRequest request) throws Exception {
		String redirectUrl = contextInfo.getWxH5PaymentCallBackUrl() == null ? ""
				: contextInfo.getWxH5PaymentCallBackUrl();
		PaymentErrorView errorView = new PaymentErrorView("1049", "支付过程发生错误", redirectUrl + "?orderState=0");
		PaymentSessionEntity paymentSession = paymentSessionService.findBySn(psSn);
		if (paymentSession == null) {
			logger.info("[{}]回调找不到对应的paymentSessionSn", psSn);
			return new ModelAndView(errorView);
		}
		paymentSession.setOutNotifyTime(new Timestamp(new Date().getTime()));
		String paymentPluginId = paymentSession.getPaymentPluginId();
		PaymentPlugin paymentPlugin = StringUtils.isNotEmpty(paymentPluginId)
				? paymentPluginService.getPaymentPlugin(paymentSession.getOrder(), paymentPluginId)
				: null;
		if (paymentPlugin == null) {
			logger.info("[{}]没有找到的支付插件{}", psSn, paymentPluginId);
			return new ModelAndView(errorView);
		} else if (BooleanUtils.isNotTrue(paymentPlugin.getIsEnabled())) {
			logger.info("[{}]支付插件{}状态为不可用", psSn, paymentPluginId);
			return paymentPlugin.getProcessResultView(false);
		} else if (paymentSession.getIsSuccess()) {
			logger.info("[{}]已经完成支付，不需要再进行业务处理，再次向第三方发送收到支付通知", psSn);
			return paymentPlugin.getProcessResultView(true);
		}

		PaymentOrderEntity order = paymentSession.getOrder();

		if (order == null) {
			logger.error("[{}]找不到对应的订单！", psSn);
			return paymentPlugin.getProcessResultView(false);
		}

		String localOutTradeNo = paymentSession.getSn();
		// 验证请求报文
		Map<String, String> reqMap = new HashMap<String, String>();
		boolean isVerifyPass = paymentPlugin.verifyNotifyData(paymentSession, reqMap, request);
		boolean isPaySuccess = false;
		// 支付结果通知报文验证
		if (isVerifyPass) {
			// 区分是否为来自支付宝的退款结果通知
			if (StringUtils.isNotEmpty(reqMap.get("refund_fee")) && StringUtils.isNotEmpty(reqMap.get("out_biz_no"))) {
				// 通知为支付宝退款结果通知
				String refundSn = reqMap.get("out_biz_no");
				logger.info("[{}]收到支付宝退款结果通知报文，退款号为:{}", psSn, refundSn);
				PaymentRefundRecordEntity refundEntity = paymentRefundRecordDao
						.findUnique("select e from PaymentRefundRecordEntity e where e.sn ='" + refundSn + "'");
				if (refundEntity != null
						&& refundEntity.getRefundStatus().intValue() == EntityContext.REFUND_APPLY_STATE_COMMIT) {

					if (paymentPlugin.refundQuery(refundEntity) == 2) {
						TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
							@Override
							public void afterCommit() {
								// 事务提交后，支付结果放入队列异步通知。
								IQueue<RefundResultNotifyMessage> queue = cacheManager.getHazelcastInstance()
										.getQueue("refundResultNotifyQueue");

								RefundResultNotifyMessage notifyMessage = new RefundResultNotifyMessage(refundSn, null,
										new Date());
								try {
									queue.put(notifyMessage);
									logger.info("退款结果：{}，成功放入异步通知队列！", refundSn);
								} catch (InterruptedException e) {
									// 支付结果通知失败
									logger.error("退款结果：{}，放入异步通知队列失败！", refundSn);
								}
							}
						});
					}
				}
				logger.info("[{}]完成支付宝退款结果通知报文处理！", psSn);
			} else {
				// 通知为支付结果通知
				isPaySuccess = paymentPlugin.isPaySuccess(paymentPlugin, paymentSession);
				// 支付结果通知报文验证通过，进行订单状态信息更新
				if (isPaySuccess) {
					// 判断这个支付结果通知信息是已经处理过且已经完成支付的，不做收款处理
					if (reqMap.get("out_trade_no").equals(localOutTradeNo)
							&& paymentSession.getOrder().getAmountPayable().compareTo(BigDecimal.ZERO) <= 0) {
						logger.warn("[{}]订单sn[{}]已完成收款,且第三方系统支付单号[{}]的支付结果已经处理", psSn,
								paymentSession.getOrder().getOrderCode(), paymentSession.getOutTradeNo());

					} else {
						// 该支付结果通知信息款处理过且未完成收款的，进入收款处理。
						paymentSessionService.handle(paymentSession);

						// 知会业务系统
						Map<String, String> modelMap = new TreeMap<>();
						if (paymentSession.getIsSuccess() == true) {
							modelMap.put("returnCode", "SUCCESS");

							// nonceStr 要与下单返回结果中的一致
							modelMap.put("amount", order.getPaymentRecord().getAmount().toString());
						} else {
							modelMap.put("returnCode", "FAILED");
						}
						modelMap.put("clientTradeNo", paymentSession.getClientTradeNo());
						modelMap.put("paymentSessionSn", paymentSession.getSn());
						modelMap.put("outTradeNo", paymentSession.getOutTradeNo());

						DigestUtil.fillSignatureParam(modelMap, order.getPaymentMerchant().getMerchantCode(),
								order.getPaymentMerchant().getSecret());

						String jsonstr = JSONObject.valueToString(modelMap);
						String post;
						try {
							logger.info("发送支付结果报文到业务系统：");
							post = HttpClientUtil.jsonPost(paymentSession.getNotifyUrl(), "UTF-8", jsonstr, null);
							logger.info("业务系统响应支付结果通知报文：{}", post);
						} catch (Exception e) {
							throw new RuntimeException(e);
						}
						paymentSession.setNotifyTime(new Timestamp(System.currentTimeMillis()));
						paymentSession.setNotifyNumberOfTimes(paymentSession.getNotifyNumberOfTimes() + 1);
						if (post.toUpperCase().contains("SUCCESS")) {
							paymentSession.setNotifyResult(true);
						}
					}
				}
			}
		}
		ModelAndView modelAndView = new ModelAndView();// 回复收到支付通知成功报文
		paymentPlugin.afterPayHandle(paymentPlugin, paymentSession, isVerifyPass, reqMap, order.getOrderMemo(), param,
				isPaySuccess, request, modelAndView);

		return modelAndView;
	}

	@Override
	public GenericResponse query(PaymentQueryReq req) {
		GenericResponse res = new GenericResponse();

		// 读取商户号
		String merchantCode = req.getMerchantCode();
		if (StringUtils.isEmpty(req.getMerchantCode())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "商户号不能为空！");
			return res;
		}

		if (StringUtils.isEmpty(req.getPaymentSessionCode())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "paymentSessionCode不能为空！");
			return res;
		}

		// 查询商户信息
		Object merchantObj = paymentMerchantDao
				.findUnique("from PaymentMerchantEntity where merchantCode = '" + merchantCode + "'");
		if (null == merchantObj) {
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "找不到该商户！");
			return res;
		}
		PaymentMerchantEntity merchantEntity = (PaymentMerchantEntity) merchantObj;
		if (merchantEntity.getStatus().intValue() != 1) {
			res.setRet(ResponseContext.RES_PERM_LOCK_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_LOCK_INFO + "该商户已经失效！");
			return res;
		}

		// 获取secret
		// 验收报文签名
		Map<String, String> reqMap = DigestUtil.jsonObjToMap(req);
		boolean signatureValid = false;
		try {
			signatureValid = DigestUtil.isSignatureValid(reqMap, merchantEntity.getSecret(), "SM3");
		} catch (Exception e) {
			logger.error("[{}]验证支付请求消息的签名时发生异常！{}", req.getPaymentSessionCode(), e);
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "验证查询支付结果请求消息的签名时发生异常！");
			return res;
		}

		if (signatureValid == false) {
			logger.error("[{}]验证支付请求消息的签名失败！", req.getPaymentSessionCode());
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO + "验证查询支付结果请求消息签名失败！");
			return res;
		}

		String hql = "select s from PaymentSessionEntity s where s.sn = :sn";
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("sn", req.getPaymentSessionCode());
		Object paymentSessionObj = paymentSessionDao.findUnique(hql, map);
		if (null != paymentSessionObj) {
			PaymentSessionEntity paymentSession = (PaymentSessionEntity) paymentSessionObj;

			PaymentPlugin paymentPlugin = paymentPluginService.getPaymentPlugin(paymentSession.getOrder(),
					paymentSession.getPaymentPluginId());
			if (paymentPlugin == null || !paymentPlugin.getIsEnabled()) {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "没有找到所需的支付插件或支付插件状态不可用");
				return res;
			}

			boolean isPaySuccess;
			try {
				isPaySuccess = paymentPlugin.isPaySuccess(paymentPlugin, paymentSession);
				// 支付结果通知报文验证通过，进行订单状态信息更新
				if (isPaySuccess) {
					// 如果支付完成，一直未收到支付结果通知，重新通知业务系统
					if (paymentSession.getIsSuccess() == false) {
						if (paymentSession.getOrder().getAmountPayable().compareTo(BigDecimal.ZERO) <= 0) {
							logger.warn("[{}]订单sn[{}]已完成收款,且第三方系统支付单号[{}]的支付结果已经处理", paymentSession.getSn(),
									paymentSession.getOrder().getOrderCode(), paymentSession.getOutTradeNo());
						} else {
							// 该支付结果通知信息款处理过且未完成收款的，进入收款处理。
							paymentSessionService.handle(paymentSession);
						}
					} else {
						res.setRet(ResponseContext.RES_SUCCESS_CODE);
						res.setRetInfo("成功支付，并已收到支付结果！");
					}

					Map<String, String> modelMap = new TreeMap<>();
					// if (paymentSession.getIsSuccess() == true) {
					modelMap.put("returnCode", "SUCCESS");

					// nonceStr 要与下单返回结果中的一致
					modelMap.put("amount", paymentSession.getOrder().getPaymentRecord().getAmount().toString());
					modelMap.put("clientTradeNo", paymentSession.getClientTradeNo());
					modelMap.put("paymentSessionSn", paymentSession.getSn());
					modelMap.put("outTradeNo", paymentSession.getOutTradeNo());
					DigestUtil.fillSignatureParam(modelMap, merchantCode, merchantEntity.getSecret());

					String jsonstr = JSONObject.valueToString(modelMap);
					String post;
					try {
						logger.info("发送支付结果报文到业务系统：");
						post = HttpClientUtil.jsonPost(paymentSession.getNotifyUrl(), "UTF-8", jsonstr, null);
						logger.info("业务系统响应支付通知报文：{}", post);
					} catch (Exception e) {
						// paymentSession.setNotifyResult(true);
						res.setRet(ResponseContext.RES_SUCCESS_CODE);
						res.setRetInfo("支付成功，重新通知业务系统时发生异常！");
						logger.info("[{}]支付成功，重新通知业务系统时发生异常：{}", req.getPaymentSessionCode(), e);
						return res;
					}

					if (post.toUpperCase().contains("SUCCESS")) {
						paymentSession.setNotifyResult(true);
						res.setRet(ResponseContext.RES_SUCCESS_CODE);
						res.setRetInfo("支付成功，并重新通知业务系统！");
						logger.info("[{}]支付成功，并重新通知业务系统!", req.getPaymentSessionCode());
					} else {
						res.setRet(ResponseContext.RES_SUCCESS_CODE);
						res.setRetInfo("["+req.getPaymentSessionCode()+"]支付成功，重新通知业务系统，但业务系统处理失败！原因:" + post);
						logger.info("[{}]支付成功，重新通知业务系统，但业务系统处理失败!:{}", req.getPaymentSessionCode(), post);
					}
				} else {
					res.setRet(ResponseContext.RES_QUERY_PAYMENT_INCOMPLETE_CODE);
					res.setRetInfo(ResponseContext.RES_QUERY_PAYMENT_INCOMPLETE_INFO);
				}
			} catch (Exception e1) {
				logger.info("查询支付结果异常{}", e1);
				res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO + "查询支付结果异常:" + e1.getMessage());
			}

		} else {
			res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "找不到对应的支付会话！");
		}

		return res;
	}

	private ModelAndView fillJsonView(GenericResponse res) {
		ModelAndView mv = new ModelAndView(new MappingJackson2JsonView());
		mv.addObject("returnCode", res.getRet());
		mv.addObject("returnInfo", res.getRetInfo());
		return mv;
	}

	private String getParam(PaymentSubmitReq req) {
		if (req == null || req.getPaymentScene() == null) {
			return null;
		} else if (req.getPaymentScene().equals("WX_MINIPROGRAM") || req.getPaymentScene().equals("WX_PUBLIC") || req.getPaymentScene().equals("AL_MINIPROGRAM")) {
			return req.getOpenId();
		} else if (req.getPaymentScene().equals("WX_PAYCODE") || req.getPaymentScene().equals("AL_PAYCODE")) {
			return req.getAuthCode();
		} else {
			return null;
		}
	}

//	@PostConstruct
//	private void notifyPaymentResult() throws Exception {
//		new Thread(new Runnable() {
//			@Override
//			public void run() {
//				try {
//					IQueue<PaymentResultNotifyMessage> queue = cacheManager.getHazelcastInstance()
//							.getQueue("paymentResultNotifyQueue");
//
//					while (true) {
//						PaymentResultNotifyMessage item = queue.take();
//						logger.info("异步处理支付结果消息: {}", item);
//						if (item != null) {
//							// Thread.sleep(1500);// 等待主调用方法的事务提交。
//							// 直接支付不需要回调的支付方式要回写paymentSession，order的状态
//							SessionFactory sessionFactory = (SessionFactory) SpringHandler
//									.getBean("faithSessionFactory");
//							Session session = sessionFactory.openSession();
//
//							Transaction transaction = session.beginTransaction();
//							@SuppressWarnings("unchecked")
//							Query<PaymentSessionEntity> query = session
//									.createQuery("select e from PaymentSessionEntity e where e.sn ='"
//											+ item.getPayemntSessionSn() + "'");
//							PaymentSessionEntity paymentSession = query.uniqueResult();
//							if (paymentSession != null) {
//
//								Map<String, String> modelMap = new TreeMap<String, String>();
//								if (null == item.getResulstMap().get("errorCode")) {
//									modelMap.put("returnCode", "SUCCESS");
//									Object objOrderResult = item.getResulstMap().get("orderResult");
//									if (null != objOrderResult) {
//										@SuppressWarnings("unchecked")
//										Map<String, Object> orderResultMap = (Map<String, Object>) objOrderResult;
//										if (null != orderResultMap.get("outTradeNo")) {
//											paymentSession.setOutTradeNo(orderResultMap.get("outTradeNo").toString());
//											modelMap.put("outTradeNo", paymentSession.getOutTradeNo());
//											// 修改订单状态，创建支付记录
//											handle(paymentSession, session);
//											// nonceStr 要与下单返回结果中的一致
//											modelMap.put("amount", paymentSession.getAmount().toString());
//										}
//									}
//
//								} else {
//									modelMap.put("returnCode", "FAIL");
//								}
//								modelMap.put("clientTradeNo", paymentSession.getClientTradeNo());
//								modelMap.put("paymentSessionSn", paymentSession.getSn());
//
//								DigestUtil.fillSignatureParam(modelMap,
//										paymentSession.getOrder().getPaymentMerchant().getMerchantCode(),
//										paymentSession.getOrder().getPaymentMerchant().getSecret());
//
//								String jsonstr = JSONObject.valueToString(modelMap);
//								String post;
//								try {
//									// 通知业务系统
//									logger.info("发送支付结果报文到业务系统：");
//									post = HttpClientUtil.jsonPost(paymentSession.getNotifyUrl(), "UTF-8", jsonstr,
//											null);
//									logger.info("业务系统响应支付通知报文：{}", post);
//								} catch (Exception e) {
//									throw new RuntimeException(e);
//								}
//
//								if (post.toUpperCase().contains("SUCCESS")) {
//									paymentSession.setNotifyResult(true);
//								}
//								// 完成通知业务逻辑
//							}
//							session.save(paymentSession);
//							if (transaction != null) {
//								transaction.commit();
//							}
//
//							if (session != null && session.isOpen()) {
//								session.close();
//							}
//						}
//					}
//
//				} catch (Exception e) {
//					e.printStackTrace();
//				}
//
//			}
//		}).start();
//
//	}
//
//	private void handle(PaymentSessionEntity paymentSession, Session session) {
//		Assert.notNull(paymentSession, "支付会话不能为NULL");
//		logger.info("[{}]进入支付成功后业务逻辑处理，handle", paymentSession.getSn());
//
//		if (!LockMode.PESSIMISTIC_WRITE.equals(session.getCurrentLockMode(paymentSession))) {
//			session.flush();
//			session.refresh(paymentSession, LockMode.PESSIMISTIC_WRITE);
//		}
//
//		if (BooleanUtils.isNotFalse(paymentSession.getIsSuccess())) {
//			logger.info("[{}]会话已经完成支付", paymentSession.getSn());
//			return;
//		}
//		PaymentOrderEntity order = paymentSession.getOrder();
//		if (order != null) {
//			logger.info("[{}]创建订单支付记录OrderPayment", paymentSession.getSn());
//			PaymentRecordEntity orderPayment = new PaymentRecordEntity();
//			orderPayment.setMethod(PaymentRecordEntity.Method.online);
//			orderPayment.setPaymentMethod(paymentSession.getPaymentPluginName());
//			orderPayment.setAmount(paymentSession.getAmount());
//			orderPayment.setFee(paymentSession.getFee());
//			orderPayment.setOrder(order);
//			orderPayment.setOutTradeNo(paymentSession.getOutTradeNo());
//			orderPayment.setPaymentSessionSn(paymentSession.getSn());
//			orderPayment.setPaymentPlunginSn(paymentSession.getPaymentPluginId());
//			payment(order, orderPayment, paymentSession, session);
//
//		}
//		// 修改支付会话状态为已经成功
//		paymentSession.setIsSuccess(true);
//		logger.info("[{}]支付会话状态设置为成功，会话结束", paymentSession.getSn());
//	}
//
//	/**
//	 * @param @param order
//	 * @param @param orderPayment 参数说明
//	 * @return void 返回类型
//	 * @throws @Title: payment
//	 * @Description: TODO(收款)
//	 */
//	private void payment(PaymentOrderEntity order, PaymentRecordEntity orderPayment,
//			PaymentSessionEntity paymentSession, Session session) {
//		Assert.notNull(order, "订单不能为NULL");
//		Assert.notNull(orderPayment, "订单支付不能为NULL");
//		Assert.notNull(orderPayment.getAmount(), "订单支付金额不能为NULL");
//		Assert.state(orderPayment.getAmount().compareTo(BigDecimal.ZERO) > 0, "订单支付金额需要大于零");
//
//		orderPayment.setSn(CodeUtil.getSNCode("PR"));
//		orderPayment.setOrder(order);
//		session.save(orderPayment);
//		logger.info("[{}]保存订单支付记录", paymentSession.getSn());
//
//		// 修改已付金额
//		BigDecimal paidAmount = order.getPaidAmount();
//		paidAmount = (paidAmount == null) ? BigDecimal.ZERO : paidAmount;
//		order.setPaidAmount(paidAmount.add(orderPayment.getEffectiveAmount()));
//		order.setPaymentRecord(orderPayment);
//
//		order.setOrderState(EntityContext.PRODUCT_ORDER_STATE_PAY); // 需要配送的订单付款后状态变为已付款
//
//		session.save(order);
//		// 订单审计日志
//
//	}

}