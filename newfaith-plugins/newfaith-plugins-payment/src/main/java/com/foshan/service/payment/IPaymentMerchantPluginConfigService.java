package com.foshan.service.payment;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.foshan.entity.payment.PaymentMerchantPluginConfigEntity;
import com.foshan.form.payment.request.PaymentMerchantPluginReq;
import com.foshan.form.payment.request.UploadPaymentCertReq;
import com.foshan.form.payment.response.GetPaymentMerchantPluginRes;
import com.foshan.form.payment.response.GetPaymentPluginsRes;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.upload.UploadRes;


/**
 * 
* @ClassName: PluginService 
* @Description: TODO(插件管理) 
* <AUTHOR>
* @date 2019年3月13日 下午1:24:06 
*
 */
public interface IPaymentMerchantPluginConfigService {

	/**
	 * 
	* @Title: getPaymentMerchantPluginConfigList 
	* @Description: TODO(根据商户ID、插件ID以及插件类型获取商户插件的列表) 
	* @param @param paymentMerchantId
	* @param @param pluginId
	* @param @param type
	* @param @return  参数说明 
	* @return List<PaymentMerchantPluginConfigEntity>    返回类型 
	 */
	public List<PaymentMerchantPluginConfigEntity> getPaymentMerchantPluginConfigList(Integer paymentMerchantId,Integer pluginId,Integer type);
	/**
	 * 
	* @Title: getPaymentMerchantPluginConfig 
	* @Description: TODO(根据商户ID、插件ID以及插件类型获取商户插件) 
	* @param @param paymentMerchantId
	* @param @param pluginId
	* @param @param type
	* @param @return  参数说明 
	* @return PaymentMerchantPluginConfigEntity    返回类型 
	 */
	public PaymentMerchantPluginConfigEntity getPaymentMerchantPluginConfig(Integer paymentMerchantId,Integer pluginId,Integer type);

	/** 
	* @Title: addPaymentMerchantPaymentPluginConfig 
	* @Description: TODO(商户设置支付配置) 
	* @param @param req  参数说明 
	* @return void    返回类型 
	*/
	public IResponse addPaymentMerchantPaymentPluginConfig(PaymentMerchantPluginReq req);
	/** 
	* @Title: updatePaymentMerchantPaymentPluginConfig 
	* @Description: TODO(商户修改支付配置) 
	* @param @param req  参数说明 
	* @return void    返回类型 
	*/
	public IResponse updatePaymentMerchantPaymentPluginConfig(PaymentMerchantPluginReq req);
	/** 
	* @Title: commitPaymentPluginConfig 
	* @Description: TODO(商户提交支付配置审核) 
	* @param  req
	* @return IResponse    返回类型 
	* @throws 
	*/
	public IResponse submitPaymentPluginConfig(PaymentMerchantPluginReq req);
	/** 
	* @Title: auditPaymentMerchantPaymentPluginConfig 
	* @Description: TODO(平台管理员审核商户支付配置请求) 
	* @param  req
	* @return IResponse    返回类型 
	*/
	public IResponse auditPaymentMerchantPaymentPluginConfig(PaymentMerchantPluginReq req);
	/** 
	* @Title: appointPaymentMerchantPaymentPluginConfig 
	* @Description: TODO(平台管理员为商户指定支付配置数据) 
	* @param req
	* @return IResponse    返回类型 
	* @throws 
	*/
	public IResponse appointPaymentMerchantPaymentPluginConfig(PaymentMerchantPluginReq req);

	/** 
	* @Title: getPaymentMerchantPluginById 
	* @Description: TODO(管理员根据商铺、插件及审核状态查询商户支付的配置信息) 
	* @param curPageNum
	* @param pageSize
	* @param  paymentMerchantId
	* @param  paymentMerchantName
	* @param pluginId
	* @param auditStatus
	* @param regionId 区域id
	* @return Response
	*  
	*/
	public IResponse getPaymentPlugins(Integer curPageNum, Integer pageSize, Integer paymentMerchantId, String paymentMerchantName, Integer pluginId, Integer auditStatus, Integer regionId);
	
	/**
	 * 
	* @Title: updatePaymentMerchantPaymentPluginConfigByPlatform 
	* @Description: TODO(平台管理员修改商户支付的配置信息)
	* @param req
	* @return 
	 */
	public IResponse updatePaymentMerchantPaymentPluginConfigByPlatform(PaymentMerchantPluginReq req);
	
	/**
	 * 
	* @Title: clearPaymentMerchantPaymentPluginConfigByPlatform 
	* @Description: TODO(平台管理员删除商户支付的配置信息)
	* @param req
	* @return 
	 */
	public IResponse deletePaymentMerchantPaymentPluginConfigByPlatform(PaymentMerchantPluginReq req);

	/**
	 * 
	* Title: getPaymentMerchantPluginByMachId 
	* Description: TODO(根据微信商户号、插件SN以及插件类型获取商户插件) 
	* @param  machId  微信商户号
	* @param  pluginSn 插件SN 
	* @return PaymentMerchantPluginConfigEntity    返回类型 
	 */
	public PaymentMerchantPluginConfigEntity getPaymentMerchantPluginByMachId(String machId, String pluginSn);

	/**
	 * 
	 * @Title: 
	 *   获取商户支付插件配置详情
	 * @Description:
	 *   商铺插件id不能为空 
	 * @param req
	 * @return GetPaymentMerchantPluginRes
	 */
	public GetPaymentMerchantPluginRes getPaymentMerchantPluginDetail(PaymentMerchantPluginReq req);
	
	/**
	 * 
	 * @Title:
	 *   获取支付插件配置详情
	 * @Description:
	 *   插件配置id不能为空
	 * @param req
	 * @return GetPaymentMerchantPluginRes
	 */
	public GetPaymentMerchantPluginRes getPaymentPluginDetail(PaymentMerchantPluginReq req);
	
	/** 
	* @Title: getPaymentPlugins 
	* @Description: TODO(获取管理支付插件的From列表) 
	* @return List<PaymentMerchantPaymentPluginListForm>    返回类型 
	*/
	public GetPaymentPluginsRes getPaymentPlugins(PaymentMerchantPluginReq req);
	
	/**
	 * 
	 * @Title: 
	 *   上传支付证书
	 * @Description:
	 *   如果为平台管理操作上传支付证书，商铺ID必填
	 * @param request
	 * @param req
	 * @return
	 */
	public UploadRes uploadCertFile(HttpServletRequest request, UploadPaymentCertReq req);
	
	/**
	 * 
	 * @Title:平台为商铺配置支付插件
	 * @Description:平台为商铺配置支付插件
	 * @param req PaymentMerchantPluginReq
	 * @return
	 */
	public IResponse addPaymentMerchantPaymentPluginConfigByPlatform(PaymentMerchantPluginReq req);


}
