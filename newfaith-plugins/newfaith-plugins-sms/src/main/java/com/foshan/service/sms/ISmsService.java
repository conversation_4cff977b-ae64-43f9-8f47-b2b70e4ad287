package com.foshan.service.sms;

import javax.servlet.http.HttpServletResponse;

import com.foshan.form.response.IResponse;
import com.foshan.form.sms.request.QuerySendStatisticsReq;
import com.foshan.form.sms.request.SmsReq;


public interface ISmsService {
    public IResponse sendSms(SmsReq req);
    public IResponse searchByTaskId(SmsReq req);
    public IResponse querySendDetails(SmsReq req);
    public IResponse updateSmsAccountInfo();
    public void aliyunQuerySendStatistics(QuerySendStatisticsReq req);
    public IResponse getSmsInfoList(SmsReq req);
    public IResponse exportSmsInfoList(SmsReq req,HttpServletResponse response);
}
