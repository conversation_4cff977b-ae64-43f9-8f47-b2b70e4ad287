package com.foshan.service.sms.impl;




import com.foshan.service.impl.GenericService;
import com.foshan.util.SmsContextInfo;
import com.foshan.dao.sms.ISmsAccountDao;
import com.foshan.dao.sms.ISmsDao;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;

public class genericSmsService extends GenericService {
    @Resource(name = "smsDao")
    public ISmsDao smsDao;
    @Resource(name = "smsAccountDao")
    public ISmsAccountDao smsAccountDao;
	@Autowired
	protected SmsContextInfo smsContextInfo;
}
