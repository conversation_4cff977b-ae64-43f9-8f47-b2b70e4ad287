package com.foshan.form.sms;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonInclude(Include.NON_NULL) 
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceUrlForm implements IForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1486599469379033709L;
	private Integer invoiceUrlId;

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
