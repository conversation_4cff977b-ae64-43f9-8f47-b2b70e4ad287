package com.foshan.form.sms.response;

import java.util.ArrayList;
import java.util.List;

import com.foshan.form.response.BasePageResponse;
import com.foshan.form.sms.SmsInfoForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


	
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="设备表(GetSmsInfoListRes)")
public class GetSmsInfoListRes extends BasePageResponse {

	private static final long serialVersionUID = 6200032153015501991L;

	@ApiModelProperty(value = "设备表列表")
	private List<SmsInfoForm> smsInfoList = new ArrayList<>();
}
