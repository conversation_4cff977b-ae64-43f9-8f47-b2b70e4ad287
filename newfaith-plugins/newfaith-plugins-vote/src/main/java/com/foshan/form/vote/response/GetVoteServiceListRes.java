package com.foshan.form.vote.response;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.ServiceForm;
import com.foshan.form.response.BasePageResponse;
@JsonInclude(Include.NON_NULL)
public class GetVoteServiceListRes extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -3828600486373154379L;
	private List<ServiceForm> serviceList = new ArrayList<ServiceForm>();

	public GetVoteServiceListRes() {
		super();
		// TODO 自动生成的构造函数存根
	}

	public GetVoteServiceListRes(int currentPage, int pageSize, int total, int totalResult) {
		super(currentPage, pageSize, total, totalResult);
		// TODO 自动生成的构造函数存根
	}

	public GetVoteServiceListRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO 自动生成的构造函数存根
	}

	public GetVoteServiceListRes(List<ServiceForm> serviceList) {
		super();
		this.serviceList = serviceList;
	}

	public List<ServiceForm> getServiceList() {
		return serviceList;
	}

	public void setServiceList(List<ServiceForm> serviceList) {
		this.serviceList = serviceList;
	}
	
	
}
