package com.foshan.form.vote.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

@JsonInclude(Include.NON_NULL) 
public class ModifyLuckyPrizeRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -8171222744777106507L;
	private Integer luckyRuleId;
	private Integer prizeId;
	private String prizeCode;
	private String prizeName;
	private String prizeImage;
	private Integer prizeCount;
	private Double odds;

	public ModifyLuckyPrizeRes() {
		super();
		// TODO Auto-generated constructor stub
	}

	public ModifyLuckyPrizeRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public ModifyLuckyPrizeRes(Integer luckyRuleId, Integer prizeId, String prizeCode, String prizeName,
			String prizeImage, Integer prizeCount, Double odds) {
		super();
		this.luckyRuleId = luckyRuleId;
		this.prizeId = prizeId;
		this.prizeCode = prizeCode;
		this.prizeName = prizeName;
		this.prizeImage = prizeImage;
		this.prizeCount = prizeCount;
		this.odds = odds;
	}

	public Integer getLuckyRuleId() {
		return luckyRuleId;
	}

	public void setLuckyRuleId(Integer luckyRuleId) {
		this.luckyRuleId = luckyRuleId;
	}

	public Integer getPrizeId() {
		return prizeId;
	}

	public void setPrizeId(Integer prizeId) {
		this.prizeId = prizeId;
	}

	public String getPrizeCode() {
		return prizeCode;
	}

	public void setPrizeCode(String prizeCode) {
		this.prizeCode = prizeCode;
	}

	public String getPrizeName() {
		return prizeName;
	}

	public void setPrizeName(String prizeName) {
		this.prizeName = prizeName;
	}

	public String getPrizeImage() {
		return prizeImage;
	}

	public void setPrizeImage(String prizeImage) {
		this.prizeImage = prizeImage;
	}

	public Integer getPrizeCount() {
		return prizeCount;
	}

	public void setPrizeCount(Integer prizeCount) {
		this.prizeCount = prizeCount;
	}

	public Double getOdds() {
		return odds;
	}

	public void setOdds(Double odds) {
		this.odds = odds;
	}

	@Override
	public String toString() {
		return "ModifyLuckyPrizeRes [luckyRuleId=" + luckyRuleId + ", prizeId=" + prizeId + ", prizeCode=" + prizeCode
				+ ", prizeName=" + prizeName + ", prizeImage=" + prizeImage + ", prizeCount=" + prizeCount + ", odds="
				+ odds + ", ret=" + ret + ", retInfo=" + retInfo + "]";
	}

}
