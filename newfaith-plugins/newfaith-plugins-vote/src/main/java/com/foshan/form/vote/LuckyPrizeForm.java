package com.foshan.form.vote;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonInclude(Include.NON_NULL) 
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class LuckyPrizeForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = 5257590880478337824L;
	private Integer prizeId;
	private String prizeCode;
	private String prizeName;
	private String prizeImage;
	private Integer prizeCount;
	private Double odds;
	private Integer luckyRuleId;
	private Integer luckyId;
	private String winnerInfo;
	private String luckyTime;
	private Integer giftId;
	private Integer modelId;
	private Integer modelType;
	private String modelCode;
	private Integer productId;
	private Integer giftState;
	private String endTime;
	private Integer showFlag;
	
	public LuckyPrizeForm(Integer prizeId, String prizeCode, String prizeName, String prizeImage, Integer prizeCount,
			Double odds) {
		super();
		this.prizeId = prizeId;
		this.prizeCode = prizeCode;
		this.prizeName = prizeName;
		this.prizeImage = prizeImage;
		this.prizeCount = prizeCount;
		this.odds = odds;
	}

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
