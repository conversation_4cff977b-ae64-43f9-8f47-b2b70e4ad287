package com.foshan.form.vote.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.request.BasePageRequest;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonInclude(Include.NON_NULL)
@Getter
@Setter
@NoArgsConstructor
public class VoteContestantReq extends BasePageRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = 798890191837345212L;
	private Integer serviceId;
	private Integer columnId;
	private Integer groupId;
	private Integer contestantId;
	private String contestantCode;
	private String contestantDesc;
	private String contestantName;
	private String contestantPhone;
	private Integer contestantState;
	private String assetCode;
	private Integer newGroupId;

}
