package com.foshan.form.vote;

import java.util.HashMap;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(Include.NON_EMPTY)
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class QuestionPlanCountForm implements IForm {
	/**
	* 
	*/
	private static final long serialVersionUID = -8438986197932032903L;
	private Integer totalLessCount;
	private Integer questionPlanId;
	private HashMap<Integer, Integer> subLessCount = new HashMap<>();

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
