package com.foshan.util.vote;

import java.util.HashMap;

import com.foshan.form.vote.AccountLuckyForm;
import com.foshan.form.vote.QuestionAnswerForm;
import com.foshan.form.vote.QuestionForm;
import com.foshan.form.vote.QuestionPlanCountForm;


public class QuestionMap {

	// 题库题目缓存<缓存分组编号，<题目序号，题目对象>>
	public static HashMap<Integer,HashMap<Integer, QuestionForm>> questionList = new HashMap<>();

	// 用户回答问卷次数<用户标识,<问卷Id，用户答题对象>>
	public static HashMap<String, HashMap<String,QuestionAnswerForm>> accountAnswerCount = new HashMap<>();

	// 用户抽奖次数<用户标识,<业务Id，剩余抽奖次数>>
	public static HashMap<String,HashMap<String,AccountLuckyForm>> accountLessQuestionLuckyCount = new HashMap<>();
	
	// 用户剩余答题次数<用户标识，<问卷id，剩余答题次数>>
	public static HashMap<String,HashMap<String,QuestionPlanCountForm>> accountLessQuestionPlanCount = new HashMap<>();
}
