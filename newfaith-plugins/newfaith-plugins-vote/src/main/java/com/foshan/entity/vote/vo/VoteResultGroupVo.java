package com.foshan.entity.vote.vo;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_EMPTY)
public class VoteResultGroupVo {
	private Integer serviceId;
	private Integer columnId;
	private String columnName;
	private Integer groupId;
	private String groupName;
	private Integer contestantId;
	private String contestantCode;
	private String contestantName;
	private String contestantPhone;
	private String ImageFile;
	private String smallImageFile;
	private Long votes = 0L;

}
