package com.foshan.entity.vote;

import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_vote_result")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class VoteResultEntity implements IEntityBean {
	/**
	 * 投票结果
	 */
	private static final long serialVersionUID = -2113528506459074989L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer Id;
	@Column(columnDefinition = "varchar(20)  comment '投票智能卡号'")
	private String smartcardId;
	@Column(columnDefinition = "varchar(10)  comment '投票区域码'")
	private String regionCode;
	@Column(columnDefinition = "varchar(30)  comment '投票用户编号'")
	private String userCode;
	@Column(columnDefinition = "varchar(42)  comment '投票MAC地址'")
	private String macAddr;
	@Column(columnDefinition = "varchar(42)  comment '投票IP地址'")
	private String ipAddr;
	@Column(columnDefinition = "varchar(20)  comment '投票电话号码'")
	private String phone;
	@Column(columnDefinition = "int(3)  comment '投票次数'")
	private Integer voteMore;
	@Column(columnDefinition = "Timestamp  default current_timestamp comment '投票时间'")
	private Timestamp voteTime;
	@Column(columnDefinition = "varchar(30)  comment '投票栏目编号'")
	private String columnCode;
	@Column(columnDefinition = "varchar(100)  comment '投票额外信息'")
	private String resultInfo;
	@Column(columnDefinition = "int(11) comment '业务Id'")
	private Integer serviceId;
	@Column(columnDefinition = "int(11) comment '栏目Id'")
	private Integer columnId;
	@ManyToOne(targetEntity = VoteGroupEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "groupId", referencedColumnName = "id",foreignKey = @ForeignKey(name = "none",value = ConstraintMode.NO_CONSTRAINT))
	private VoteGroupEntity voteGroup;
	@ManyToOne(targetEntity = VoteContestantEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "contestantId", referencedColumnName = "id",foreignKey = @ForeignKey(name = "none",value = ConstraintMode.NO_CONSTRAINT))
	private VoteContestantEntity voteContestant;
	
}
