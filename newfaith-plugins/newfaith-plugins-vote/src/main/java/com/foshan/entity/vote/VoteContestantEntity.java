package com.foshan.entity.vote;

import java.sql.Timestamp;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_vote_contestant")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class VoteContestantEntity implements IEntityBean {

	/**
	 *投票选手表
	 */
	private static final long serialVersionUID = -2703124658556127716L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(20) comment '选手编号'")
	private String contestantCode;
	@Column(columnDefinition = "varchar(30) comment '选手名称'")
	private String contestantName;
	@Column(columnDefinition = "varchar(20) comment '选手电话'")
	private String contestantPhone;
	@Column(columnDefinition = "varchar(500) comment '选手说明'")
	private String contestantDesc;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '注册时间'")
	private Timestamp registTime;
	@Column(columnDefinition = "varchar(50) comment '选手关键字'")
	private String contestantKeyWords;
	@Column(columnDefinition = "int(1) comment '选手状态 0--无效 1--有效'")
	private Integer contestantState;
	@Column(columnDefinition = "int(11) comment '业务Id'")
	private Integer serviceId;
	@Column(columnDefinition = "int(11) comment '栏目Id'")
	private Integer columnId;
	@ManyToOne(targetEntity = AssetEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "assetId", referencedColumnName = "id", nullable = true,foreignKey = @ForeignKey(name = "none",value = ConstraintMode.NO_CONSTRAINT))
	private AssetEntity asset;
	@ManyToMany(targetEntity = VoteGroupEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_group_contestant", joinColumns = @JoinColumn(name = "voteContestantId", referencedColumnName = "id",foreignKey = @ForeignKey(name = "none",value = ConstraintMode.NO_CONSTRAINT)), inverseJoinColumns = @JoinColumn(name = "voteGroupId", referencedColumnName = "id",foreignKey = @ForeignKey(name = "none",value = ConstraintMode.NO_CONSTRAINT)),foreignKey = @ForeignKey(name = "none",value = ConstraintMode.NO_CONSTRAINT))
	@JsonIgnore
	private Set<VoteGroupEntity> groupList = new HashSet<VoteGroupEntity>();

	
}
