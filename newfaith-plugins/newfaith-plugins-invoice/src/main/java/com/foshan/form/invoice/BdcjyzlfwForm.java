package com.foshan.form.invoice;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonInclude(Include.NON_NULL) 
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class BdcjyzlfwForm implements IForm {
	/**
	 * 不动产经营租赁服务
	 */
	private static final long serialVersionUID = 3452453526807898296L;
	@ApiModelProperty(value = "不动产地址")
	private String bdcdz;
	@ApiModelProperty(value = "不动产详细地址")
	private String fullAddress;
	@ApiModelProperty(value = "租赁起止日期")
	private String zlqqz;
	@ApiModelProperty(value = "跨地市标识")
	private String kdsbz;
	@ApiModelProperty(value = "产权证书号")
	private String cqzsh;
	@ApiModelProperty(value = "单位")
	private String dw;
	
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
