package com.foshan.form.invoice.request;

import com.foshan.form.request.AuditlogInfo;
import com.foshan.form.request.IRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="百旺短信验证码请求参数(BWSmsReq)")
public class BWSmsReq implements IRequest,AuditlogInfo{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -573745542248218773L;
	@ApiModelProperty(value = "短信验证码")
	private String verifyCode;
	
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
	
}
