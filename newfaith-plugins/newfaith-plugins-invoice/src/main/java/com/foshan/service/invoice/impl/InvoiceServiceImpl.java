package com.foshan.service.invoice.impl;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.ClientProtocolException;
import org.json.JSONObject;
import org.springframework.cache.Cache;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.entity.invoice.InvoiceAccountEntity;
import com.foshan.entity.invoice.InvoiceInterfaceEntity;
import com.foshan.entity.invoice.InvoicePostEntity;
import com.foshan.entity.invoice.InvoiceUrlEntity;
import com.foshan.form.invoice.GoodsForm;
import com.foshan.form.invoice.QuotaForm;
import com.foshan.form.invoice.request.BWInvoiceReq;
import com.foshan.form.invoice.request.BWQueryReq;
import com.foshan.form.invoice.request.BWRedApplyReq;
import com.foshan.form.invoice.request.BWRedInfoQueryReq;
import com.foshan.form.invoice.request.BWSmsReq;
import com.foshan.form.invoice.request.InvoiceReq;
import com.foshan.form.invoice.request.RedInfoOperateReq;
import com.foshan.form.invoice.request.RedIssueReq;
import com.foshan.form.invoice.response.bWInvoice.BWInvoiceRes;
import com.foshan.form.invoice.response.bWInvoice.GetQuotaRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.invoice.IInvoiceService;
import com.foshan.util.HttpClientUtil;
import com.foshan.util.JsonUtil;
import com.foshan.util.invoice.BWEncryptionUtil;
import com.foshan.util.invoice.Base64Util;
import com.foshan.util.invoice.MD5Util;
import com.hazelcast.spring.cache.HazelcastCacheManager;

@Transactional
@Service("invoiceService")
public class InvoiceServiceImpl extends GenericInvoiceService implements IInvoiceService {
	@Resource
	private HazelcastCacheManager cacheManager;
	
	private static InvoiceAccountEntity account=null;

	/*@Override
	public IResponse invoice(InvoiceReq req) {
		BWInvoiceRes res = new BWInvoiceRes();
		StringBuilder retInfo = new StringBuilder();
		InvoiceAccountEntity account = invoiceAccountDao.getUniqueByHql("select a "
				+ "from InvoiceAccountEntity a ", "");
		if(null !=account && account.getAccountType()==0) {
			if(null!=req.getInvoiceType() && req.getInvoiceType()==0) {//开正票
				retInfo.append(StringUtils.isEmpty(req.getBillCode()) ? "“单据编号”不能为空!" : "")
					.append(StringUtils.isEmpty(req.getTaxationMode())? "“征税方式”不能为空!":"")
					.append(StringUtils.isEmpty(req.getBuyerName()) ? "“购货单位名称”不能为空!" : "");
				BWInvoiceContentForm contentForm = new BWInvoiceContentForm();
				int row = 1;
				for(GoodsForm goods : req.getGoodsList()) {
					retInfo.append(StringUtils.isEmpty(goods.getGoodsName()) ? "“商品名称”不能为空!" : "")
					.append(StringUtils.isEmpty(goods.getTaxKindCode()) ? "“税收分类编码”不能为空!" : "")
					.append(null==goods.getLineKind() ? "“发票行性质”不能为空!" : "")
					.append(StringUtils.isEmpty(goods.getTaxRate()) ? "“税率”不能为空!" : "");
					if(retInfo.length()>0) {
						break;
					}
					MxxxForm mxxxForm = new MxxxForm();
					mxxxForm.setDjhh(row+"");
					row++;
					mxxxForm.setFphxz(goods.getLineKind().toString());
					mxxxForm.setSpmc(goods.getGoodsName());
					mxxxForm.setSsbm(goods.getTaxKindCode());
					mxxxForm.setGgxh(StringUtils.isNotEmpty(goods.getSpecName()) ? goods.getSpecName():"");
					mxxxForm.setJldw(StringUtils.isNotEmpty(goods.getSaleUnit()) ? goods.getSaleUnit():"");
					mxxxForm.setHsdj(StringUtils.isNotEmpty(goods.getPrice()) ? goods.getPrice():"");
					mxxxForm.setSpsl(StringUtils.isNotEmpty(goods.getQty()) ? goods.getQty():"");
					mxxxForm.setHsje(StringUtils.isNotEmpty(goods.getTotalAmount()) ? goods.getTotalAmount():"");
					mxxxForm.setBhsdj(StringUtils.isNotEmpty(goods.getNonTaxPrice()) ? goods.getNonTaxPrice():"");
					mxxxForm.setBhsje(StringUtils.isNotEmpty(goods.getAmount()) ? goods.getAmount():"");
					mxxxForm.setTax(goods.getTaxRate());
					mxxxForm.setSe(StringUtils.isNotEmpty(goods.getTaxAmount()) ? goods.getTaxAmount():"");
					mxxxForm.setLslbs(StringUtils.isNotEmpty(goods.getDutyFree()) ? goods.getDutyFree():"0");
					mxxxForm.setYhzcbs(StringUtils.isNotEmpty(goods.getCouponFlag()) ? goods.getCouponFlag():"1");
					mxxxForm.setZzstsgl(StringUtils.isNotEmpty(goods.getCouponPolicy()) ? goods.getCouponPolicy():"");
					mxxxForm.setSpfwjc("");
					mxxxForm.setSppc("");
					mxxxForm.setSpdm("");
					mxxxForm.setSpfl("");
					contentForm.getMxxx().add(mxxxForm);
				}
				if(retInfo.length()>0) {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(retInfo.toString());
					return res;
				}
				contentForm.setDjbh(req.getBillCode());
				
				account.getSellerInformationList().forEach(o->{
					if(o.getUseState() == 1) {
						contentForm.setXfdzdh(o.getSellerAddressTel());
						contentForm.setXfyhzh(o.getSellerBankAccount());
						contentForm.setFhr(StringUtils.isNotEmpty(req.getChecker()) ? req.getChecker() : o.getChecker());
						contentForm.setSkr(StringUtils.isNotEmpty(req.getPayee()) ? req.getPayee() : o.getPayee());
						contentForm.setKpr(StringUtils.isNotEmpty(req.getDrawer()) ? req.getDrawer() : o.getDrawer());
						contentForm.setZdr(StringUtils.isNotEmpty(req.getPreparer()) ? req.getPreparer() : o.getPreparer());
					}
				});
	
				JSONObject accountParameterJson = new JSONObject(account.getParameter());
				contentForm.setKpzddm(JsonUtil.getJSONNodeByPath(accountParameterJson,"kpzddm"));
				contentForm.setGsdm(JsonUtil.getJSONNodeByPath(accountParameterJson,"gsdm"));//公司代码
				contentForm.setYhdm(JsonUtil.getJSONNodeByPath(accountParameterJson,"yhdm"));//用户代码
				contentForm.setFplxdm(StringUtils.isNotEmpty(req.getInvoiceKindCode()) ? req.getInvoiceKindCode() : "02");
				contentForm.setKplx("0");
				contentForm.setZsfs(req.getTaxationMode());
				contentForm.setKhmc(req.getBuyerName());
				contentForm.setKhsh(StringUtils.isNotEmpty(req.getBuyerTaxCode()) ? req.getBuyerTaxCode() : "");
				contentForm.setKhdzdh(StringUtils.isNotEmpty(req.getBuyerAddressTel()) ? req.getBuyerAddressTel() : "");
				contentForm.setKhyhzh(StringUtils.isNotEmpty(req.getBuyerBankAccount()) ? req.getBuyerBankAccount() : "");
				contentForm.setGmfMobile(StringUtils.isNotEmpty(req.getBuyerMobile()) ? req.getBuyerMobile() : "");
				contentForm.setGmfEmail(StringUtils.isNotEmpty(req.getBuyerEmail()) ? req.getBuyerEmail() : "");
				contentForm.setHsje(StringUtils.isNotEmpty(req.getTotalAmount()) ? req.getTotalAmount() : "0");
				contentForm.setHjje(StringUtils.isNotEmpty(req.getAmount()) ? req.getAmount() :"0");
				contentForm.setHjse(StringUtils.isNotEmpty(req.getTaxAmount()) ? req.getTaxAmount() :"0");
				contentForm.setBz(StringUtils.isNotEmpty(req.getInvoiceRemark()) ? req.getInvoiceRemark() :"");
				contentForm.setQdbz(null!=req.getGoodsList()&&req.getGoodsList().size()>8 ? "1":"0");
				contentForm.setKce(StringUtils.isNotEmpty(req.getMinusAmount()) ? req.getMinusAmount() :"0");
				contentForm.setTdyslxdm("");
				contentForm.setKjly("");
				contentForm.setFpdm("");
				contentForm.setFphm("");
				contentForm.setSfwzzfp("");
				contentForm.setZppzdm("");
				contentForm.setHsbz("");
				contentForm.setBmdm("");
				contentForm.setDjrq(DateUtil.format(new Date(), 0));
				contentForm.setSjly("");//数据来源
				contentForm.setKz1("");
				contentForm.setKz2("");
				contentForm.setKz3("");
				
				ObjectMapper mapper = new ObjectMapper();
				String content ="";
				String responseStr="";
				String contentBase64 ="";
				 try {
					 content = mapper.writeValueAsString(contentForm);
					 contentBase64 = Base64Util.encode(content);
					 Map<String,Object> map = getBWInvoiceReqData(account,contentBase64,"QDP-FP-10001");
					 if(StringUtils.isNotEmpty(map.get("retInfo").toString())) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(map.get("retInfo").toString());
						return res;
					 }
					 responseStr = map.get("responseStr").toString();
				 } catch (JsonProcessingException e) {
					 e.printStackTrace();
				 }
	
				JSONObject responseJson = new JSONObject(responseStr);
				if(responseJson.has("code") && 
						JsonUtil.getJSONNodeByPath(responseJson,"code").equals("0")){
					 try {
				         Thread.sleep(1000); 
				     } catch(InterruptedException ex) {
				    	 Thread.currentThread().interrupt();
				     }
					 req.setApiCode("QDP-FP-10003");
					 res=(BWInvoiceRes) queryInvoiceResult(req,account);
					
					 //res.setRet(ResponseContext.RES_SUCCESS_CODE);
					 //res.setRetInfo(JsonUtil.getJSONNodeByPath(responseJson,"message"));
				}else if(responseJson.has("message")) {
					res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
					res.setRetInfo(JsonUtil.getJSONNodeByPath(responseJson,"message")+JsonUtil.getJSONNodeByPath(responseJson,"renson"));
				}else {
					res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
				}
			}else if(null!=req.getInvoiceType() && req.getInvoiceType()==1) {//开红票
				retInfo.append(StringUtils.isEmpty(req.getBillCode()) ? "“单据编号”不能为空!" : "");
				if(retInfo.length()>0) {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(retInfo.toString());
					return res;
				}
				RedRushForm redRushForm = new RedRushForm();
				redRushForm.setDjbh(req.getBillCode());
				//redRushForm.setYdjbh(req.getOriginalBillCode());
				//redRushForm.setChyydm(req.getRedReason());
				
				ObjectMapper mapper = new ObjectMapper();
				String responseStr="";
				 try {
					 String content = mapper.writeValueAsString(redRushForm);
					 String contentBase64 = Base64Util.encode(content);
					 Map<String,Object> map = getBWInvoiceReqData(account,contentBase64,"QDP-FP-10011");
					 if(StringUtils.isNotEmpty(map.get("retInfo").toString())) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(map.get("retInfo").toString());
						return res;
					 }
					 responseStr = map.get("responseStr").toString();
				 } catch (JsonProcessingException e) {
					 e.printStackTrace();
				 }
	
				JSONObject responseJson = new JSONObject(responseStr);
				if(responseJson.has("code") && 
						JsonUtil.getJSONNodeByPath(responseJson,"code").equals("0")){
					 try {
				         Thread.sleep(2000); 
				     } catch(InterruptedException ex) {
				    	 Thread.currentThread().interrupt();
				     }
					 req.setApiCode("QDP-FP-10012");
					res=(BWInvoiceRes) queryInvoiceResult(req,account);
				}else if(responseJson.has("message")) {
					res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
					res.setRetInfo(JsonUtil.getJSONNodeByPath(responseJson,"message")+JsonUtil.getJSONNodeByPath(responseJson,"renson"));
				}else {
					res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
				}
			}else if(null!=req.getInvoiceType() && req.getInvoiceType()==2) {//申请开红票
				retInfo.append(StringUtils.isEmpty(req.getBillCode()) ? "“单据编号”不能为空!" : "")
					.append(StringUtils.isEmpty(req.getOriginalBillCode())? "“原订单编号”不能为空!":"")
					.append(StringUtils.isEmpty(req.getRedReason()) ? "“冲红原因代码”不能为空!" : "");
				if(retInfo.length()>0) {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(retInfo.toString());
					return res;
				}
				RedRushForm redRushForm = new RedRushForm();
				redRushForm.setDjbh(req.getBillCode());
				redRushForm.setYdjbh(req.getOriginalBillCode());
				redRushForm.setChyydm(req.getRedReason());
				
				ObjectMapper mapper = new ObjectMapper();
				String responseStr="";
				 try {
					 String content = mapper.writeValueAsString(redRushForm);
					 String contentBase64 = Base64Util.encode(content);
					 Map<String,Object> map = getBWInvoiceReqData(account,contentBase64,"QDP-FP-10006");
					 if(StringUtils.isNotEmpty(map.get("retInfo").toString())) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(map.get("retInfo").toString());
						return res;
					 }
					 responseStr = map.get("responseStr").toString();
				 } catch (JsonProcessingException e) {
					 e.printStackTrace();
				 }
	
				JSONObject responseJson = new JSONObject(responseStr);
				if(responseJson.has("code") && 
						JsonUtil.getJSONNodeByPath(responseJson,"code").equals("0")){
					 try {
				         Thread.sleep(2000); 
				     } catch(InterruptedException ex) {
				    	 Thread.currentThread().interrupt();
				     }
					 req.setApiCode("QDP-FP-10007");
					res=(BWInvoiceRes) queryInvoiceResult(req,account);
				}else if(responseJson.has("message")) {
					res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
					res.setRetInfo(JsonUtil.getJSONNodeByPath(responseJson,"message")
							+JsonUtil.getJSONNodeByPath(responseJson,"renson"));
				}else {
					res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
				}
			
			}else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				return res;
			}

		}else {
			 res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
			 res.setRetInfo("请配置开票的账户信息!");
			 return res;
		}

		 return res;
	}*/
	
	//@Override
	public IResponse invoice(InvoiceReq req) {
		BWInvoiceRes res = new BWInvoiceRes(); 
		StringBuilder retInfo = new StringBuilder();
		retInfo.append(StringUtils.isEmpty(req.getReqSerialNo()) ? "“发票请求流水号”不能为空!" : "")
		.append(StringUtils.isEmpty(req.getBuyerName()) ? "“购货单位名称”不能为空!" : "")
		.append(StringUtils.isEmpty(req.getAmount()) ? "“合计金额”不能为空!" : "")
		.append(StringUtils.isEmpty(req.getTaxAmount()) ? "“合计税额”不能为空!" : "")
		.append(StringUtils.isEmpty(req.getTotalAmount()) ? "“价税合计”不能为空!" : "");
		req.setInvoiceKindCode(StringUtils.isNotEmpty(req.getInvoiceKindCode()) ? req.getInvoiceKindCode() : "82");
		req.setActType(StringUtils.isNotEmpty(req.getActType()) ? req.getActType() : "0");
//		InvoiceAccountEntity account = invoiceAccountDao.getUniqueByHql("select a "
//				+ "from InvoiceAccountEntity a  where a.state=1 and accountType=0", "");
		String token = getToken();
		Cache cache = cacheManager.getCache("baiwang-tokenCache");
		Map<String,Object> map = null;
		if(null != cache) {
			map = cache.get(account.getAccountName(),Map.class);
			if(null!=map) {
				
				req.setChecker(StringUtils.isNotEmpty(req.getChecker()) ? req.getChecker() : 
					(map.containsKey("checker") ? map.get("checker").toString():""));
				req.setSellerBankAccount(StringUtils.isNotEmpty(req.getSellerBankAccount()) ? req.getSellerBankAccount() : 
					(map.containsKey("sellerBankAccount") ? map.get("sellerBankAccount").toString():""));
				req.setChecker(StringUtils.isNotEmpty(req.getChecker()) ? req.getChecker() : 
					(map.containsKey("checker") ? map.get("checker").toString():""));
				req.setPayee(StringUtils.isNotEmpty(req.getPayee()) ? req.getPayee() : 
					(map.containsKey("payee") ? map.get("payee").toString():""));
				req.setDrawer(StringUtils.isNotEmpty(req.getDrawer()) ? req.getDrawer() : 
					(map.containsKey("drawer") ? map.get("drawer").toString():""));
				req.setBuyerNaturalPerson(StringUtils.isNotEmpty(req.getBuyerNaturalPerson()) ? req.getBuyerNaturalPerson() : 
					(map.containsKey("buyerNaturalPerson") ? map.get("buyerNaturalPerson").toString():"N"));
//				req.setPreparer(StringUtils.isNotEmpty(req.getPreparer()) ? req.getPreparer() : 
//					(map.containsKey("preparer")? map.get("preparer").toString():""));
				req.setSellerTaxCode(StringUtils.isNotEmpty(req.getSellerTaxCode()) ? req.getSellerTaxCode() : 
					(map.containsKey("sellerTaxCode") ? map.get("sellerTaxCode").toString():""));
				req.setSellerName(StringUtils.isNotEmpty(req.getSellerName()) ? req.getSellerName() : 
					(map.containsKey("sellerName") ? map.get("sellerName").toString():""));
				req.setSellerBusinessAddress(StringUtils.isNotEmpty(req.getSellerBusinessAddress()) ? req.getSellerBusinessAddress() : 
					(map.containsKey("sellerBusinessAddress") ? map.get("sellerBusinessAddress").toString():""));
				req.setSellerBusinessTel(StringUtils.isNotEmpty(req.getSellerBusinessTel()) ? req.getSellerBusinessTel() : 
					(map.containsKey("sellerBusinessTel") ? map.get("sellerBusinessTel").toString():""));
				req.setSellerBankDeposit(StringUtils.isNotEmpty(req.getSellerBankDeposit()) ? req.getSellerBankDeposit() : 
					(map.containsKey("sellerBankDeposit") ? map.get("sellerBankDeposit").toString():""));
				req.setTerminalCode(StringUtils.isNotEmpty(req.getTerminalCode()) ? req.getTerminalCode() : 
					(map.containsKey("terminalCode") ? map.get("terminalCode").toString():""));
				for(GoodsForm goods : req.getGoodsList()) {
					retInfo.append(StringUtils.isEmpty(goods.getGoodsName()) ? "“商品名称”不能为空!" : "")
					.append(StringUtils.isEmpty(goods.getTaxKindCode()) ? "“税收分类编码”不能为空!" : "")
					.append(null==goods.getLineKind() ? "“发票行性质”不能为空!" : "")
					.append(StringUtils.isEmpty(goods.getTaxRate()) ? "“税率”不能为空!" : "");
					if(retInfo.length()>0) {
						break;
					}
				}
				Map<String,InvoiceInterfaceEntity> interfaceMap = (Map<String, InvoiceInterfaceEntity>) map.get("interfaceMap");
				StringBuilder url = new StringBuilder(map.get("invoiceUrl")+"?");
				InvoiceInterfaceEntity invoiceInterface = (InvoiceInterfaceEntity)interfaceMap.get("baiwang.defp.issue");
				String method= invoiceInterface.getInterfaceName();
				req.setApiName(method);
				if(retInfo.length()>0) {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(retInfo.toString());
					return res;
				}
				ObjectMapper mapper = new ObjectMapper();
				String content ="";
				 try {
					 content = mapper.writeValueAsString(req);
				 } catch (JsonProcessingException e) {
					 e.printStackTrace();
				 }

				String appKey = map.get("appKey").toString();
				String timestamp = System.currentTimeMillis()+"";
				String uuid = UUID.randomUUID().toString();  
				Map<String, String> textParams = new HashMap<String, String>();
				textParams.put("method", method);
				textParams.put("appKey", appKey);
				textParams.put("token", token);
				textParams.put("version", "6.0");
				textParams.put("type", "sync");
				textParams.put("format", "json");
				textParams.put("signType", "9");
				textParams.put("requestId", uuid);
				textParams.put("timestamp", timestamp);
				String sign = signTopRequest( textParams,  map.get("appSecret").toString(), content);
		        url.append("method="+method)
		        	.append("&appKey="+appKey)
		        	.append("&token="+map.get("token"))
		        	.append("&sign="+sign+"&version=6.0&type=sync&format=json&signType=9")
		        	.append("&requestId="+uuid)
		        	.append("&timestamp="+timestamp);

				 String responseStr=post(account,map,token,method,content);
				 if(StringUtils.isNotEmpty(responseStr)) {
					 JSONObject resultJson = new JSONObject(responseStr);
					 if(resultJson.has("success") && 
								JsonUtil.getJSONNodeByPath(resultJson,"success").equals("true")) {
						 String resultData = JsonUtil.getJSONNodeByPath(resultJson,"response");
						 if(StringUtils.isNotEmpty(resultData) && !resultData.equals("null")) {
							// String data = Base64Util.decode(resultData);
							 JSONObject json = new JSONObject(resultData);
							 res.setInvoiceNo(JsonUtil.getJSONNodeByPath(json,"/invoiceNo"));
							 res.setInvoiceDate(JsonUtil.getJSONNodeByPath(json,"/invoiceDate"));
							 res.setPdfUrl(JsonUtil.getJSONNodeByPath(json,"/pdfUrl"));
							 res.setXmlUrl(JsonUtil.getJSONNodeByPath(json,"/xmlUrl"));
							 res.setOfdUrl(JsonUtil.getJSONNodeByPath(json,"/ofdUrl"));
						 }
						 String message = JsonUtil.getJSONNodeByPath(resultData,"message");
						 res.setRet(ResponseContext.RES_SUCCESS_CODE);
						 res.setRetInfo(message);

					 }else if(resultJson.has("errorResponse")) {
						 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
						 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"errorResponse/message"));
					 }else {
						 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
						 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
					 }
			 }else {
				 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
				 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
			 }
				
			}else {
				 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
				 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO+"（获取TOKEN或发票信息异常）");
			}
		}
		return res;
	}
	
	 public  String signTopRequest(Map<String, String> params, String secret, String body) {
		 ArrayList<String> keys = new ArrayList<>(params.keySet());
		 Collections.sort(keys);
		 StringBuilder query = new StringBuilder();
		 if (secret != null && secret.trim().length() > 0) {
			 query.append(secret);
		 }
		 for (String key : keys) {
			 String value = (String) params.get(key);
		     if ((StringUtils.isNotEmpty(key)) && (StringUtils.isNotEmpty(value))) {
		         query.append(key).append(value);
		     }
		 }
		 if (body != null && body.trim().length() > 0) {
		     body = StringUtils.trim(body);
		     query.append(body);
		 }
		 if (secret != null && secret.trim().length() > 0) {
		     query.append(secret);
		 }
		 String sign = MD5Util.getMd5(query.toString());
		 return sign.toString();
	}
	 
    public IResponse bWQuery(BWQueryReq req) {
    	BWInvoiceRes res = new BWInvoiceRes(); 
    	if(StringUtils.isEmpty(req.getQueryKind())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("查询方式不能为空！");
			return res;
    	}
    	if(req.getQueryKind().equals("0")&&(
    			StringUtils.isEmpty(req.getInvoiceNo()) || StringUtils.isEmpty(req.getInvoiceDate()))) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("查询方式不能为空！");
			return res;
    	}
    	
    	if(req.getQueryKind().equals("1")&&StringUtils.isEmpty(req.getReqSerialNo())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("“开票请求流水号”查询方式不能为空！");
			return res;
    	}
    	
//		InvoiceAccountEntity account = invoiceAccountDao.getUniqueByHql("select a "
//				+ "from InvoiceAccountEntity a  where a.state=1 and accountType=0", "");
		String token = getToken();
		Cache cache = cacheManager.getCache("baiwang-tokenCache");
		Map<String,Object> map = null;
		if(null != cache) {
			map = cache.get(account.getAccountName(),Map.class);
			if(null!=map) {
				Map<String,InvoiceInterfaceEntity> interfaceMap = (Map<String, InvoiceInterfaceEntity>) map.get("interfaceMap");
				InvoiceInterfaceEntity invoiceInterface = (InvoiceInterfaceEntity)interfaceMap.get("baiwang.defp.query");
				String method= invoiceInterface.getInterfaceName();
				req.setApiName(method);
				req.setSellerTaxCode(StringUtils.isNotEmpty(req.getSellerTaxCode()) ? req.getSellerTaxCode() : 
					(map.containsKey("sellerTaxCode") ? map.get("sellerTaxCode").toString():""));
				req.setTerminalCode(StringUtils.isNotEmpty(req.getTerminalCode()) ? req.getTerminalCode() : 
					(map.containsKey("terminalCode") ? map.get("terminalCode").toString():""));
				req.setInvoiceKindCode(StringUtils.isNotEmpty(req.getInvoiceKindCode()) ? req.getInvoiceKindCode() : "82");
				req.setQueryKind(StringUtils.isNotEmpty(req.getQueryKind()) ? req.getQueryKind() : "0");
				req.setFullInfo(StringUtils.isNotEmpty(req.getFullInfo()) ? req.getFullInfo() : "0");
				String content ="";
				 try {
					 content = mapper.writeValueAsString(req);
				 } catch (JsonProcessingException e) {
					 e.printStackTrace();
				 }
				 String responseStr=post(account,map,token,method,content);
				 if(StringUtils.isNotEmpty(responseStr)) {
					 JSONObject resultJson = new JSONObject(responseStr);
					 if(resultJson.has("success") && 
								JsonUtil.getJSONNodeByPath(resultJson,"success").equals("true")) {
						 String resultData = JsonUtil.getJSONNodeByPath(resultJson,"response");
						 if(StringUtils.isNotEmpty(resultData) && !resultData.equals("null")) {
							// String data = Base64Util.decode(resultData);
							 JSONObject json = new JSONObject(resultData);
							 res.setInvoiceNo(JsonUtil.getJSONNodeByPath(json,"/invoiceNo"));
							 res.setInvoiceDate(JsonUtil.getJSONNodeByPath(json,"/invoiceDate"));
							 res.setPdfUrl(JsonUtil.getJSONNodeByPath(json,"/pdfUrl"));
							 res.setXmlUrl(JsonUtil.getJSONNodeByPath(json,"/xmlUrl"));
							 res.setOfdUrl(JsonUtil.getJSONNodeByPath(json,"/ofdUrl"));
							 String message = JsonUtil.getJSONNodeByPath(resultData,"message");
							 res.setRet(ResponseContext.RES_SUCCESS_CODE);
							 res.setRetInfo(message);
						 }else {
							 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
							 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO+"等待发票文件返回！");
						 }

//						 if(message.contains("成功") || message.contains("完成")) {
//							 res.setRet(ResponseContext.RES_SUCCESS_CODE);
//							 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"message"));
//						 }else if(message.contains("开具中")){
//							 res.setRet(ResponseContext.RES_INVOICE_UNDERWAY_CODE);
//							 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"message"));
//						 }else{
//							 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
//							 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"message")
//									 +JsonUtil.getJSONNodeByPath(resultJson,"renson"));
//						 }
					 }else if(resultJson.has("errorResponse")) {
						 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
						 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"errorResponse/message"));
					 }else {
						 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
						 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
					 }
			 }else {
				 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
				 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
			 }
			}
		}
    	return res;
    } 
    
    public IResponse bWRedApply(BWRedApplyReq req) {
    	BWInvoiceRes res = new BWInvoiceRes(); 

		StringBuilder retInfo = new StringBuilder();
		retInfo.append(StringUtils.isEmpty(req.getOriginalInvoiceNo()) ? "“原数电发票号码”发票号码不能为空！" : "")
		.append(StringUtils.isEmpty(req.getOriginalInvoiceDate()) ? "“原开票日期”发票号码不能为空！" : "");
    	
    	if(StringUtils.isNotEmpty(retInfo.toString())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(retInfo.toString());
			return res;
    	}
//		InvoiceAccountEntity account = invoiceAccountDao.getUniqueByHql("select a "
//				+ "from InvoiceAccountEntity a  where a.state=1 and accountType=0", "");
		String token = getToken();
		Cache cache = cacheManager.getCache("baiwang-tokenCache");
		Map<String,Object> map = null;
		if(null != cache) {
			map = cache.get(account.getAccountName(),Map.class);
			if(null!=map) {
				Map<String,InvoiceInterfaceEntity> interfaceMap = (Map<String, InvoiceInterfaceEntity>) map.get("interfaceMap");
				InvoiceInterfaceEntity invoiceInterface = (InvoiceInterfaceEntity)interfaceMap.get("baiwang.defp.red.apply");
				String method= invoiceInterface.getInterfaceName();
				req.setApiName(method);
				req.setSellerTaxCode(StringUtils.isNotEmpty(req.getSellerTaxCode()) ? req.getSellerTaxCode() : 
					(map.containsKey("sellerTaxCode") ? map.get("sellerTaxCode").toString():""));
				req.setTerminalCode(StringUtils.isNotEmpty(req.getTerminalCode()) ? req.getTerminalCode() : 
					(map.containsKey("terminalCode") ? map.get("terminalCode").toString():""));
				req.setInvoiceUserType(StringUtils.isNotEmpty(req.getInvoiceUserType()) ? req.getInvoiceUserType() : "0");
				req.setInvoiceSource(StringUtils.isNotEmpty(req.getInvoiceSource()) ? req.getInvoiceSource() : "2");
				req.setApplyUserType(StringUtils.isNotEmpty(req.getApplyUserType()) ? req.getApplyUserType() : "0");
				String content ="";
				 try {
					 content = mapper.writeValueAsString(req);
				 } catch (JsonProcessingException e) {
					 e.printStackTrace();
				 }
				 String responseStr=post(account,map,token,method,content);
				 if(StringUtils.isNotEmpty(responseStr)) {
					 JSONObject resultJson = new JSONObject(responseStr);
					 if(resultJson.has("success") && 
								JsonUtil.getJSONNodeByPath(resultJson,"success").equals("true")) {
						 String resultData = JsonUtil.getJSONNodeByPath(resultJson,"response");
						 if(StringUtils.isNotEmpty(resultData) && !resultData.equals("null")) {
							// String data = Base64Util.decode(resultData);
							 JSONObject json = new JSONObject(resultData);	 
							 String redInfoId = JsonUtil.getJSONNodeByPath(json,"redInfoId");
							 String applyDate = JsonUtil.getJSONNodeByPath(json,"applyDate");
							 BWQueryReq bWQueryReq = new BWQueryReq();
							 bWQueryReq.setQueryKind("2");
							 bWQueryReq.setReqSerialNo(redInfoId);
							 bWQueryReq.setInvoiceDate(applyDate);
							 res.setRedInfoId(redInfoId);
							 res.setRedInfoNo(JsonUtil.getJSONNodeByPath(json,"redInfoNo"));
//							 try {
//						        Thread.sleep(20000); 
//						     } catch(InterruptedException ex) {
//						    	Thread.currentThread().interrupt();
//						     }
//							 res = (BWInvoiceRes) bWQuery(bWQueryReq);
							 res.setRet(ResponseContext.RES_SUCCESS_CODE);
							 res.setRetInfo(JsonUtil.getJSONNodeByPath(json,"message"));
						 }
					 }else if(resultJson.has("errorResponse")) {
						 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
						 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"errorResponse/message"));
					 }else {
						 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
						 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
					 }
			 }else {
				 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
				 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
			 }
			}
		}
    	return res;
    }
    
    public IResponse bWRedInfoQuery(BWQueryReq req) {
    	BWInvoiceRes res = new BWInvoiceRes(); 
    	if(StringUtils.isEmpty(req.getInvoiceNo())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("发票号码不能为空！");
			return res;
    	}
    	
//		InvoiceAccountEntity account = invoiceAccountDao.getUniqueByHql("select a "
//				+ "from InvoiceAccountEntity a  where a.state=1 and accountType=0", "");
		String token = getToken();
		Cache cache = cacheManager.getCache("baiwang-tokenCache");
		Map<String,Object> map = null;
		if(null != cache) {
			map = cache.get(account.getAccountName(),Map.class);
			if(null!=map) {
				Map<String,InvoiceInterfaceEntity> interfaceMap = (Map<String, InvoiceInterfaceEntity>) map.get("interfaceMap");
				InvoiceInterfaceEntity invoiceInterface = (InvoiceInterfaceEntity)interfaceMap.get("baiwang.defp.red.info.query");
				String method= invoiceInterface.getInterfaceName();
				req.setApiName(method);
				req.setSellerTaxCode(StringUtils.isNotEmpty(req.getSellerTaxCode()) ? req.getSellerTaxCode() : 
					(map.containsKey("sellerTaxCode") ? map.get("sellerTaxCode").toString():""));
				req.setTerminalCode(StringUtils.isNotEmpty(req.getTerminalCode()) ? req.getTerminalCode() : 
					(map.containsKey("terminalCode") ? map.get("terminalCode").toString():""));
				req.setInvoiceKindCode(StringUtils.isNotEmpty(req.getInvoiceKindCode()) ? req.getInvoiceKindCode() : "82");
				req.setQueryKind(StringUtils.isNotEmpty(req.getQueryKind()) ? req.getQueryKind() : "0");
				req.setFullInfo(StringUtils.isNotEmpty(req.getFullInfo()) ? req.getFullInfo() : "0");
				String content ="";
				 try {
					 content = mapper.writeValueAsString(req);
				 } catch (JsonProcessingException e) {
					 e.printStackTrace();
				 }
				 String responseStr=post(account,map,token,method,content);
				 if(StringUtils.isNotEmpty(responseStr)) {
					 JSONObject resultJson = new JSONObject(responseStr);
					 if(resultJson.has("success") && 
								JsonUtil.getJSONNodeByPath(resultJson,"success").equals("true")) {
						 String resultData = JsonUtil.getJSONNodeByPath(resultJson,"response");
						 if(StringUtils.isNotEmpty(resultData) && !resultData.equals("null")) {
							// String data = Base64Util.decode(resultData);
							 JSONObject json = new JSONObject(resultData);
							 res.setInvoiceNo(JsonUtil.getJSONNodeByPath(json,"/invoiceNo"));
							 res.setInvoiceDate(JsonUtil.getJSONNodeByPath(json,"/invoiceDate"));
							 res.setPdfUrl(JsonUtil.getJSONNodeByPath(json,"/pdfUrl"));
							 res.setXmlUrl(JsonUtil.getJSONNodeByPath(json,"/xmlUrl"));
							 res.setOfdUrl(JsonUtil.getJSONNodeByPath(json,"/ofdUrl"));
						 }
						 String message = JsonUtil.getJSONNodeByPath(resultData,"message");
						 if(message.contains("成功") || message.contains("完成")) {
							 res.setRet(ResponseContext.RES_SUCCESS_CODE);
							 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"message"));
						 }else if(message.contains("开具中")){
							 res.setRet(ResponseContext.RES_INVOICE_UNDERWAY_CODE);
							 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"message"));
						 }else{
							 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
							 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"message")
									 +JsonUtil.getJSONNodeByPath(resultJson,"renson"));
						 }
					 }else if(resultJson.has("errorResponse")) {
						 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
						 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"errorResponse/message"));
					 }else {
						 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
						 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
					 }
			 }else {
				 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
				 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
			 }
			}
		}
    	return res;
    } 
    
    public IResponse redIssue(RedIssueReq req) {
    	BWInvoiceRes res = new BWInvoiceRes(); 
    	if(StringUtils.isEmpty(req.getReqSerialNo())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("开票序列号不能为空！");
			return res;
    	}
    	
    	if(StringUtils.isEmpty(req.getRedInfoId())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("红字信息确认单id不能为空！");
			return res;
    	}
    	
    	if(StringUtils.isEmpty(req.getOriginalInvoiceKindCode())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("原发票类型代码不能为空！");
			return res;
    	}
    	
    	if(StringUtils.isEmpty(req.getOriginalInvoiceNo())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("开票序列号不能为空！");
			return res;
    	}
    	
//    	
//		InvoiceAccountEntity account = invoiceAccountDao.getUniqueByHql("select a "
//				+ "from InvoiceAccountEntity a  where a.state=1 and accountType=0", "");
		String token = getToken();
		Cache cache = cacheManager.getCache("baiwang-tokenCache");
		Map<String,Object> map = null;
		if(null != cache) {
			map = cache.get(account.getAccountName(),Map.class);
			if(null!=map) {
				Map<String,InvoiceInterfaceEntity> interfaceMap = (Map<String, InvoiceInterfaceEntity>) map.get("interfaceMap");
				InvoiceInterfaceEntity invoiceInterface = (InvoiceInterfaceEntity)interfaceMap.get("baiwang.defp.red.issue");
				String method= invoiceInterface.getInterfaceName();
				req.setApiName(method);
				req.setSellerTaxCode(StringUtils.isNotEmpty(req.getSellerTaxCode()) ? req.getSellerTaxCode() : 
					(map.containsKey("sellerTaxCode") ? map.get("sellerTaxCode").toString():""));
				req.setTerminalCode(StringUtils.isNotEmpty(req.getTerminalCode()) ? req.getTerminalCode() : 
					(map.containsKey("terminalCode") ? map.get("terminalCode").toString():""));
				req.setInvoiceKindCode(StringUtils.isNotEmpty(req.getInvoiceKindCode()) ? req.getInvoiceKindCode() : "82");
				String content ="";
				 try {
					 content = mapper.writeValueAsString(req);
				 } catch (JsonProcessingException e) {
					 e.printStackTrace();
				 }
				 String responseStr=post(account,map,token,method,content);
				 if(StringUtils.isNotEmpty(responseStr)) {
					 JSONObject resultJson = new JSONObject(responseStr);
					 if(resultJson.has("success") && 
								JsonUtil.getJSONNodeByPath(resultJson,"success").equals("true")) {
						 String resultData = JsonUtil.getJSONNodeByPath(resultJson,"response");
//						 if(StringUtils.isNotEmpty(resultData) && !resultData.equals("null")) {
//							// String data = Base64Util.decode(resultData);
//							 JSONObject json = new JSONObject(resultData);
//							 res.setInvoiceNo(JsonUtil.getJSONNodeByPath(json,"/invoiceNo"));
//							 res.setInvoiceDate(JsonUtil.getJSONNodeByPath(json,"/invoiceDate"));
//							 res.setPdfUrl(JsonUtil.getJSONNodeByPath(json,"/pdfUrl"));
//							 res.setXmlUrl(JsonUtil.getJSONNodeByPath(json,"/xmlUrl"));
//							 res.setOfdUrl(JsonUtil.getJSONNodeByPath(json,"/ofdUrl"));
//						 }
						 String message = JsonUtil.getJSONNodeByPath(resultData,"message");
						 if((message.contains("成功") || message.contains("完成"))&& StringUtils.isNotEmpty(resultData) && !resultData.equals("null")) {
							 JSONObject json = new JSONObject(resultData);
							 res.setInvoiceNo(JsonUtil.getJSONNodeByPath(json,"/invoiceNo"));
							 res.setInvoiceDate(JsonUtil.getJSONNodeByPath(json,"/invoiceDate"));
							 res.setPdfUrl(JsonUtil.getJSONNodeByPath(json,"/pdfUrl"));
							 res.setXmlUrl(JsonUtil.getJSONNodeByPath(json,"/xmlUrl"));
							 res.setOfdUrl(JsonUtil.getJSONNodeByPath(json,"/ofdUrl"));
							 res.setRet(ResponseContext.RES_SUCCESS_CODE);
							 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"message"));
						 }else if(message.contains("开具中")|| ((message.contains("成功") || message.contains("完成"))
								 && StringUtils.isEmpty(resultData)) ){
							 res.setRet(ResponseContext.RES_INVOICE_UNDERWAY_CODE);
							 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"message"));
						 }else{
							 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
							 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"message")
									 +JsonUtil.getJSONNodeByPath(resultJson,"renson"));
						 }
					 }else if(resultJson.has("errorResponse")) {
						 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
						 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"errorResponse/message"));
					 }else {
						 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
						 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
					 }
			 }else {
				 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
				 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
			 }
			}
		}
    	return res;
    }
    
    public IResponse redInfoOperate(RedInfoOperateReq req) {
    	BWInvoiceRes res = new BWInvoiceRes(); 
    	if(StringUtils.isEmpty(req.getRedInfoId())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("红字信息确认单id");
			return res;
    	}
//    	
//		InvoiceAccountEntity account = invoiceAccountDao.getUniqueByHql("select a "
//				+ "from InvoiceAccountEntity a  where a.state=1 and accountType=0", "");
		String token = getToken();
		Cache cache = cacheManager.getCache("baiwang-tokenCache");
		Map<String,Object> map = null;
		if(null != cache) {
			map = cache.get(account.getAccountName(),Map.class);
			if(null!=map) {
				Map<String,InvoiceInterfaceEntity> interfaceMap = (Map<String, InvoiceInterfaceEntity>) map.get("interfaceMap");
				InvoiceInterfaceEntity invoiceInterface = (InvoiceInterfaceEntity)interfaceMap.get("baiwang.defp.red.info.operate");
				String method= invoiceInterface.getInterfaceName();
				req.setApiName(method);
				req.setSellerTaxCode(StringUtils.isNotEmpty(req.getSellerTaxCode()) ? req.getSellerTaxCode() : 
					(map.containsKey("sellerTaxCode") ? map.get("sellerTaxCode").toString():""));
				req.setTerminalCode(StringUtils.isNotEmpty(req.getTerminalCode()) ? req.getTerminalCode() : 
					(map.containsKey("terminalCode") ? map.get("terminalCode").toString():""));
				req.setActionType(StringUtils.isNotEmpty(req.getActionType()) ? req.getActionType() : "1");
				String content ="";
				 try {
					 content = mapper.writeValueAsString(req);
				 } catch (JsonProcessingException e) {
					 e.printStackTrace();
				 }
				 String responseStr=post(account,map,token,method,content);
				 if(StringUtils.isNotEmpty(responseStr)) {
					 JSONObject resultJson = new JSONObject(responseStr);
					 if(resultJson.has("success") && 
								JsonUtil.getJSONNodeByPath(resultJson,"success").equals("true")) {
						 String resultData = JsonUtil.getJSONNodeByPath(resultJson,"response");
						 if(StringUtils.isNotEmpty(resultData) && !resultData.equals("null")) {
							// String data = Base64Util.decode(resultData);
							 JSONObject json = new JSONObject(resultData);
							 res.setInvoiceNo(JsonUtil.getJSONNodeByPath(json,"/invoiceNo"));
							 res.setInvoiceDate(JsonUtil.getJSONNodeByPath(json,"/invoiceDate"));
							 res.setPdfUrl(JsonUtil.getJSONNodeByPath(json,"/pdfUrl"));
							 res.setXmlUrl(JsonUtil.getJSONNodeByPath(json,"/xmlUrl"));
							 res.setOfdUrl(JsonUtil.getJSONNodeByPath(json,"/ofdUrl"));
						 }
						 String message = JsonUtil.getJSONNodeByPath(resultData,"message");
						 if(message.contains("成功") || message.contains("完成")) {
							 res.setRet(ResponseContext.RES_SUCCESS_CODE);
							 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"message"));
						 }else if(message.contains("开具中")){
							 res.setRet(ResponseContext.RES_INVOICE_UNDERWAY_CODE);
							 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"message"));
						 }else{
							 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
							 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"message")
									 +JsonUtil.getJSONNodeByPath(resultJson,"renson"));
						 }
					 }else if(resultJson.has("errorResponse")) {
						 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
						 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"errorResponse/message"));
					 }else {
						 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
						 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
					 }
			 }else {
				 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
				 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
			 }
			}
		}
    	return res;
    }
	
	public String post(InvoiceAccountEntity account,Map<String,Object> map,String token,String method,String requestBody) {
		StringBuilder url = new StringBuilder(map.get("invoiceUrl")+"?");
		String appKey = map.get("appKey").toString();
		String timestamp = System.currentTimeMillis()+"";
		String uuid = UUID.randomUUID().toString();  
		Map<String, String> textParams = new HashMap<String, String>();
		textParams.put("method", method);
		textParams.put("appKey", appKey);
		textParams.put("token", token);
		textParams.put("version", "6.0");
		textParams.put("type", "sync");
		textParams.put("format", "json");
		textParams.put("signType", "9");
		textParams.put("requestId", uuid);
		textParams.put("timestamp", timestamp);
		String sign = signTopRequest( textParams,  map.get("appSecret").toString(), requestBody);
        url.append("method="+method)
        	.append("&appKey="+appKey)
        	.append("&token="+map.get("token"))
//        	.append("&username="+map.get("accountName")+"&")
//        	.append("&password="+BWEncryptionUtil.md5AndSha(account.getPassword()+
//        			map.get("userSalt"))+"&sign="+sign+"&version=6.0&type=sync&format=json")
        	.append("&sign="+sign+"&version=6.0&type=sync&format=json&signType=9")
        	.append("&requestId="+uuid)
        	.append("&timestamp="+timestamp);

		//String responseStr=post(account,url.toString(),content);
		
		String responseStr="";
		try {
			responseStr = HttpClientUtil.jsonPost(url.toString(),"UTF-8",requestBody,null);
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		InvoicePostEntity post = new InvoicePostEntity();
		post.setThirdAccountId(null);
		post.setAccountId(account.getId());
		post.setUrl(url.toString());
		post.setRequest(requestBody);
		post.setResponse(responseStr);
		invoicePostDao.save(post);

		return responseStr;
	}
	
	public IResponse queryInvoiceResult(InvoiceReq req,InvoiceAccountEntity account ){
		BWInvoiceRes res = new BWInvoiceRes();
//		if(null!=req.getInvoiceType() && req.getInvoiceType()==0) {
//			req.setApiCode("QDP-FP-10003");
//		}else if(null!=req.getInvoiceType() && req.getInvoiceType()==1) {
//			req.setApiCode("QDP-FP-10012");
//		}else if(null!=req.getInvoiceType() && req.getInvoiceType()==2) {
//			req.setApiCode("QDP-FP-10007");
//		}else if(StringUtils.isEmpty(req.getApiCode())){
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//			return res;
//		}
//		if(null == account) {
//			account = invoiceAccountDao.getUniqueByHql("select a "
//					+ "from InvoiceAccountEntity a ", "");
//		}
//		BWInvoiceResultContentForm bWInvoiceResultContentForm = new BWInvoiceResultContentForm();
//		bWInvoiceResultContentForm.setDjbh(req.getBillCode());
//		String resultStr ="";
//		 try {
//			 String content = mapper.writeValueAsString(bWInvoiceResultContentForm);
//			 String contentBase64 = Base64Util.encode(content);
//			 Map<String,Object> map = getBWInvoiceReqData(account,contentBase64,req.getApiCode());
//			 if(StringUtils.isNotEmpty(map.get("retInfo").toString())) {
//				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
//				res.setRetInfo(map.get("retInfo").toString());
//				return res;
//			 }
//			 resultStr = map.get("responseStr").toString();
//		 } catch (JsonProcessingException e) {
//			 e.printStackTrace();
//		 }
//		 if(StringUtils.isNotEmpty(resultStr)) {
//			 JSONObject resultJson = new JSONObject(resultStr);
//			 if(resultJson.has("code") && 
//						JsonUtil.getJSONNodeByPath(resultJson,"code").equals("0")) {
//				 String resultData = JsonUtil.getJSONNodeByPath(resultJson,"/data");
//				 if(StringUtils.isNotEmpty(resultData) && !resultData.equals("null")) {
//					 String data = Base64Util.decode(resultData);
//					 JSONObject json = new JSONObject(data);
//					 res.setInvoiceNo(JsonUtil.getJSONNodeByPath(json,"/qdfphm"));
//					 res.setInvoiceDate(JsonUtil.getJSONNodeByPath(json,"/kprq"));
//					 res.setPdfUrl(JsonUtil.getJSONNodeByPath(json,"/pdf"));
//					 res.setXmlUrl(JsonUtil.getJSONNodeByPath(json,"/xml"));
//					 res.setOfdUrl(JsonUtil.getJSONNodeByPath(json,"/ofd"));
//				 }
//				 String message = JsonUtil.getJSONNodeByPath(resultJson,"message");
//				 if(message.contains("成功") || message.contains("完成")) {
//					 res.setRet(ResponseContext.RES_SUCCESS_CODE);
//					 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"message"));
//				 }else if(message.contains("开具中")){
//					 res.setRet(ResponseContext.RES_INVOICE_UNDERWAY_CODE);
//					 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"message"));
//				 }else{
//					 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
//					 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"message")
//							 +JsonUtil.getJSONNodeByPath(resultJson,"renson"));
//				 }
//			 }else if(resultJson.has("message")) {
//				 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
//				 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"message")
//						 +JsonUtil.getJSONNodeByPath(resultJson,"renson"));
//			 }else {
//				 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
//				 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
//			 }
//		 }else {
//			 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
//			 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
//		 }
//		 
		 return res;
	}
	
	public Map<String,Object> getBWInvoiceReqData(InvoiceAccountEntity account,String contentBase64,String interfaceCode) {
		Map<String,Object> map = new HashMap<>();
		BWInvoiceReq bWInvoiceReq = new BWInvoiceReq();
		StringBuilder retInfo = new StringBuilder();
		bWInvoiceReq.setAppid(account.getAccountName());
		//bWInvoiceReq.setSignType("1");
		bWInvoiceReq.setSignType("3");
		InvoiceUrlEntity invoiceUrl = invoiceUrlDao.getUniqueByHql("select a from InvoiceUrlEntity a inner j"
				+ "oin a.accountList b where a.urlType=0 and a.isOpen=1 and b.id="+account.getId(), "");
		String interfaceName = "";
		if(null != invoiceUrl) {
			InvoiceInterfaceEntity invoiceInterface = invoiceInterfaceDao.getUniqueByHql("select a from InvoiceInterfaceEntity a inner join a.invoiceUrl b where "
					+ " b.id="+invoiceUrl.getId()+" and a.interfaceCode='"+interfaceCode+"' ", "");
			if(null != invoiceInterface ) {
				interfaceName = invoiceInterface.getInterfaceName();
				bWInvoiceReq.setServiceid(interfaceName);
			}else {
				retInfo.append("请配置获取token的接口！");
			}
		}else {
			retInfo.append("请配置请求地址");
		}
		
		String requestBody = "";
		 try {
			 String signString = MD5Util.getMd5(account.getAccountName()+account.getPassword()+contentBase64+interfaceName);
			 bWInvoiceReq.setContent(contentBase64);
			 //bWInvoiceReq.setSignature(signString);
			 bWInvoiceReq.setSignature("  ");
			 requestBody = mapper.writeValueAsString(bWInvoiceReq);
		 } catch (JsonProcessingException e) {
			 e.printStackTrace();
		 }
		String responseStr="";
		try {
			responseStr = HttpClientUtil.jsonPost(invoiceUrl.getUrl(),"UTF-8",requestBody,null);
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		InvoicePostEntity post = new InvoicePostEntity();
		post.setThirdAccountId(null);
		post.setAccountId(account.getId());
		post.setUrl(invoiceUrl.getUrl());
		post.setRequest(requestBody);
		post.setResponse(responseStr);
		invoicePostDao.save(post);
		
		map.put("retInfo", retInfo.toString());
		map.put("responseStr", responseStr);
		return map;
	}
	
	
	
	@Override
	public IResponse withdrawalRedRushApplication(InvoiceReq req) {
		BWInvoiceRes res = new BWInvoiceRes();
//		if(StringUtils.isEmpty(req.getBillCode())){
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//			return res;
//		}
//		InvoiceAccountEntity account = invoiceAccountDao.getUniqueByHql("select a "
//				+ "from InvoiceAccountEntity a ", "");
//		if(null != account) {
//			RedRushForm redRushForm = new RedRushForm();
//			redRushForm.setDjbh(req.getBillCode());
//			ObjectMapper mapper = new ObjectMapper();
//			String responseStr="";
//			try {
//				 String content = mapper.writeValueAsString(redRushForm);
//				 String contentBase64 = Base64Util.encode(content);
//				 Map<String,Object> map = getBWInvoiceReqData(account,contentBase64,"QDP-FP-10008");
//				 if(StringUtils.isNotEmpty(map.get("retInfo").toString())) {
//					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
//					res.setRetInfo(map.get("retInfo").toString());
//					return res;
//				 }
//				 responseStr = map.get("responseStr").toString();
//			 } catch (JsonProcessingException e) {
//				 e.printStackTrace();
//			 }
//
//			JSONObject responseJson = new JSONObject(responseStr);
//			if(responseJson.has("code") && 
//					JsonUtil.getJSONNodeByPath(responseJson,"code").equals("0")){
//				try {
//			        Thread.sleep(2000); 
//			    } catch(InterruptedException ex) {
//			    	Thread.currentThread().interrupt();
//			    }
//				req.setApiCode("QDP-FP-10009");
//				res=(BWInvoiceRes) queryInvoiceResult(req,account);
//			}else if(responseJson.has("message")) {
//				res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
//				res.setRetInfo(JsonUtil.getJSONNodeByPath(responseJson,"message")+JsonUtil.getJSONNodeByPath(responseJson,"renson"));
//			}else {
//				res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
//				res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
//			}
//		}else {
//			 res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
//			 res.setRetInfo("请配置开票的账户信息!");
//			 return res;
//		}
		return res;
	}
	
	public IResponse queryWithdrawalRedRushApplicationResult(InvoiceReq req){
//		req.setApiCode("QDP-FP-10009");
		return queryInvoiceResult(req,null);
	}
	
	public IResponse queryQuota(){
		GetQuotaRes res = new GetQuotaRes();
		InvoiceAccountEntity account = invoiceAccountDao.getUniqueByHql("select a "
				+ "from InvoiceAccountEntity a ", "");
		if(null != account) {
			QuotaForm quotaForm = new QuotaForm();
			JSONObject accountParameterJson = new JSONObject(account.getParameter());
			quotaForm.setKpzddm(JsonUtil.getJSONNodeByPath(accountParameterJson,"kpzddm"));
			quotaForm.setDjbh("");
			ObjectMapper mapper = new ObjectMapper();
			String responseStr="";
			try {
				 String content = mapper.writeValueAsString(quotaForm);
				 String contentBase64 = Base64Util.encode(content);
				 Map<String,Object> map = getBWInvoiceReqData(account,contentBase64,"QDP-QYXX-10002");
				 if(StringUtils.isNotEmpty(map.get("retInfo").toString())) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(map.get("retInfo").toString());
					return res;
				 }
				 responseStr = map.get("responseStr").toString();
			 } catch (JsonProcessingException e) {
				 e.printStackTrace();
			 }

			JSONObject responseJson = new JSONObject(responseStr);
			if(responseJson.has("code") && 
					JsonUtil.getJSONNodeByPath(responseJson,"code").equals("0")){
				 String resultData = JsonUtil.getJSONNodeByPath(responseJson,"/data");
				 if(StringUtils.isNotEmpty(resultData) && !resultData.equals("null")) {
					 String data = Base64Util.decode(resultData);
					 JSONObject json = new JSONObject(data);
					 res.setTotalQuota(JsonUtil.getJSONNodeByPath(json,"/Zsxed"));
					 res.setUsedQuota(JsonUtil.getJSONNodeByPath(json,"/Ysysxed"));
					 res.setResidualQuota(JsonUtil.getJSONNodeByPath(json,"/Sysxed"));
					 res.setResidualNumberOfVATInvoices(JsonUtil.getJSONNodeByPath(json,"/Syzzfpzs"));
					 res.setNumberOfInvoicesUsed(JsonUtil.getJSONNodeByPath(json,"/Ysyfpzs"));
					 res.setTotalInvoiceNumber(JsonUtil.getJSONNodeByPath(json,"/Zfpzs"));
					 res.setTotalInvoiceAmount(JsonUtil.getJSONNodeByPath(json,"/Fphjje"));
				 }
				 res.setRet(ResponseContext.RES_SUCCESS_CODE);
				 res.setRetInfo(JsonUtil.getJSONNodeByPath(responseJson,"message"));
			}else if(responseJson.has("message")) {
				res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
				res.setRetInfo(JsonUtil.getJSONNodeByPath(responseJson,"message")+JsonUtil.getJSONNodeByPath(responseJson,"renson"));
			}else {
				res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
			}
		}else {
			 res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
			 res.setRetInfo("请配置开票的账户信息!");
			 return res;
		}
		return res;
	}
	
	
//	public Map<String,Object> getInvoiceAccountInfo(){
//		 InvoiceAccountEntity account = invoiceAccountDao.getUniqueByHql("select a "
//			 		+ "from InvoiceAccountEntity a ", "");
//		 Map<String,Object> map = null;
//		 Cache cache = cacheManager.getCache("baiwang-tokenCache");
////			System.out.println("++++++++++++++"+cache.evictIfPresent(account.getAccountName()));
//		 if(null != cache && cache.evictIfPresent(account.getAccountName())) {
//			map = cache.get(account.getAccountName(),Map.class);
//			Date date = (Date) map.get("expiresTime");
//			if(!new Date().before(date)) {
//				map = getToken(map);
//			}
//		 }else {
//			map =  getTokenByAccount(account);
//		 }
//		 return map;
//	}
//	
//	//获取token并把相关数据放入缓存
//	public Map<String,Object>  getTokenByAccount(InvoiceAccountEntity account) {
//		Map<String,Object> m = new HashMap<>();
//		String retInfo="";
//		InvoiceUrlEntity invoiceUrl = invoiceUrlDao.getUniqueByHql("select a from InvoiceUrlEntity a inner j"
//				+ "oin a.accountList b where a.urlType=0 and a.isOpen=1 and b.id="+account.getId(), "");
//		if(null != invoiceUrl) {
//			account.getSellerInformationList().forEach(o->{
//				if(o.getUseState() == 1) {
//					m.put("sellerAddressTel", o.getSellerAddressTel());
//					m.put("sellerBankAccount", o.getSellerBankAccount());
//					m.put("sellerContact", o.getSellerContact());
//					m.put("sellerName", o.getSellerName());
//					m.put("sellerTaxCode", o.getSellerTaxCode());
//					m.put("terminalCode", o.getTerminalCode());
//					m.put("checker", o.getChecker());
//					m.put("payee", o.getPayee());
//					m.put("drawer", o.getDrawer());
//				}
//			});
//			JSONObject accountParameterJson = new JSONObject(account.getParameter());
//			String encryptedPassword = BWEncryptionUtil.encrypt(
//					BWEncryptionUtil.md5(account.getPassword()+JsonUtil.getJSONNodeByPath(accountParameterJson,"userSalt")),"sha-1");
//			List<InvoiceInterfaceEntity> interfaceList = invoiceInterfaceDao.getListByHql("select a from InvoiceInterfaceEntity a inner join a.invoiceUrl b where "
//					+ " b.id="+invoiceUrl.getId()+" and (a.interfaceCode='baiwang.oauth.token' or a.interfaceCode='baiwang.invoice.issue')", "");
//			if(null != interfaceList && interfaceList.size()>0) {
//				m.put("accountName", account.getAccountName());
//				m.put("invoiceUrl", invoiceUrl.getUrl());
//				m.put("password", account.getPassword());
//				m.put("encryptedPassword", encryptedPassword);
//				m.put("appKey", JsonUtil.getJSONNodeByPath(accountParameterJson,"appKey"));
//				m.put("appSecret", JsonUtil.getJSONNodeByPath(accountParameterJson,"appSecret"));
//				m.put("userSalt", JsonUtil.getJSONNodeByPath(accountParameterJson,"userSalt"));
//				for(InvoiceInterfaceEntity invoiceInterface : interfaceList) {
//					if(invoiceInterface.getInterfaceName().equals("baiwang.oauth.token")) {
//						m.put("tokenInterface", invoiceInterface.getInterfaceName());
//						m.put("tokenInterfaceParameter", invoiceInterface.getParameter());
//					}else if(invoiceInterface.getInterfaceName().equals("baiwang.invoice.issue")) {
//						m.put("invoiceInterface", invoiceInterface.getInterfaceName());
//						m.put("invoiceInterfaceParameter", invoiceInterface.getParameter());
//					}
//				}
//			}else {
//				retInfo = "请配置获取token的接口！";
//			}
//		}else {
//			retInfo = "请配置请求地址！";
//		}
//		m.put("retInfo", retInfo);
//		return getToken(m);
//	}
//	
//	public Map<String,Object> getToken(Map<String,Object> m) {
//		if(((String)m.get("retInfo")).length()>0) {
//			return m;
//		}
//		String retInfo="";
//		Cache cache = cacheManager.getCache("baiwang-tokenCache");
//		StringBuilder url = new StringBuilder(m.get("invoiceUrl")+"?");
//		JSONObject invoiceInterfaceJson = new JSONObject((String)m.get("tokenInterfaceParameter"));
//		Iterator iterator = invoiceInterfaceJson.keys();  
//        while(iterator.hasNext()){  
//        	String key = (String) iterator.next();  
//        	if(key.contains("method")) {
//        		url.append("method="+m.get("tokenInterface")+"&");
//        	}else if(key.contains("username")) {
//        		url.append("username="+m.get("accountName")+"&");
//        	}else if(key.contains("password")) {
//        		url.append("password="+m.get("encryptedPassword")+"&");
//        	}else if(key.contains("timestamp")) {
//        		url.append("timestamp="+System.currentTimeMillis()+"&");
//        	}else {
//        		url.append(key+"="+invoiceInterfaceJson.getString(key)+"&");
//        	}
//        }  
//        try {
//			String responseStr = HttpClientUtil.jsonPost(url.toString(),"UTF-8","",null);
//			JSONObject responseJson = new JSONObject(responseStr);
//			if(responseJson.has("success") && JsonUtil.getJSONNodeByPath(responseJson,"success")
//					.toLowerCase().equals("true")){
//				try {
//					Date date = DateUtil.parse(JsonUtil.getJSONNodeByPath(responseJson,"/response/expires_time")
//							.toLowerCase(), 2);
//					String token = JsonUtil.getJSONNodeByPath(responseJson,"/response/access_token");
//					
//					m.put("token", token);
//					m.put("expiresTime", date);
//
//					cache.put(m.get("accountName"), m);
//				} catch (ParseException e) {
//					e.printStackTrace();
//				}
//			}else if(responseJson.has("success") && 
//					JsonUtil.getJSONNodeByPath(responseJson,"success")
//					.toLowerCase().equals("false")){
//				String message = JsonUtil.getJSONNodeByPath(responseJson,"/errorResponse/message");
//				retInfo=message;
//			}else {
//				retInfo="获取TOKEN异常！";
//			}
//		} catch (ClientProtocolException e) {
//			e.printStackTrace();
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
//        m.put("retInfo", retInfo);
//        return m;
//	}
//	
//	public void queryInvoice(QueryInvoiceReq req) {
//		
//		
//		
//	}
	
	public IResponse updateToken() {
		GenericResponse res = new GenericResponse();
		account = invoiceAccountDao.getUniqueByHql("select a "
				+ "from InvoiceAccountEntity a  where a.state=1 and accountType=0", "");
		if(null == account) {
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo("请配置发票账户信息！");
			return res;
		}
		Map<String,Object> map = getTokenByAccount();
		if (StringUtils.isEmpty(map.get("retInfo").toString())) {
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_SPEECH_SOUNDS_TOKEN_CODE);
			res.setRetInfo(map.containsKey("retInfo") ? map.get("retInfo").toString() : "获取TOKEN异常！");
		}
		return res;
	}
	
	public String getToken(){

		Map<String,Object> map = null;
		Cache cache = cacheManager.getCache("baiwang-tokenCache");
		if(null != cache) {
			if(null == account) {
				account = invoiceAccountDao.getUniqueByHql("select a "
						+ "from InvoiceAccountEntity a  where a.state=1 and accountType=0", "");
				if(null == account) return "";
			}
			map = cache.get(account.getAccountName().toString(),Map.class);
			if(null!= map) {
				Date date = (Date) map.get("expiresTime");
				if(!new Date().before(date)) {
					map = getTokenByAccount();
					if(StringUtils.isNotEmpty(map.get("retInfo").toString())) {
						return map.get("token").toString();
					}else {
						return "";
					}
				}else {
					return map.get("token").toString();
				}
			}else {
				map =  getTokenByAccount();
				if(null==map.get("retInfo") || 
						(null!=map.get("retInfo")&&StringUtils.isEmpty(map.get("retInfo").toString()))) {
					return map.get("token").toString();
				}else {
					return "";
				}
			}

		}else {
			map =  getTokenByAccount();
			if(StringUtils.isEmpty(map.get("retInfo").toString())) {
				return map.get("token").toString();
			}else {
				return "";
			}
		}

	}
	
	public Map<String,Object> getTokenByAccount() {
		String retInfo="";
		Cache cache = cacheManager.getCache("baiwang-tokenCache");
		Map<String,Object> m = new HashMap<>();
		if(null !=account && account.getAccountType()==0) {
			InvoiceUrlEntity invoiceUrl = invoiceUrlDao.getUniqueByHql("select a from InvoiceUrlEntity a inner j"
					+ "oin a.accountList b where a.urlType=0 and a.isOpen=1 and b.id="+account.getId(), "");
			if(null != invoiceUrl) {
				account.getSellerInformationList().forEach(o->{
					if(o.getUseState() == 1) {
						m.put("sellerBusinessAddress", StringUtils.isNotEmpty(o.getSellerBusinessAddress())? o.getSellerBusinessAddress():"");
						m.put("sellerBusinessTel", StringUtils.isNotEmpty(o.getSellerBusinessTel())?o.getSellerBusinessTel():"");
						m.put("sellerBankAccount", StringUtils.isNotEmpty(o.getSellerBankAccount())?o.getSellerBankAccount():"");
						m.put("sellerContact", StringUtils.isNotEmpty(o.getSellerContact())?o.getSellerContact():"");
						m.put("sellerName", StringUtils.isNotEmpty(o.getSellerName())?o.getSellerName():"");
						m.put("sellerTaxCode", StringUtils.isNotEmpty(o.getSellerTaxCode())?o.getSellerTaxCode():"");
						m.put("terminalCode", StringUtils.isNotEmpty(o.getTerminalCode())?o.getTerminalCode():"");
						m.put("checker", StringUtils.isNotEmpty(o.getChecker())?o.getChecker():"");
						m.put("payee", StringUtils.isNotEmpty(o.getPayee())?o.getPayee():"");
						m.put("drawer", StringUtils.isNotEmpty(o.getDrawer())?o.getDrawer():"");
						m.put("preparer", StringUtils.isNotEmpty(o.getPreparer())?o.getPreparer():"");
						m.put("sellerBankDeposit", StringUtils.isNotEmpty(o.getSellerBankDeposit())?o.getSellerBankDeposit():"");
					}
				});
				JSONObject accountParameterJson = new JSONObject(account.getParameter());
//				String encryptedPassword = BWEncryptionUtil.encrypt(
//						BWEncryptionUtil.md5(account.getPassword()+JsonUtil.getJSONNodeByPath(accountParameterJson,"userSalt")),"sha-1");
				List<InvoiceInterfaceEntity> interfaceList = invoiceInterfaceDao.getListByHql("select a from InvoiceInterfaceEntity a inner join a.invoiceUrl b where "
						+ " b.id="+invoiceUrl.getId(), "");
				if(null != interfaceList && interfaceList.size()>0) {
					m.put("accountName", account.getAccountName());
					m.put("invoiceUrl", invoiceUrl.getUrl());
					m.put("appKey", JsonUtil.getJSONNodeByPath(accountParameterJson,"appKey"));
					m.put("appSecret", JsonUtil.getJSONNodeByPath(accountParameterJson,"appSecret"));
					m.put("userSalt", JsonUtil.getJSONNodeByPath(accountParameterJson,"userSalt"));
					m.put("password", BWEncryptionUtil.md5AndSha(account.getPassword()+JsonUtil.getJSONNodeByPath(accountParameterJson,"userSalt")));
					Map<String,InvoiceInterfaceEntity> interfaceMap = new HashMap<>();
					for(InvoiceInterfaceEntity invoiceInterface : interfaceList) {
						interfaceMap.put(invoiceInterface.getInterfaceCode(), invoiceInterface);
					}
					m.put("interfaceMap", interfaceMap);
					if(!interfaceMap.containsKey("baiwang.oauth.token")) {
						retInfo = "请配置获取token的接口！";
						m.put("retInfo", retInfo);
						return m;
					}
				}else {
					retInfo = "请配置接口！";
					m.put("retInfo", retInfo);
					return m;
				}
			}else {
				retInfo = "请配置请求地址！";
				m.put("retInfo", retInfo);
				return m;
			}
		}
		StringBuilder url = new StringBuilder(m.get("invoiceUrl")+"?");
		Map<String,InvoiceInterfaceEntity> interfaceMap = (Map<String, InvoiceInterfaceEntity>) m.get("interfaceMap");
		InvoiceInterfaceEntity invoiceInterface = (InvoiceInterfaceEntity)interfaceMap.get("baiwang.oauth.token");
		m.put("token", "");
        url.append("method="+invoiceInterface.getInterfaceName()+"&")
        	.append("username="+m.get("accountName")+"&")
        	.append("password="+m.get("password")+"&client_id="+m.get("appKey")+"&client_secret="+m.get("appSecret")+"&")
        	.append("timestamp="+System.currentTimeMillis());
        try {
			String responseStr = HttpClientUtil.jsonPost(url.toString(),"UTF-8","",null);
			JSONObject responseJson = new JSONObject(responseStr);
			if(responseJson.has("success") && JsonUtil.getJSONNodeByPath(responseJson,"success")
					.toLowerCase().equals("true")){
				try {
					String datetime =  JsonUtil.getJSONNodeByPath(responseJson,"/response/expires_time");
			        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
			        LocalDateTime ldt = LocalDateTime.parse(datetime,dtf);
			        ldt=ldt.minusMinutes(10); //提前10分钟，用于提前刷新缓存
			        DateTimeFormatter fa = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			        String datetime2 = ldt.format(fa);
			        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
			            Date expiresTime = sdf.parse( datetime2);
			            m.put("expiresTime", expiresTime);
					String token = JsonUtil.getJSONNodeByPath(responseJson,"/response/access_token");
					m.put("token", token);
					m.put("retInfo", retInfo);
					cache.put(m.get("accountName"), m);
				} catch (ParseException e) {
					e.printStackTrace();
				}
			}else if(responseJson.has("success") && 
					JsonUtil.getJSONNodeByPath(responseJson,"success")
					.toLowerCase().equals("false")){
				String message = JsonUtil.getJSONNodeByPath(responseJson,"/errorResponse/message");
				retInfo=message;
			}else {
				retInfo="获取TOKEN异常！";
				m.put("retInfo", retInfo);
				return m;
			}
			m.put("retInfo", retInfo);
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return m;
	}

	@Override
	public IResponse bWRedInfoQuery(BWRedInfoQueryReq req) {
		// TODO Auto-generated method stub
		return null;
	}
	
	public IResponse getbWSmsVerify() {
		GenericResponse res = new GenericResponse();
		Map<String,String> parametersMap = new HashMap<>();
		res = smsBasics("baiwang.defp.sms.verify",parametersMap);
		return res;
	}
	
	public IResponse bWSmsCheck(BWSmsReq req) {
		GenericResponse res = new GenericResponse();
		if(StringUtils.isNotEmpty(req.getVerifyCode())) {
			Map<String,String> parametersMap = new HashMap<>();
			parametersMap.put("verifyCode", req.getVerifyCode());
			res = smsBasics("baiwang.defp.sms.check",parametersMap);
		}else {
			 res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			 res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	public GenericResponse smsBasics(String methodKey,Map<String,String> parametersMap) {
		GenericResponse res = new GenericResponse();
		String token = getToken();
		Cache cache = cacheManager.getCache("baiwang-tokenCache");
		Map<String,Object> map = null;
		if(null != cache) {
			map = cache.get(account.getAccountName(),Map.class);
			if(null!=map) {
				Map<String,InvoiceInterfaceEntity> interfaceMap = (Map<String, InvoiceInterfaceEntity>) map.get("interfaceMap");
				StringBuilder url = new StringBuilder(map.get("invoiceUrl")+"?");
				InvoiceInterfaceEntity invoiceInterface = (InvoiceInterfaceEntity)interfaceMap.get(methodKey);
				String method= invoiceInterface.getInterfaceName();

				parametersMap.put("sellerTaxCode", map.containsKey("sellerTaxCode") ? map.get("sellerTaxCode").toString():"");
				parametersMap.put("terminalCode",map.containsKey("terminalCode") ? map.get("terminalCode").toString():"");
				parametersMap.put("apiName",method);
				ObjectMapper mapper = new ObjectMapper();
				String content ="";
				 try {
					 content = mapper.writeValueAsString(parametersMap);
				 } catch (JsonProcessingException e) {
					 e.printStackTrace();
				 }

				String appKey = map.get("appKey").toString();
				String timestamp = System.currentTimeMillis()+"";
				String uuid = UUID.randomUUID().toString();  
				Map<String, String> textParams = new HashMap<String, String>();
				textParams.put("method", method);
				textParams.put("appKey", appKey);
				textParams.put("token", token);
				textParams.put("version", "6.0");
				textParams.put("type", "sync");
				textParams.put("format", "json");
				textParams.put("signType", "9");
				textParams.put("requestId", uuid);
				textParams.put("timestamp", timestamp);
				String sign = signTopRequest( textParams,  map.get("appSecret").toString(), content);
		        url.append("method="+method)
		        	.append("&appKey="+appKey)
		        	.append("&token="+map.get("token"))
		        	.append("&sign="+sign+"&version=6.0&type=sync&format=json&signType=9")
		        	.append("&requestId="+uuid)
		        	.append("&timestamp="+timestamp);

				 String responseStr=post(account,map,token,method,content);
				 if(StringUtils.isNotEmpty(responseStr)) {
					 JSONObject resultJson = new JSONObject(responseStr);
					 if(resultJson.has("success") && 
								JsonUtil.getJSONNodeByPath(resultJson,"success").equals("true")) {
						 String resultData = JsonUtil.getJSONNodeByPath(resultJson,"response");
						 String message = JsonUtil.getJSONNodeByPath(resultData,"message");
						 res.setRet(ResponseContext.RES_SUCCESS_CODE);
						 res.setRetInfo(message);
					 }else if(resultJson.has("errorResponse")) {
						 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
						 res.setRetInfo(JsonUtil.getJSONNodeByPath(resultJson,"errorResponse/message"));
					 }else {
						 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
						 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
					 }
			 }else {
				 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
				 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
			 }
			}else {
				 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
				 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
			 }
				
			}else {
				 res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
				 res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO+"（获取TOKEN或发票信息异常）");
			}
		return res;
	}
}

