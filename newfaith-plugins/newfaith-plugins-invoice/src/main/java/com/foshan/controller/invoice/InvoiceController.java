package com.foshan.controller.invoice;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.invoice.request.BWQueryReq;
import com.foshan.form.invoice.request.BWRedApplyReq;
import com.foshan.form.invoice.request.BWRedInfoQueryReq;
import com.foshan.form.invoice.request.BWSmsReq;
import com.foshan.form.invoice.request.InvoiceReq;
import com.foshan.form.invoice.request.RedInfoOperateReq;
import com.foshan.form.invoice.request.RedIssueReq;
import com.foshan.form.invoice.response.bWInvoice.BWInvoiceRes;
import com.foshan.form.invoice.response.bWInvoice.GetQuotaRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


@Api(tags = "开发票模块")
@RestController
public class InvoiceController extends BaseInvoiceController {

	// 开发票
	@ApiOperation(value = "开发票(invoice)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/invoice", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public BWInvoiceRes invoice(@RequestBody InvoiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		BWInvoiceRes res = (BWInvoiceRes) invoiceService.invoice(req);
		return res;
	}
	// 查询开正票、红票和申请红票结果
	@ApiOperation(value = "查询开正票、开红票和申请红票结果(queryInvoiceResult)", httpMethod = "POST", notes = "查询开正票、红票和申请红票结果,billCode、invoiceType不能为空")
	@ResponseBody
	@RequestMapping(value = "/queryInvoiceResult", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public BWInvoiceRes queryInvoiceResult(@RequestBody InvoiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		BWInvoiceRes res = (BWInvoiceRes) invoiceService.queryInvoiceResult(req,null);
		return res;
	}
	
	// 撤消申请红票
	@ApiOperation(value = "撤消申请红票(withdrawalRedRushApplication)", httpMethod = "POST", notes = "撤消申请红票,billCode不能为空")
	@ResponseBody
	@RequestMapping(value = "/withdrawalRedRushApplication", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public BWInvoiceRes withdrawalRedRushApplication(@RequestBody InvoiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		BWInvoiceRes res = (BWInvoiceRes) invoiceService.withdrawalRedRushApplication(req);
		return res;
	}
	
	// 查询撤消申请红票票结果
	@ApiOperation(value = "查询撤消申请红票票结果(queryWithdrawalRedRushApplicationResult)", httpMethod = "POST", notes = "查询撤消申请红票票结果,billCode不能为空")
	@ResponseBody
	@RequestMapping(value = "/queryWithdrawalRedRushApplicationResult", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public BWInvoiceRes queryWithdrawalRedRushApplicationResult(@RequestBody InvoiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		BWInvoiceRes res = (BWInvoiceRes) invoiceService.queryWithdrawalRedRushApplicationResult(req);
		return res;
	}
	
	// 查询企业授信额度
	@ApiOperation(value = "查询企业授信额度(queryQuota)", httpMethod = "POST", notes = "查询企业授信额度")
	@ResponseBody
	@RequestMapping(value = "/queryQuota", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetQuotaRes queryQuota(@RequestBody InvoiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetQuotaRes res = (GetQuotaRes) invoiceService.queryQuota();
		return res;
	}
	
	
	
	// 更新token
	@ApiOperation(value = "更新token(updateBWToken)", httpMethod = "POST", notes = "更新token")
	@ResponseBody
	@RequestMapping(value = "/updateBWToken", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse updateBWToken(@RequestBody InvoiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) invoiceService.updateToken();
		return res;
	}
	
	// 已开数电票查询
	@ApiOperation(value = "已开数电票查询(bWQuery)", httpMethod = "POST", notes = "已开数电票查询")
	@ResponseBody
	@RequestMapping(value = "/bWQuery", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public BWInvoiceRes bWQuery(@RequestBody BWQueryReq req, HttpServletRequest request)
			throws JsonProcessingException {
		BWInvoiceRes res = (BWInvoiceRes) invoiceService.bWQuery(req);
		return res;
	}
	
	// 申请开红票
	@ApiOperation(value = "申请开红票(bWRedApply)", httpMethod = "POST", notes = "申请开红票")
	@ResponseBody
	@RequestMapping(value = "/bWRedApply", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public BWInvoiceRes bWRedApply(@RequestBody BWRedApplyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		BWInvoiceRes res = (BWInvoiceRes) invoiceService.bWRedApply(req);
		return res;
	}
	
	// 获取验证码
	@ApiOperation(value = "获取验证码(getbWSmsVerify)", httpMethod = "POST", notes = "获取验证码")
	@ResponseBody
	@RequestMapping(value = "/getbWSmsVerify", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse getbWSmsVerify(@RequestBody BWSmsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) invoiceService.getbWSmsVerify();
		return res;
	}
	
	// 短信验证
	@ApiOperation(value = "短信验证(bWSmsCheck)", httpMethod = "POST", notes = "短信验证")
	@ResponseBody
	@RequestMapping(value = "/bWSmsCheck", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse bWSmsCheck(@RequestBody BWSmsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) invoiceService.bWSmsCheck(req);
		return res;
	}
	
	/*// 红字确认单查询
	@ApiOperation(value = "红字确认单查询(bWRedInfoQuery)", httpMethod = "POST", notes = "红字确认单查询")
	@ResponseBody
	@RequestMapping(value = "/bWRedInfoQuery", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public BWInvoiceRes bWRedInfoQuery(@RequestBody BWRedInfoQueryReq req, HttpServletRequest request)
			throws JsonProcessingException {
		BWInvoiceRes res = (BWInvoiceRes) invoiceService.bWRedInfoQuery(req);
		return res;
	}
	
	//红字确认单操作
	@ApiOperation(value = "红字确认单操作(redInfoOperate)", httpMethod = "POST", notes = "红字确认单操作")
	@ResponseBody
	@RequestMapping(value = "/redInfoOperate", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public BWInvoiceRes redInfoOperate(@RequestBody RedInfoOperateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		BWInvoiceRes res = (BWInvoiceRes) invoiceService.redInfoOperate(req);
		return res;
	}
	
	//开红字发票
	@ApiOperation(value = "开红字发票(redIssue)", httpMethod = "POST", notes = "开红字发票")
	@ResponseBody
	@RequestMapping(value = "/redIssue", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public BWInvoiceRes redIssue(@RequestBody RedIssueReq req, HttpServletRequest request)
			throws JsonProcessingException {
		BWInvoiceRes res = (BWInvoiceRes) invoiceService.redIssue(req);
		return res;
	}*/
}
