package com.foshan.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import javax.persistence.*;
import java.sql.Timestamp;



@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_message")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class MessageEntity extends EntityObject {

	/**
	 * 消息
	 */
	private static final long serialVersionUID = 8331596961888573220L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(36) comment '消息编码'")
	private String messageCode;
	@Column(columnDefinition = "int(11) comment '发送者ID'")
	private Integer senderId;
	@Column(columnDefinition = "int(11) comment '接收者ID'")
	private Integer receiverId;
	@Column(columnDefinition = "int(4) default 0 '接收者类型:0-member,1-user'")
	private Integer receiverType;
	@Column(columnDefinition = "varchar(512) comment '消息正文'")
	private String content;
	@Column(columnDefinition = "int(4) default 0 '消息类型:0-系统消息,1-装修管理类,2-事项申请类,3-催缴费类,4-服务消息'")
	private Integer type;
	@Column(columnDefinition = "varchar(255) comment '跳转链接'")
	private String url;
	@Column(columnDefinition = "int(4) default 0  '消息状态：0-未读,1-已读'")
	private Integer status;
	@Column(columnDefinition = "varchar(64) comment '阅读时间'")
	private Timestamp readTime;
}
