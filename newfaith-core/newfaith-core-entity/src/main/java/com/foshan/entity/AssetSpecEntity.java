package com.foshan.entity;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name="t_asset_spec")
@JsonIgnoreProperties(value={"hibernateLazyInitializer","handler"})
public class AssetSpecEntity implements IEntityBean{

	/**
	 * 媒资规格
	 */
	private static final long serialVersionUID = 5457407463564376674L;
	
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition="int(11) comment '业务ID'")
	private Integer serviceId;
	@Column(columnDefinition="int(4) comment '图片宽度'")
	private Integer imageWidth;
	@Column(columnDefinition="int(4) comment '图片高度'")
	private Integer imageHeight;
	@Column(columnDefinition="tinyint(2) default 1  comment '是否是按原图比例压缩'")
	private boolean keepAspectRatio;
	@Column(columnDefinition="int(4) comment '缩略小图图宽度'")
	private Integer smallImageWidth;
	@Column(columnDefinition="int(4) comment '缩略小图图高度'")
	private Integer smallImageHeight;
	@Column(columnDefinition="int(4) comment '缩略中图高度'")
	private Integer middleImageHeight;
	@Column(columnDefinition="int(4) comment '缩略中图宽度'")
	private Integer middleImageWidth;
	@Column(columnDefinition="tinyint(2) default 0  comment '是否是自动上架'")
	private boolean onShelves;
	@Column(columnDefinition = "varchar(200) comment '标签'")
	private String tag;
	@Column(columnDefinition="int(4) default 1 comment '转格式;0:原图格式;1:转成JPG;2:转成PNG;3:转成BMP;4:转成JPEG;5:转成GIF'")
	private Integer variableFormat;
	@Column(columnDefinition="int(10) default 0 comment '如图片大小大于设置值,按此值压缩,小于则不压，单位kb'")
	private Integer Filesize;
	@Column(columnDefinition = "decimal(2,2) comment '精度，递归压缩的比率;取值在0-1之间建;议小于0.9'")
	private BigDecimal accuracy;

	
	
}
