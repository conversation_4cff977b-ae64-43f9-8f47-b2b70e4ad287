package com.foshan.entity;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_url")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class UrlEntity extends EntityObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8923243171038611136L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(255) comment '文章标题'")
	private String title;
	@Column(columnDefinition = "varchar(1024) comment '审核意见'")
	private String idea;
	@Column(columnDefinition = "Timestamp  comment '审核时间'")
	private Timestamp auditTime;
	@Column(columnDefinition = "int(1) default 2 comment '审核状态 0--审核不通过 1--审核通过 2--初始化  3--已提交待审核'")
	private Integer auditState;
	@Column(columnDefinition = "varchar(1024) comment '目标ＵＲＬ链接'")
	private String targetUrl;
	@ManyToMany(targetEntity = RegionEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_url_region", joinColumns = @JoinColumn(name = "urlId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "regionId", referencedColumnName = "id"))
	@JsonIgnore
	private List<RegionEntity> regionList = new ArrayList<RegionEntity>();

}
