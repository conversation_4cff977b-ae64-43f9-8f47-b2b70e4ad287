package com.foshan.entity.context;



public class EntityContext {
	/*
	 * 数据库记录常量
	 */
	public final static Integer RECORD_STATE_INVALID=0; //记录否
	public final static Integer RECORD_STATE_VALID=1;  //记录是
	/*
	 * 投票、抽奖规则常量
	 */
	public final static Integer PLAN_TYPE_EVERYDAY=1;  //每天可投票
	public final static Integer PLAN_TYPE_WHOLE=2; //整个投票阶段
	
	/*
	 * 栏目内容类型常量
	 */
	public final static Integer COLUMN_TYPE_IMAGE=0;  //图文栏目
	public final static Integer COLUMN_TYPE_VIDEO=1;  //音视频栏目
	public final static Integer COLUMN_TYPE_VOTE=2;   //投票栏目
	public final static Integer COLUMN_TYPE_QUESTION=3;  //问卷栏目
	public final static Integer COLUMN_TYPE_TOP=4;	//排行榜
	public final static Integer COLUMN_TYPE_LUCKY=5; //抽奖
	public final static Integer COLUMN_TYPE_LUCKY_RESULT=6; //抽奖结果
	public final static Integer COLUMN_TYPE_SHOP=7;//商城栏目
	/*
	 * 栏目映射系统
	 */
	public final static Integer COLUMN_MAPPING_BO=1; //高清互动平台
	public final static Integer COLUMN_MAPPING_IP=2; //IP视频平台
	/*
	 * 推荐标识
	 */
	public final static Integer COMMENT_FLAG_INVALID=0;//推荐标识：不推荐
	public final static Integer COMMENT_FLAG_VALID=1;//推荐标识:推荐
	/*
	 * 目标终端
	 */
	public final static Integer COLUMN_TARGET_TYPE_TV=0;//电视端
	public final static Integer COLUMN_TARGET_TYPE_MOBILE=1;//移动端
	public final static Integer COLUMN_TARGET_TYPE_COMMON=2;//通用
	/*
	 * 媒资类型常量
	 */
	public final static Integer ASSET_TYPE_IMAGE=0;  //图片媒资
	public final static Integer ASSET_TYPE_SOUND=1; //声音
	public final static Integer ASSET_TYPE_VIDEO=2;  //视频
	public final static Integer ASSET_TYPE_DOC = 3;	//办公文件
	/*
	 * 媒资状态常量
	 */
	public final static Integer ASSET_STATE_PENDING=0;  //待审核
	public final static Integer ASSET_STATE_DOWN=1;   // 下架
	public final static Integer ASSET_STATE_UP=2;  //上架
	/*
	 * 媒资包常量
	 */
	public final static Integer ASSET_PACKAGE_FLAG_INVALID=0; //单独媒资
	public final static Integer ASSET_PACKAGE_FLAG_VALID=1;  //媒资包
	/*
	 * 统计查询是否统计访问量开关标识
	 */
	public final static Integer COUNT_VISIT_OFF=0;//不统计访问量
	public final static Integer COUNT_VISIT_ON=1;//统计访问量
	/*
	 * 默认获取排行队列大小
	 */
	public final static Integer DEFAULT_TOP=10;//默认top队列大小为10
	/*
	 * 栏目显示目标终端类型
	 */
	public final static Integer FOLDER_TARGET_TYPE_TV=0;//栏目在电视端显示
	public final static Integer	FOLDER_TARGET_TYPE_MOBILE=1;//栏目在移动端显示
	public final static Integer FOLDER_TARGET_TYPE_COMMON=2;//通用栏目
	/*
	 * 栏目推荐标识
	 */
	public final static Integer FOLDER_COMMEND_OFF=0;//推荐标识关闭
	public final static Integer FOLDER_COMMEND_ON=1;//推荐标识打开
	
	 /**
	  * 菜单路由信息
	  */
    public final static String ROUTER_LAYOUT = "Layout"; //Layout组件标识
    public final static String ROUTER_PARENT_VIEW = "ParentView"; //ParentView组件标识
    public static final Integer MENU_TYPE_DIR = 2; //菜单类型（目录）
    public static final Integer MENU_TYPE_MENU = 0; //菜单类型（菜单）
    public static final Integer MENU_TYPE_BUTTON = 1; // 菜单类型（权限按钮）
    public static final Integer MENU_YES_FRAME = 1; // 是否菜单外链（是）
    public static final Integer MENU_NO_FRAME = 0; // 是否菜单外链（否）

	/*
	 *赠券模版有效期类型
	 */
	public final static Integer GIFTROLL_MODEL_PERIOD_TYPE_DEDUCTION=0;//抵扣劵
	public final static Integer GIFTROLL_MODEL_PERIOD_TYPE_EXCHANGE=1;//兑换券
	public final static Integer GIFTROLL_MODEL_PERIOD_TYPE_POINTS=2;//积分券
	public final static Integer GIFTROLL_MODEL_PERIOD_TYPE_CASH=3;//现金券
	public final static Integer GIFTROLL_MODEL_PERIOD_TYPE_PRODUCT=4;//产品券
	public final static Integer GIFTROLL_MODEL_PERIOD_TYPE_POST=5;//运费券
	public final static Integer GIFTROLL_MODEL_PERIOD_TYPE_SHOP_COUPON=6;//商城优惠券
	/*
	 * 抽奖奖品类型
	 */
	public final static Integer LUCKY_PRIZE_TYPE_PHYSICAL=0;//实物奖品
	public final static Integer LUCKY_PRIZE_TYPE_PROGRAM=1;//节目包
	public final static Integer LUCKY_PRIZE_TYPE_BROAD=2;//宽带包
	public final static Integer LUCKY_PRIZE_TYPE_TICKET=3;//电子票券
	/*
	 *赠券模版有效期类型
	 */
	public final static Integer GIFTROLL_MODEL_TYPE_PERIOD=0;//赠券固定有效期
	public final static Integer GIFTROLL_MODEL_TYPE_REMAIN=1;//赠券剩余有效期
	
	/*
	 * 问卷题目类型
	 */
	public final static Integer QUESTION_PLAN_TYPE_ANSWER=1;//答题问卷
	public final static Integer QUESTION_PLAN_TYPE_NOANSWER=2;//调查问卷
	public final static Integer QUESTION_PLAN_TYPE_DYNAMIC=3;//动态问卷
	
	/*
	 * 终端设备类型
	 */
	public final static Integer DEVICE_TYPE_STB=0; //机顶盒
	public final static Integer DEVICE_TYPE_GW=1;  //网关
	public final static Integer DEVICE_TYPE_WEB=2; //浏览器
	public final static Integer DEVICE_TYPE_PHONE=3; //手机
	
	/*
	 * 积分兑换奖品类型
	 */
	public final static Integer POINTS_PRIZE_TYPE_REAL=0;//实物奖品
	public final static Integer POINTS_PRIZE_TYPE_VIRTUAL=1;//虚拟奖品
	
	/*
	 * 虚拟奖品编号生成规则
	 */
	public final static Integer POINTS_PRIZE_CODE_GENERIC_RANDOM=0;//随机生成
	public final static Integer POINTS_PRIZE_CODE_GENERIC_CUSTOM=1;//定制生成

	/*
	 * 抽奖绑定类型
	 */
	public final static Integer LUCKY_BIND_TYPE_NONE=0;//未绑定
	public final static Integer LUCKY_BIND_TYPE_VOTE=1;//投票
	public final static Integer LUCKY_BIND_TYPE_QUESTION=2;//问卷
	
	/*
	 * 投票规则终端类型
	 */
	public final static Integer VOTE_CLIENT_TYEP_ALL=0;//所有终端
	public final static Integer VOTE_CLIENT_TYPE_STB=1;//机顶盒
	public final static Integer VOTE_CLIENT_TYPE_PHONE=2;//手机
	public final static Integer VOTE_CLIENT_TYPE_OTHER=3;//其它
	
	
	/*
	 * 订单类型
	 */
	public final static Integer PRODUCT_ORDER_TYPE_NORMAL=0;//普通订单
	public final static Integer PRODUCT_ORDER_TYPE_PICKUP=1;//自提订单
	public final static Integer PRODUCT_ORDER_TYPE_BOSS = 2;//BOSS订单
	public final static Integer PRODUCT_ORDER_TYPE_VIDEO=3;//视频类订单
	public final static Integer PRODUCT_ORDER_TYPE_COMMUNITY=4;//小区订单
	public final static Integer PRODUCT_ORDER_TYPE_URGENT_COMMUNITY=5;//小区加急订单

	/*
	 * 订单类型
	 */
	public final static Integer PRODUCT_ORDER_IS_PRIVATE_ORDER=0;//个人订单
	public final static Integer PRODUCT_ORDER_IS_COMPANY_ORDER=1;//企业购订单
	public final static Integer PRODUCT_ORDER_IS_POINTS_ORDER = 2;//积分订单
	/*
	 * 订单状态
	 */
	public final static Integer PRODUCT_ORDER_STATE_SUBMIT_NOPAY=0;//已提交未付款
	public final static Integer PRODUCT_ORDER_STATE_PAY=1;//已付款
	public final static Integer PRODUCT_ORDER_STATE_SENDGOODS=2;//已发货
	public final static Integer PRODUCT_ORDER_STATE_CLOSE_NO_APPRAISE=3;//关闭未评价
	public final static Integer PRODUCT_ORDER_STATE_CLOSE_APPRAISE=4;//已评价
	public final static Integer PRODUCT_ORDER_STATE_SPLIT=7;//已拆分
	public final static Integer PRODUCT_ORDER_STATE_CANCEL=8;//已取消
	
	/*
	 * 订单流程状态
	 */
	public final static Integer PRODUCT_ORDER_FLOW_SATTE_NORMAL=0;//正常
	public final static Integer PRODUCT_ORDER_FLOW_STATE_APPLY_CANCEL=1;//申请取消订单
	public final static Integer PRODUCT_ORDER_FLOW_STATE_VERIFY_CANCEL=2;//取消申请审核通过
	public final static Integer PRODUCT_ORDER_FLOW_STEEE__APPLY_REFUND=3;//申请退款
	public final static Integer PRODUCT_ORDER_FLOW_STATE_VERIFY_REFUND=4;//退款申请审批通过

	/*
	 * 订单项退款状态
	 */
	public final static Integer PRODUCT_ORDER_ITEM_REFUND_STATE_APPLY=1;//退款中
	public final static Integer PRODUCT_ORDER_ITEM_REFUND_STATE_REFUND_PART=3;//完成部分退款。
	public final static Integer PRODUCT_ORDER_ITEM_REFUND_STATE_REFUND_SUCCESS=2;//退款成功,退款到账

	/**
	 * 退款记录状态
	 */
	public final static Integer REFUND_APPLY_STATE_INITAL=0;//初始化，未向支付系统发起退款申请
	public final static Integer REFUND_APPLY_STATE_COMMIT=1;//已经发起退款请求，第三方支付正在处理退款
	public final static Integer REFUND_APPLY_STATE_FINISH=2;//完成退款，退款到账。
	public final static Integer REFUND_APPLY_STATE_ERROR=3;//支付系统退款出现异常
	public final static Integer REFUND_APPLY_STATE_CLOSED=4;//支付系统付关闭了该订单退款
	
	/*
	 * 订单拆分状态
	 */
	public final static Integer PRODUCT_ORDER_SPLIT_STATE_INVALID=0;//未拆分
	public final static Integer PRODUCT_ORDER_SPLIT_STATE_VALID=1;//已拆分
	
	
	/*
	 * 收货地址址默认
	 */
	public final static Integer MEMBER_ADDRESS_NOTDEFAULT=0;//不是默认
	public final static Integer MEMBER_ADDRESS_DEFAULT=1;//设置默认
	
	/*
	 * 购物车项选定
	 * 
	 */
	public final static Integer CART_ITEM_NOCHOICE=0;//未选中
	public final static Integer CART_ITEM_SELECTION=1;//选中
	

	/*
	 * 扣库存时机
	 * 
	 */
	public final static Integer STOCK_ALLOCATION_TIME_ORDER=0;//下订单
	public final static Integer STOCK_ALLOCATION_TIME_PAYMENT=1;//支付完成
	public final static Integer STOCK_ALLOCATION_TIME_SHIP=2;//发货
	
	/*
	 * 小数位精确方式
	 */
	public final static Integer ROUNDTYPE_ROUNDHALFUP=0;//四舍五入
	public final static Integer ROUNDTYPE_ROUNDUP=1;//向上取整
	public final static Integer ROUNDTYPE_ROUNDDOWN=2;//向下取整
	
	/*
	 * SN序列号类型
	 */
	public final static Integer SNTYPE_PRODUCT=0;//商品
	public final static Integer SNTYPE_ORDER=1;//订单
	public final static Integer SNTYPE_ORDERPAYMENT=2;//订单支付
	public final static Integer SNTYPE_ORDERREFUNDS=3;//订单退款
	public final static Integer SNTYPE_ORDERSHIPPING=4;//订单发货
	public final static Integer SNTYPE_ORDERRETURNS=5;// 订单退货
	public final static Integer SNTYPE_PAYMENTSESSION=6;//支付事务
	
	/*
	 * 支付类型
	 */
	public final static Integer PAYMENTSESSIONTYPE_ORDERPAYMENT=0;//订单支付
	public final static Integer PAYMENTSESSIONTYPE_DEPOSITRECHARGE=1;//预存款充值

	/*
	 * 审核状态
	 */
	public final static Integer RECORD_AUDIT_NO_PASS=0;//审核不通过
	public final static Integer RECORD_AUDIT_PASS=1;//审核通过
	public final static Integer RECORD_AUDIT_INITIAL=2;//初始化
	public final static Integer RECORD_AUDIT_WAITING=3;//已提交待审核
	
	/*
	 * 自动审核状态
	 */
	public final static Integer AUTO_AUDIT_ARTIFICAL=0;//人工审核
	public final static Integer AUTO_AUDIT_AUTO=1;//自动审核
	
	/*
	 * 自动上架开关
	 */
	public final static Integer AUTO_UPSHELF_CLOSE=0;//关闭自动上架功能
	public final static Integer AUTO_UPSHELF_OPEN=1;//打开自动上架功能
	
	/*
	 * 商铺、商品自营状态
	 */
	public final static Integer SELF_SUPPORT_INVALID=0;//不是自营
	public final static Integer SELF_SUPPORT_VALID=1;//自营
	
	/*
	 * 产品导入标识
	 */
	public final static Integer PRODUCT_IMPORT_FLAG_NONE=0;//该产品还未导入
	public final static Integer PRODUCT_IMPORT_FLAG_IN=1;//该产品已经导入
	
	/*
	 * 产品上架状态
	 */
	public final static Integer UP_SHELF_STATE_DOWN=0;//下架
	public final static Integer UP_SHELF_STATE_UP_FOR_INDIVIDUAL=1;//上架（只对普通消费者）
	public final static Integer UP_SHELF_STATE_UP_FOR_COMPANY=2;//上架（只对企业购）
	public final static Integer UP_SHELF_STATE_UP_FOR_INDIVIDUAL_AND_COMPANY = 3;//上架（企业购和普通消费者）
    public final static Integer UP_SHELF_STATE_UP_FOR_POINTS = 4;//上架（只对积分商城）
    public final static Integer UP_SHELF_STATE_UP_FOR_POINTS_AND_INDIVIDUAL = 5;//上架（只对积分商城和普通消费者）
    public final static Integer UP_SHELF_STATE_UP_FOR_POINTS_AND_COMPANY = 6;//上架（只对积分商城和企业购）
    public final static Integer UP_SHELF_STATE_UP_FOR_ALL = 7;//上架（对积分商城和普通消费者和企业购）

	/*
	 * 产品包类型
	 */
	public final static Integer PRODUCT_GROUP_TYPE_UPSHELF=0;//上架产品
	public final static Integer PRODUCT_GROUP_TYPE_PROMOTION=1;//促销产品
	public final static Integer PRODUCT_GROUP_TYPE_ACTIVITY=2;//活动产品
	public final static Integer PRODUCT_GROUP_TYPE_COMPANY=3;//企业购产品
	
	/*
	 * 评价参数
	 */
	public final static Integer PRODUCT_APPRAISE_DOWNTIMES=1;//差评
	public final static Integer PRODUCT_APPRAISE_MEDIUMTIMES=2;//中评
	public final static Integer PRODUCT_APPRAISE_UPTIMES=3;//好评
	
	/*
	 * 订单轨迹状态
	 */
	public final static Integer PRODUCT_ORDER_TRACE_CREATE=0;//创建
	public final static Integer PRODUCT_ORDER_TRACE_PAY=1;//买家已付款
	public final static Integer PRODUCT_ORDER_TRACE_SEND=2;//商家已发货
	public final static Integer PRODUCT_ORDER_TRACE_CHECK=3;//买家已收货
	public final static Integer PRODUCT_ORDER_TRACE_REFUND=4;//商家已退款
	public final static Integer PRODUCT_ORDER_TRACE_SPLIT=5;//创建并拆分
	public final static Integer PRODUCT_ORDER_TRACE_CANCEL=6;//已取消
	public final static Integer PRODUCT_ORDER_TRACE_SELL_CHANGE_ORDER_AMOUNT=7;//卖家修改订单金额
	public final static Integer PRODUCT_ORDER_TRACE_CHANGE_ADDRESS=8;//修改地址
	
	/*
	 * 催单状态
	 */
	public final static Integer PRODUCT_ORDER_IS_NOT_REMINDER=0;//未催单
	public final static Integer PRODUCT_ORDER_IS_REMINDER=1;//已催单
	/*
	 * 退货状态
	 */
	public final static Integer SALES_RETURN_APPLY=0;//申请退货
	public final static Integer SALES_RETURN_PASS=1;//退货审核通过(提交退化物流信息)
	public final static Integer SALES_RETURN_COMPLETE=2;//退款完成
	public final static Integer SALES_RETURN_NO_PASS=3;//退货审核不通过
	public final static Integer SALES_RETURN_RECEIVE_GOODS=4;//已发货，待确定收货和退款
	public final static Integer SALES_RETURN_COMPLETE_NO_REFUND=5;//商家确认收货
    public final static Integer SALES_RETURN_FIRST_CHECK = 6;//自营商品退款需要一级审核
    public final static Integer SALES_RETURN_SECOND_CHECK = 7;//自营商品退款需要二级审核
    public final static Integer SALES_RETURN_THIRD_CHECK = 8;//自营商品退款需要三级审核
	
	/*
	 * 售后类型
	 */
	public final static Integer AFTER_SALE_TYPE_IS_GOODS_REJECTED=0;//退货退款
	public final static Integer AFTER_SALE_TYPE_IS_REFUNDMENT=1;//发货前退款
	public final static Integer AFTER_SALE_TYPE_IS_DELIVER_GOODS_REFUNDMENT=2;//发货后的退款

	/*
	 * 积分来源  积分类型 0--签到 1--浏览页面 2--浏览详情 3--订单记录 4--订单金额 5--点播 6--答题 7--其它'"
	 */
	public final static Integer POINTS_TYPE_CHECKIN=0;//签到
	public final static Integer POINTS_TYPE_BROWSE=1;//浏览页面
	public final static Integer POINTS_TYPE_BROWSE_DETAIL=2;//浏览详情
	public final static Integer POINTS_TYPE_ORDER=3;//订单记录
	public final static Integer POINTS_TYPE_ORDER_AMOUNT=4;//订单金额
	public final static Integer POINTS_TYPE_VIDEO=5;//点播
	public final static Integer POINTS_TYPE_QUESTION=6;//答题
	public final static Integer POINTS_TYPE_OTHER=7;//其它
	
	/*
	 * 总积分类型
	 */
	public final static Integer TOTAL_POINTS_TYPE_CARD=0;//卡片积分
	public final static Integer TOTAL_POINTS_TYPE_NORMAL=1;//活动积分
	
	/*
	 * 特殊积分策略标识
	 */
	public final static Integer POINTS_PLAN_SPECIAL_FLAG_NORMAL=0;//普通积分
	public final static Integer POINTS_PLAN_SPECIAL_FLAG_SPECIAL=1;//特殊积分
	
	/*
	 * 积分策略规则
	 */
	public final static Integer POINTS_PLAN_RULE_UNLIMIT=0;//积分累积无限制
	public final static Integer POINTS_PLAN_RULE_ONCE=1;//积分累积每天一次
	
	/*
	 * 积分等级类别
	 */
	public final static Integer POINTS_LEVEL_TYPE_NORMAL=0;//通用
	public final static Integer POINTS_LEVEL_TYPE_SERVICE=1;//业务专属
	
	/*
	 * 分单状态
	 */
	public final static Integer IS_NOT_SINGLE=0;//不是分单
	public final static Integer SUCCESSFUL_SINGLE=1;//是分单,已分派给客服
	public final static Integer UNSUCCESSFUL_SINGLE=2;//是分单，未成功分派给客服
	
	/*
	 * 商品是否是活动商品
	 */
	public final static Integer IS_NOT_PROMOTION=0;//不是活动商品
	public final static Integer IS_PROMOTION=1;//是活动商品，不能下单
	
	/*
	 * 订单流类型
	 */
	public final static Integer ORDER_FLOW_TYPE_NORMAL=0;//正常流程
	public final static Integer ORDER_FLOW_TYPE_SERVICE=1;//需要客服介入
	
	/*
	 * 订单支付类型
	 */
	public final static Integer ORDER_PAY_CONFIRM=0;//必须客户确认后才能够支付
	public final static Integer ORDER_PAY_NOMAL=1;//正常支付
	
	/*
	 * 商品配送方式
	 */
	public final static Integer PRODUCT_DELIVERY_TYPE_NONE=0;//不需要配送
	public final static Integer PRODUCT_DELIVERY_TYPE_NORMAL=1;//普通配送
	public final static Integer PRODUCT_DELIVERY_TYPE_COLD=2;//冷链配送
	
	/*
	 * 商品是否包邮
	 */
	public final static Integer PRODUCT_IS_NEED_COURIER_NONE=0;//包邮
	public final static Integer PRODUCT_IS_NEED_COURIER_NEED=1;//不包邮
	/*
	 * 商品自提方式
	 */
	public final static Integer PRODUCT_PICKUP_NOT_NEED=0;//不需要自提
	public final static Integer PRODUCT_PICKUP_NEED=1;//需要自提
	public final static Integer COMMUNITY_DISTRIBUTION=3;//小区配送 
	
	
	/*
	 *商户插件配置状态 0未提交审核、1、审核中、2审核通过、3审核不通过 
	 */
	public final static Integer STORE_PLUGIN_CONFIG_STATE_NOSUBMIT=0;//未提交审核
	public final static Integer STORE_PLUGIN_CONFIG_STATE_SUBMITED=1;//审核中
	public final static Integer STORE_PLUGIN_CONFIG_STATE_PASS=2;//审核通过
	public final static Integer STORE_PLUGIN_CONFIG_STATE_NOPASS=3;//审核不通过 
	
	/*
	 * 商品价格属性
	 */
	public final static Integer PRODUCT_PRICE_TYPE_NORMAL=0;//默认价格
	public final static Integer PRODUCT_PRICE_TYPE_PROMOTION=1;//活动价格
	public final static Integer PRODUCT_PRICE_TYPE_COMPANY=2;//企业购价
	public final static Integer PRODUCT_PRICE_TYPE_POINTS = 3;//积分价格
	
	/*
	 * sku列表属性
	 */
	public final static Integer SKU_LIST_TYPE_NONE=0;
	public final static Integer SKU_LIST_TYPE_IMPORT=1;
	public final static Integer SKU_LIST_TYPE_ALL=2;
	
	/*
	 * 图片格式
	 */
	public final static Integer PIC_ORIGINAL_FORMAT=0;//原图格式输出
	public final static Integer PIC_JPG_FORMAT=1;//转成JGP
	public final static Integer PIC_PNG_FORMAT=2;//转成PNG
	public final static Integer PIC_BMP_FORMAT=3;//转成bmp
	public final static Integer PIC_JPEG_FORMAT=4;//转成jpeg
	public final static Integer PIC_GIF_FORMAT=5;//转成gif
	
	public final static Integer IS_NOT_COVER=0;
	public final static Integer IS_COVER=1;
	
	/*
	 * billType 发票格式
	 */
	public final static Integer BILLTYPE_IS_INDIVIDUAL=0; //个人
	public final static Integer BILLTYPE_IS_COMPANY=1;//企业
	
	
	/*
	 * 订单是否需要开发票
	 */
	public final static Integer INVOICE_IS_NOT_REQUIRED=0; //不需要开发票
	public final static Integer INVOICE_IS_REQUIRED_NOTSUBMIT=1;//需要,未开；
	public final static Integer INVOICE_IS_REQUIRED_RED=2;//需要，已冲红；
	public final static Integer INVOICE_IS_REQUIRED_FINISH=3;//需要，已完成；

	
	/*
	 *  发票类型
	 */
	public final static Integer INVOICETYPE_IS_BLUE=1; //正票
	public final static Integer INVOICETYPE_IS_RED=2; //红票
	
	/*
	 *  发票操作代码
	 */
	public final static Integer NORMAL_TICKET_IS_NORMALLY_ISSUED=10;//正票正常开具
	public final static Integer POSITIVE_TICKET_IS_REOPENED_BY_WRONG_TICKET=11;//正票错票重开
	public final static Integer RETURN_DISCOUNT_AND_RED_TICKET=20;//退货折让红票
	public final static Integer WRONG_TICKET_REOPEN_TICKET=21;//错票重开红票
	public final static Integer CHANGE_TICKETS_FOR_RED=22;//换票冲红
	
	/*
	 *  促销活动状态
	 */
	/**
	 * 未生效
	 */
	public final static Integer PROMOTION_STATUS_INITIAL=0;
	/**
	 * 已生效
	 */
	public final static Integer PROMOTION_STATUS_NORMAL=1;
	/**
	 * 关闭
	 */
	public final static Integer PROMOTION_STATUS_COLSE=2;
	
	/*
	 *  优惠券设置状态
	 */
	/**
	 * 未生效
	 */
	public final static Integer COUPON_SETTING_STATUS_INITIAL=0;
	/**
	 * 已生效
	 */
	public final static Integer COUPON_SETTING_STATUS_NORMAL=1;
	/**
	 * 关闭
	 */
	public final static Integer COUPON_SETTING_STATUS_COLSE=2;
	
	/**
	 * 收藏
	 */
	public final static Integer IS_NOT_FAVORITE=0;
	public final static Integer IS_FAVORITE=1;//是收藏
	
	/**
	 * 商品类型
	 */
	public final static Integer IS_COMMON_PRODUCT=0;//一般商品
	public final static Integer IS_VIDEO_PRODUCT=1;//点播类商品
	
	/*
	 * 减库存设置
	 */
	public final static Integer SUBTRACT_TYPE_IS_IN_ORDER=0;//下订单时锁库存
	public final static Integer SUBTRACT_TYPE_IS_IN_PAYMENT=1;//支付时锁库存
	
	/*
	 * 库存记录或修改库存请求数量类型
	 */
	public final static Integer INVENTORY_DATA_STATUE_IS_NEW_ADD=0;//商家创建库存
	public final static Integer INVENTORY_DATA_STATUE_IS_ADD=1;//商家增加库存量
	public final static Integer INVENTORY_DATA_STATUE_IS_REDUCE=2;//商家减库存量
	public final static Integer INVENTORY_DATA_STATUE_IS_PLACE_AN_ORDER=3;//下单锁库存
	public final static Integer INVENTORY_DATA_STATUE_IS_PAYMENT=4;//支付锁库存
	public final static Integer INVENTORY_DATA_SHIPMENT=5;//发货减库存(减锁库存) 
	public final static Integer INVENTORY_DATA_RELEASE=6;//取消订单释放库
	
	/*
	 * 接口请求企业购标识
	 */
	public final static Integer IS_NOT_COMPANY_BUY=0; //普通消费
	public final static Integer IS_COMPANY_BUY=1;     //企业购
	
	/*
	 * 产品限购标识
	 */
	public final static Integer PRODUCT_LIMIT_FLAG_INVALID=0;//没有限购活动
	public final static Integer PRODUCT_LIMIT_FLAG_VALID=1;//有限购活动
	/*
	 * 产品限购策略
	 */
	public final static Integer PRODUCT_LIMIT_TYPE_INVENTORY_ONLY=0;//只限制库存
    public final static Integer PRODUCT_LIMIT_TYPE_LIMIT_ONLY=1;//只限制购买数量
    public final static Integer PRODUCT_LIMIT_TYPE_TIME_INVENTORY=2;//限定时间内限定库存
    public final static Integer PRODUCT_LIMIT_TYPE_TIME_LIMIT=3;//限定时间内限定购买数量
    public final static Integer PRODUCT_LIMIT_TYPE_TIME_LIMIT_INVENTORY=4;//限定时间内限定购买数量及限定库存
    
    /*
     * 产品限购范围
     */
    public final static Integer PRODUCT_LIMIT_SCOPE_SIGNL=0;//只限单个产品
    public final static Integer PRODUCT_LIMIT_SCOPE_PART=1;//同规格下部分产品
    public final static Integer PRODUCT_LIMIT_SCOPE_ALL=2;//同规格下所有产品
	
	
	/*
	 * 商品是否开始销售标识
	 */
//	public final static Integer BEGIN_SALE_NO=0; //未开售
//	public final static Integer BEGIN_SALE_OFF_FOR_INDIVIDUAL=1;//是（只对普通消费者）
//	public final static Integer BEGIN_SALE_OFF_FOR_COMPANY=2;//是（只对企业购）
//	public final static Integer BEGIN_SALE_OFF_FOR_ALL=3;//全部
	
	/*
	 * 商铺经营模式
	 */
	public final static Integer BUSINESS_MODEL_IS_COMMON=0; //只面向普通消费者
	public final static Integer BUSINESS_MODEL_IS_COMPANY=1;//只面向企业购 
	public final static Integer BUSINESS_MODEL_ALL=2;//包括以上两种方式 

	/*
	 * 账号类型
	 *
	 */
	public final static Integer ACCOUNT_TYPE_PRIVATE_ACCOUNT=0; //个人账号
	public final static Integer ACCOUNT_TYPE_PUBLIC_ACCOUNT=1;  //企业账号
	
	
	/**
	 * 运费模板设置
	 */
	/**只启用单品运费模板**/
	public final static Integer COURIER_ENABLE_SINGLE_PRODUCT_ONLY=0; 
	/**只启用店铺运费模板**/
	public final static Integer COURIER_ENABLE_STORE_ONLY=1; 
	/**单品运费模板、店铺运费模板两者均启用**/
	public final static Integer COURIER_ENABLE_SOTRE_PRODUCT_BOTH=2;  //
	
	/**
	 * 物流运费模板应用级别
	 * 
	 */
	
	/**单品运费模板**/
	public final static Integer COURIER_SCHEDULE_LEVEL_SINGLE_PRODUCT = 0;
	/**商铺运费模板**/
	public final static Integer COURIER_SCHEDULE_LEVEL_STORE = 1;
	
	/**
	 * 运费模板类型
	 */
	
	/**按重量计算运费**/
	public final static Integer COURIER_SCHEDULE_CACULATE_BY_WEIGHT = 0;
	/**按体积计算运费**/
	public final static Integer COURIER_SCHEDULE_CACULATE_BY_VOLUME = 1;
	/**按体积算运费**/
	public final static Integer COURIER_SCHEDULE_CACULATE_BY_NUMBER = 2;

	/**
	 * 免邮条件限制值类型
	 */
	
	/**按订单的价格计算免邮**/
	public final static Integer COURIER_SCHEDULE_FORFREE_LIMIT_PRICE = 0;
	/**按购订单的商品数量计算免邮**/
	public final static Integer COURIER_SCHEDULE_FORFREE_LIMIT_NUMBER = 1;
	

	/**
	 * 免邮条件限制值类型
	 */
	
	/**满LimitValue（元/件）包邮，不满则LogisticsCost元运费**/
	public final static Integer COURIER_SCHEDULE_FORFREE_STRATEGY_LIMIT = 0;
	/**每张订单固定LogisticsCost运费**/
	public final static Integer COURIER_SCHEDULE_FORFREE_STRATEGY_FIX = 1;
	
	/**
	 * 页面商品列表接口报文标记全部字段
	 */

	public final static Integer PRODUCT_LIST_DETAIL_FLAG_ALL =0;
	/**
	 * 管理后台返回商品列表
	 */
	public final static Integer PRODUCT_LIST_DETAIL_FLAG_ADMIN =1;
	/**
	 * 移动端返回商品列表
	 */
	public final static Integer PRODUCT_LIST_DETAIL_FLAG_MOBILE =2;
	/**
	 * 大屏端返回商品列表
	 */
	public final static Integer PRODUCT_LIST_DETAIL_FLAG_TV =3;
	/**
	 * 客服返回商品列表
	 */
	public final static Integer PRODUCT_LIST_DETAIL_FLAG_CUSTOMER_SERVICE =4;
	
	/**
	 * 财务属性为空
	 */
	public final static Integer FINANCIAL_ATTRIBUTES_IS_NULL=1;
	public final static Integer FINANCIAL_ATTRIBUTES_IS_NOT_NULL=0;
	
    /*
     * 积分加减类型
     */
    public final static Integer POINTS_TYPE_ADD = 0;//增加积分
    public final static Integer POINTS_TYPE_REDUCE = 1;//减少积分
    
    /*
     * 积分消耗类型
     */
    public final static Integer POINTS_EXPEND_TYPE_ORDER = 0;//下单兑换
    public final static Integer POINTS_EXPEND_TYPE_LOTTERY = 1;//抽奖活动
	
	/*
	 * 社区审核状态
	 */
	public final static Integer EVENT_STATE__AUDIT_INITIAL=0;//初始化
	public final static Integer EVENT_STATE__AUDIT_WAITING=1;//已提交待审核
	public final static Integer EVENT_STATE__AUDIT_PASS=2;//审核通过
	public final static Integer EVENT_STATE_AUDIT_NO_PASS=3;//审核不通过
	public final static Integer EVENT_STATE_SEND_ORDERS=4;//已派单
	public final static Integer EVENT_STATE_COMPLETE=5;//已完成
	public final static Integer EVENT_STATE_REVOCATION=6;//已撤销F
}
