package com.foshan.entity;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Calendar;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.context.EntityContext;
import com.foshan.util.DateUtil;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 奖券
 */
@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorValue("GC")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class GiftRollEntity extends TicketEntity {


	private static final long serialVersionUID = 3832089032131188665L;

	@Column(columnDefinition = "DECIMAL(5,2) comment '奖卷金额 单位：元'")
	private BigDecimal giftRollAmount;

	@ManyToOne(targetEntity = GiftRollModelEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "modelId", referencedColumnName = "id", nullable = true)
	private GiftRollModelEntity giftRollModel;


	public GiftRollEntity(Integer serviceId, String smartcardId, String phone, String userCode, String giftRollCode,
			String giftRollName, BigDecimal giftRollAmount, Timestamp startTime, Timestamp endTime,
			Integer giftRollState, Timestamp enableGiftRollTime,Timestamp receivedTime) {
		super(serviceId,smartcardId,  phone,userCode, giftRollCode,giftRollName, startTime, endTime,giftRollState,  enableGiftRollTime, receivedTime);
		this.giftRollAmount = giftRollAmount;
	}


	public BigDecimal getGiftRollAmount() {
		return giftRollAmount;
	}

	public void setGiftRollAmount(BigDecimal giftRollAmount) {
		this.giftRollAmount = giftRollAmount;
	}


	public GiftRollModelEntity getGiftRollModel() {
		return giftRollModel;
	}

	public void setGiftRollModel(GiftRollModelEntity giftRollModel) {
		this.giftRollModel = giftRollModel;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}


	@Override
	public String toString() {
		return "GiftRollEntity [id=" + getId() + ", serviceId=" + getServiceId() + ", smartcardId=" + getSmartcardId() + ", phone="
				+ getPhone() + ", userCode=" + getUserCode() + ", giftRollCode=" + getGiftRollCode() + ", giftRollName=" + getGiftRollName()
				+ ", giftRollAmount=" + giftRollAmount + ", startTime=" + getStartTime() + ", endTime=" + getEndTime()
				+ ", giftRollState=" + getGiftRollState() + ", enableGiftRollTime=" + getEnableGiftRollTime() + ", receivedTime=" + getReceivedTime() + ", giftRollModel="
				+ giftRollModel + "]";
	}

	/**
	 * 
	* @Title: enableGiftRoll  
	* @Description: 票券已经核销
	* @return  -1核销失败， 票券已经核销 ；0核销失败， 不在有效期内；1核销成功
	 */
	public int enableGiftRoll() {
		Timestamp now = new Timestamp(System.currentTimeMillis());
		if (getGiftRollState() == EntityContext.RECORD_STATE_VALID || null != getEnableGiftRollTime()) {
			if (now.before(getEndTime()) && now.after(getStartTime())) {
				setEnableGiftRollTime(
						Timestamp.valueOf(DateUtil.formatLongFormat(Calendar.getInstance().getTime())));
				setGiftRollState(EntityContext.RECORD_STATE_INVALID);
				return 1;
			}
			else {
				return 0;
			}
		}
		else {
			return -1;
		}
	}
	
	public String getVerfificationMessage(int resultCode) {
		
		if(resultCode == -1) {
			return "该券已经核销";
		}
		else if(resultCode == 0){
			return "不在有效期内";
		}
        else{
        	return "核销成功";
		}
	}
}
