package com.foshan.entity;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_service")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class ServiceEntity implements IEntityBean {
	/**
	 * 
	 */
	private static final long serialVersionUID = -1280135665769201109L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(18) comment '业务标识'")
	private String serviceGroup;
	@Column(columnDefinition = "varchar(10) comment '业务编号'")
	private String serviceCode;
	@Column(columnDefinition = "varchar(30) comment '业务名称'")
	private String serviceName;
	@Column(columnDefinition = "varchar(1) comment '业务状态 0-不可用 1-可用'")
	private Integer serviceState;
	@Column(columnDefinition = "varchar(10) comment '业务版本号'")
	private String serviceVersion;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '业务创建时间'")
	private Timestamp createTime;
	@Column(columnDefinition = "varchar(512) comment '机顶盒地址'")
	private String stbUrl;
	@Column(columnDefinition = "varchar(512) comment '网关地址'")
	private String ottUrl;
	
	@Column(columnDefinition = "Timestamp comment '开始时间'")
	private Timestamp startTime;
	@Column(columnDefinition = "Timestamp comment '结束时间'")
	private Timestamp endTime;
	@Column(columnDefinition = "varchar(200) comment '备注说明，可填写积分规则'")
	private String comment;
	@ManyToOne(targetEntity = ServiceEntity.class, cascade = CascadeType.MERGE, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentServiceId", referencedColumnName = "id", nullable = true, foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
	private ServiceEntity parentService;
	@SuppressWarnings("deprecation")
	@OneToMany(targetEntity = ServiceEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentServiceId", referencedColumnName = "id",nullable = true)
	@org.hibernate.annotations.ForeignKey(name = "none")
	@JsonIgnore
	private Set<ServiceEntity> subServiceSet = new HashSet<ServiceEntity>();
	@OneToOne(targetEntity = ColumnEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "columnId", referencedColumnName = "id", nullable = true, foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
	private ColumnEntity column;
	@SuppressWarnings("deprecation")
	@ManyToMany(targetEntity = RegionEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_region_service", joinColumns = @JoinColumn(name = "serviceId", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)), inverseJoinColumns = @JoinColumn(name = "regionId", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)))
	@org.hibernate.annotations.ForeignKey(name = "none")
	@JsonIgnore
	private Set<RegionEntity> regionList = new HashSet<RegionEntity>();
	@ManyToMany(targetEntity = ServiceVerificationCodeEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_business_business_verification_code", joinColumns = @JoinColumn(name = "serviceId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "verificationCodeId", referencedColumnName = "id"))
	@JsonIgnore
	private List<ServiceVerificationCodeEntity> businessVerificationCodeList = new ArrayList<ServiceVerificationCodeEntity>();
	
	@OneToMany(targetEntity = PointsPlanEntity.class,fetch = FetchType.LAZY)
	@JoinColumn(name = "serviceId", referencedColumnName = "id", nullable = true)
	private List<PointsPlanEntity> pointsPlanList;

	
	public ServiceEntity(Integer id, String serviceGroup, String serviceCode, String serviceName, Integer serviceState,
			String serviceVersion, Timestamp createTime) {
		super();
		this.id = id;
		this.serviceGroup = serviceGroup;
		this.serviceCode = serviceCode;
		this.serviceName = serviceName;
		this.serviceState = serviceState;
		this.serviceVersion = serviceVersion;
		this.createTime = createTime;
	}

	

	@Override
	public String toString() {
		return "ServiceEntity [id=" + id + ", serviceGroup=" + serviceGroup + ", serviceCode=" + serviceCode
				+ ", serviceName=" + serviceName + ", serviceState=" + serviceState + ", serviceVersion="
				+ serviceVersion + ", createTime=" + createTime + "]";
	}

}
