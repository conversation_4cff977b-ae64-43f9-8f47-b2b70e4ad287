package com.foshan.model.permssion;

import java.io.Serializable;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
public class PrincipalModel implements Serializable {

	/**
	 * 管理员用户模型，用于缓存用户数据
	 */
	private static final long serialVersionUID = -4122576254525070268L;

	private Integer id;//管理员id
	private String userCode;//管理员code
	private String userName;//管理员账号
	protected Integer userState;//管理员数据有效状态，0-无效，1-有效
	private String name;//管理员姓名
	private String phone;//管理员电话
	private String userImage;//管理员图片
	private String lastPwdModifyTime;//最后修改密码时间
	
}
