package com.foshan.form.response.gift;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.GiftRollForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取奖券列表返回对象(GetGiftRollListRes)")
@JsonInclude(Include.NON_NULL)
public class GetGiftRollListRes extends BasePageResponse{
	/**
	 * 
	 */
	private static final long serialVersionUID = -1290358573094987075L;
	@ApiModelProperty(value = "智能卡号")
	private String smartcardId;
	@ApiModelProperty(value = "电话")
	private String phone;
	@ApiModelProperty(value = "用户编号")
	private String userCode;
	@ApiModelProperty(value = "奖券列表")
	private List<GiftRollForm> giftList = new ArrayList<GiftRollForm>();
	
	public GetGiftRollListRes(int currentPage, int pageSize, int total, int totalResult) {
		super(currentPage, pageSize, total, totalResult);
		// TODO 自动生成的构造函数存根
	}
	public GetGiftRollListRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO 自动生成的构造函数存根
	}
	
}
