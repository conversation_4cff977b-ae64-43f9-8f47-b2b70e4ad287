package com.foshan.form.response.job;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "修改任务组返回对象(ModifyJobGroupRes)")
@JsonInclude(Include.NON_NULL)
public class ModifyJobGroupRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 4082248995021401863L;
	@ApiModelProperty(value = "任务组Id",example="1")
	private Integer jobGroupId;
	@ApiModelProperty(value = "任务组名称")
	private String jobGroupName;
	@ApiModelProperty(value = "任务组信息")
	private String jobGroupInfo;
	@ApiModelProperty(value = "任务更新时间",example="2019-01-01 00:00:00")
	private String updateTime;

	
	public ModifyJobGroupRes(Integer jobGroupId, String jobGroupName, String jobGroupInfo, String updateTime) {
		super();
		this.jobGroupId = jobGroupId;
		this.jobGroupName = jobGroupName;
		this.jobGroupInfo = jobGroupInfo;
		this.updateTime = updateTime;
	}

	
	@Override
	public String toString() {
		return "ModifyJobGroupRes [jobGroupId=" + jobGroupId + ", jobGroupName=" + jobGroupName + ", jobGroupInfo="
				+ jobGroupInfo + ", updateTime=" + updateTime + "]";
	}

}
