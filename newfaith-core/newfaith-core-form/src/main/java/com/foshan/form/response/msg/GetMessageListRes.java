package com.foshan.form.response.msg;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.MenuForm;
import com.foshan.form.message.MessageForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取消息列表返回对象(GetMessageListRes)")
@JsonInclude(Include.NON_NULL)
public class GetMessageListRes extends BasePageResponse {
	/**
	 *
	 */
	@ApiModelProperty(value = "消息对象列表")
	private List<MessageForm> mesaageList = new ArrayList<MessageForm>();



	public GetMessageListRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

}
