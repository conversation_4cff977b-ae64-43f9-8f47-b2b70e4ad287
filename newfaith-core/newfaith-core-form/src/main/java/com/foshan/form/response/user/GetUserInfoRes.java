package com.foshan.form.response.user;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.UserForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取角色信息返回对象(GetRoleInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetUserInfoRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 262916190805156748L;
	@ApiModelProperty(value = "角色对象")
	private UserForm user;
	
}
