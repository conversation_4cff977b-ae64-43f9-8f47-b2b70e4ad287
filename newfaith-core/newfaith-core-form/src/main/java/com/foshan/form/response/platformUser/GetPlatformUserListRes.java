package com.foshan.form.response.platformUser;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.PlatformUserForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取管理员列表返回对象(GetPlatformUserListRes)")
@JsonInclude(Include.NON_NULL)
public class GetPlatformUserListRes extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 3214653182265174694L;
	@ApiModelProperty(value = "管理员对象列表")
	private List<PlatformUserForm> userList = new ArrayList<PlatformUserForm>();

	

	public GetPlatformUserListRes(int currentPage, int pageSize, int total, int totalResult) {
		super(currentPage, pageSize, total, totalResult);
		// TODO 自动生成的构造函数存根
	}

	public GetPlatformUserListRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO 自动生成的构造函数存根
	}

	
}
