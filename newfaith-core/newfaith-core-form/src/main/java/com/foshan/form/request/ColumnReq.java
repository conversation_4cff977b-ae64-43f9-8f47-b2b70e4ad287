package com.foshan.form.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="栏目请求对象(ColumnForm)")
public class ColumnReq extends BasePageRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "栏目Id",example="1")
	private Integer columnId;
	@ApiModelProperty(value = "业务Id",example="1")
	private Integer serviceId;
	@ApiModelProperty(value = "父栏目Id",example="1")
	private Integer parentColumnId;
	@ApiModelProperty(value = "栏目编码")
	private String columnCode;
	@ApiModelProperty(value = "栏目名称")
	private String columnName;
	@ApiModelProperty(value = "栏目缩略图Id",example="1")
	private Integer columnImageId;
	@ApiModelProperty(value = "栏目手机缩略图Id",example="1")
	private Integer columnPhoneImageId;	
	@ApiModelProperty(value = "栏目类型 ：0--图文 1-音视频 2--投票  3--问卷 4--排行榜 5--抽奖 6--中奖结果 7--商城栏目  8--商城推荐栏目",example="1")
	private Integer columnType;
	@ApiModelProperty(value = "栏目路径")
	private String columnPath;
	@ApiModelProperty(value = "栏目信息")
	private String columnInfo;
	@ApiModelProperty(value = "栏目层级")
	private String columnLevel;
	@ApiModelProperty(value = "栏目上架状态 0--无效 1--有效",example="1")
	private Integer columnState;
	@ApiModelProperty(value = "栏目查询深度，默认10层",example="1")
	private Integer depth;
	@ApiModelProperty(value = "推荐标识：0--不推荐  1--推荐",example="1")
	private Integer commendFlag;
	@ApiModelProperty(value = "栏目排序值",example="1")
	private Integer orders;
	@ApiModelProperty(value = "显示目标终端： 0-电视端 1-移动端 2-通用",example="1")
	private Integer targetType;
	@ApiModelProperty(value = "映射外部系统：1--高清互动BO平台 2--IP视频平台",example="1")
	private Integer mappingSystem;
	@ApiModelProperty(value = "映射第三方系统栏目Id")
	private String mappingFolderId;
	@ApiModelProperty(value = "映射第三方系统栏目名称")
	private String mappingFolderName;
	@ApiModelProperty(value = "是否公共栏目 0--不是  1--是",example="1")
	private Integer isGlobal;
	@ApiModelProperty(value = "栏目数据状态 0--无效 1--有效",example="1")
	private Integer state;
	@ApiModelProperty(value = "区域Id列表，逗号分割")
	private String regionIds;
	@ApiModelProperty(value = "区域Id列表",example="1")
	private Integer regionId;

}
