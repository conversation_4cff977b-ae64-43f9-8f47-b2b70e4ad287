package com.foshan.form.response.job;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "修改任务返回对象(ModifyJobRes)")
@JsonInclude(Include.NON_NULL)
public class ModifyJobRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 5418759597930221823L;
	@ApiModelProperty(value = "任务组Id",example="1")
	private Integer jobGroupId;
	@ApiModelProperty(value = "任务Id",example="1")
	private Integer jobId;
	@ApiModelProperty(value = "任务名称")
	private String jobName;
	@ApiModelProperty(value = "任务信息")
	private String jobInfo;
	@ApiModelProperty(value = "任务执行类")
	private String jobClass;
	@ApiModelProperty(value = "任务执行方法")
	private String jobMethod;
	@ApiModelProperty(value = "任务cron表达式")
	private String jobCron;
	@ApiModelProperty(value = "任务状态 0--无效 1--有效",example="1")
	private Integer jobState;
	@ApiModelProperty(value = "任务更新时间",example="2019-01-01 00:00:00")
	private String updateTime;

	

	public ModifyJobRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public ModifyJobRes(Integer jobGroupId, Integer jobId, String jobName, String jobInfo, String jobClass,
			String jobMethod, String jobCron, Integer jobState, String updateTime) {
		super();
		this.jobGroupId = jobGroupId;
		this.jobId = jobId;
		this.jobName = jobName;
		this.jobInfo = jobInfo;
		this.jobClass = jobClass;
		this.jobMethod = jobMethod;
		this.jobCron = jobCron;
		this.jobState = jobState;
		this.updateTime = updateTime;
	}

	

	@Override
	public String toString() {
		return "ModifyJobRes [jobGroupId=" + jobGroupId + ", jobId=" + jobId + ", jobName=" + jobName + ", jobInfo="
				+ jobInfo + ", jobClass=" + jobClass + ", jobMethod=" + jobMethod + ", jobCron=" + jobCron
				+ ", jobState=" + jobState + ", updateTime=" + updateTime + "]";
	}

}
