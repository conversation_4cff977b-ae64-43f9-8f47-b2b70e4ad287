package com.foshan.form.response.assetSpec;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.AssetSpecForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "修改媒资规格返回对象(AddAssetSpecRes)")
@JsonInclude(Include.NON_NULL)
public class ModifyAssetSpecRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -161200382365508644L;
	@ApiModelProperty(value = "媒资规格对象")
	private AssetSpecForm assetSpecForm;

	public ModifyAssetSpecRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

}
