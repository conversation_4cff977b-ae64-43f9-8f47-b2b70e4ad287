package com.foshan.form.response.msg;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.message.MessageForm;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取未读消息数量(GetUnreadMessageCountRes)")
@JsonInclude(Include.NON_NULL)
public class GetUnreadMessageCountRes extends BaseResponse {

	/**
	 *  获取未读消息返回报文
	 */
	@ApiModelProperty(value = "未读消息数量")
	private Integer count ;
}
