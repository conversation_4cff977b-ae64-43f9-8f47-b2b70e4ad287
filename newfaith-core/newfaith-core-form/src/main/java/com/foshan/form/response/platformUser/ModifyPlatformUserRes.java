package com.foshan.form.response.platformUser;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "修改管理员返回对象(ModifyPlatformUserRes)")
@JsonInclude(Include.NON_NULL)
public class ModifyPlatformUserRes extends BaseResponse{

	/**
	 * 
	 */
	private static final long serialVersionUID = -1292752776142787804L;
	@ApiModelProperty(value = "管理员Id",example="1")
	private Integer userId;
	@ApiModelProperty(value = "管理员编号")
	private String userCode;
	@ApiModelProperty(value = "管理员名称")
	private String userName;
	@ApiModelProperty(value = "管理员姓名")
	private String name;

	public ModifyPlatformUserRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO 自动生成的构造函数存根
	}

	public ModifyPlatformUserRes(Integer userId, String userCode, String userName) {
		super();
		this.userId = userId;
		this.userCode = userCode;
		this.userName = userName;
	}
	
	
}
