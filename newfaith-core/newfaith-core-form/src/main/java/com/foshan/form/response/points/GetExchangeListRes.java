package com.foshan.form.response.points;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.AccountExchangeForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取兑换列表返回对象(GetExchangeListRes)")
@JsonInclude(Include.NON_NULL)
public class GetExchangeListRes extends BasePageResponse{
	/**
	 * 
	 */
	private static final long serialVersionUID = -8954708544573993976L;
	@ApiModelProperty(value = "兑换列表")
	private List<AccountExchangeForm> exchangeList = new ArrayList<AccountExchangeForm>();

	

	public GetExchangeListRes(int currentPage, int pageSize, int total, int totalResult) {
		super(currentPage, pageSize, total, totalResult);
		// TODO Auto-generated constructor stub
	}

	public GetExchangeListRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	
}
