package com.foshan.form.response.plugin;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.PluginConfigForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/** 
* @ClassName: package-info 
* @Description: TODO(获取插件信息列表返回报文) 
* <AUTHOR>
* @date 2019年3月12日 下午10:42:25 
*  
*/
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取插件列表返回对象(GetPluginConfigListRes)")
@JsonInclude(Include.NON_NULL)
public class GetPluginConfigRes extends BasePageResponse {
	@ApiModelProperty(value = "插件实例对象")
	PluginConfigForm pluginConfigForm = new PluginConfigForm();
	
}
