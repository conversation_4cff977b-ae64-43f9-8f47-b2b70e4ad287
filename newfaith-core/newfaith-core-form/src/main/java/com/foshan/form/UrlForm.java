package com.foshan.form;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@ApiModel(value = "链接对象(UrlForm)")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class UrlForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = 7840806405268356647L;
	@ApiModelProperty(value = "ID", example = "1")
	private Integer urlId;
	@ApiModelProperty(value = "标题")
	private String title;
	@ApiModelProperty(value = "目标ＵＲＬ链接")
	private String targetUrl;
	@ApiModelProperty(value = "审核状态 0:待审核 1：正常 2：审核不通过", example = "1")
	private Integer auditState;
	@ApiModelProperty(value = "审核意见")
	private String idea;
	@ApiModelProperty(value = "状态 0--无效数据  1--有效数据", example = "1")
	private Integer state;
	@ApiModelProperty(value = "创建时间", example = "2019-01-01 00:00:00")
	private String createTime;
	@ApiModelProperty(value = "修改时间", example = "2019-01-01 00:00:00")
	private String lastModifyTime;
	@ApiModelProperty(value = "审核时间", example = "2019-01-01 00:00:00")
	private String auditTime;
	@ApiModelProperty(value = "可见区域")
	private List<RegionForm> regionList = new ArrayList<RegionForm>();

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
