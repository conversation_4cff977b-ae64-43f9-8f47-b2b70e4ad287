package com.foshan.form.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="根据公众号CODE获取OPENDID(GetPublicAppOpenIdByCodeReq)")
public class GetPublicAppOpenIdByCodeReq extends BaseRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = -2370958733302274617L;
	@ApiModelProperty(value = "服务编码")
	private String serviceCode;
	@ApiModelProperty(value = "会员ID",example="1")
	private Integer accountId;
	@ApiModelProperty(value = "微信授权CODE")
	private String code;

}
