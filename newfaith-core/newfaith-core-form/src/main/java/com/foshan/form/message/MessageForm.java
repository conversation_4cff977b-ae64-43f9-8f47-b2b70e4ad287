package com.foshan.form.message;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="消息对象(消息Form)")
@JsonInclude(Include.NON_NULL)
public class MessageForm implements IForm {

	/**
	 *  消息FORM
	 */
	private static final long serialVersionUID = -1602292977754751937L;
	@ApiModelProperty(value = "消息ID",example="1")
	private Integer id;
	@ApiModelProperty(value = "消息编号")
	private String messageCode;
	@ApiModelProperty(value = "发送者ID",example="1")
	private Integer senderId;
	@ApiModelProperty(value = "发送者名称")
	private String senderName;
	@ApiModelProperty(value = "接收者ID")
	private Integer receiverId;
	@ApiModelProperty(value = "接收者类型:0-member,1-user")
	private Integer receiverType;
	@ApiModelProperty(value = "消息正文")
	private String content;
	@ApiModelProperty(value = "消息类型:0-系统消息,1-其它")
	private Integer type;
	@ApiModelProperty(value = "跳转链接")
	private String url;
	@ApiModelProperty(value = "消息状态：0-未读,1-已读")
	private Integer status;
	@ApiModelProperty(value = "发送时间")
	private String createTime;
	@ApiModelProperty(value = "阅读时间")
	private String readTime;
	@ApiModelProperty(value = "最后修改时间")
	private String lastModifyTime;
	@ApiModelProperty(value = "数据状态 0--无效数据  1--有效数据")
	private Integer state;

	@Override
	public int compareTo(Object o) {
		return 0;
	}
}
