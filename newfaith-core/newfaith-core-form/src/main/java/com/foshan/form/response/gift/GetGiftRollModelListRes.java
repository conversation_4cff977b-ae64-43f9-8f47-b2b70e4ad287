package com.foshan.form.response.gift;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.GiftRollModelForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取奖券模版列表返回对象(GetGiftRollModelListRes)")
@JsonInclude(Include.NON_NULL)
public class GetGiftRollModelListRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6881084593084643511L;
	@ApiModelProperty(value = "奖券模版列表")
	private List<GiftRollModelForm> modelList = new ArrayList<GiftRollModelForm>();

	

	public GetGiftRollModelListRes(int currentPage, int pageSize, int total, int totalResult) {
		super(currentPage, pageSize, total, totalResult);
		// TODO 自动生成的构造函数存根
	}

	public GetGiftRollModelListRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO 自动生成的构造函数存根
	}

	

}
