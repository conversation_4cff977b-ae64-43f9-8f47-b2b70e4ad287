package com.foshan.form.response.address;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.PlatAddressForm;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取地址列表返回对象(GetAddressListRes)")
@JsonInclude(Include.NON_NULL)
public class GetAddressListRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 5876084710207644555L;
	@ApiModelProperty(value = "根地址对象")
	private PlatAddressForm address;

	

	public GetAddressListRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	

}
