package com.foshan.form.response.platformUser;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.RegionForm;
import com.foshan.form.RoleForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取管理员信息返回对象(GetPlatformUserInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetPlatformUserInfoRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 81876238980518443L;
	@ApiModelProperty(value = "管理员Id",example="1")
	private Integer userId;
	@ApiModelProperty(value = "管理员编号")
	private String userCode;
	@ApiModelProperty(value = "管理员名称")
	private String userName;
	@ApiModelProperty(value = "管理员姓名")
	private String name;
	@ApiModelProperty(value = "角色列表")
	private List<RoleForm> roleList = new ArrayList<RoleForm>();
	@ApiModelProperty(value = "区域列表")
	private List<RegionForm> regionList = new ArrayList<RegionForm>();

	

	public GetPlatformUserInfoRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO 自动生成的构造函数存根
	}

	public GetPlatformUserInfoRes(Integer userId, String userCode, String userName) {
		super();
		this.userId = userId;
		this.userCode = userCode;
		this.userName = userName;
	}

	

}
