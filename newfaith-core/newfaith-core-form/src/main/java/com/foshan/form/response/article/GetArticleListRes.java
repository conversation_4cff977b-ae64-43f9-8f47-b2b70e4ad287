package com.foshan.form.response.article;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.ArticleForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取媒资返回列表对象(GetArticleListRes)")
@JsonInclude(Include.NON_NULL)
public class GetArticleListRes extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4198200947406207615L;
	@ApiModelProperty(value = "文章对象列表")
	private List<ArticleForm> articleFormList = new ArrayList<ArticleForm>();
	
	public GetArticleListRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}
}
