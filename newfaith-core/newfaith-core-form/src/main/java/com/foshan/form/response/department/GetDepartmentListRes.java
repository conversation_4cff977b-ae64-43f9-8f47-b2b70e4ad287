package com.foshan.form.response.department;



import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.DepartmentForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取部门列表返回对象(GetRoleListRes)")
@JsonInclude(Include.NON_NULL)
public class GetDepartmentListRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5236446348467117056L;
	@ApiModelProperty(value = "部门列表")
	private List<DepartmentForm> departmentList = new ArrayList<DepartmentForm>();
	
}
