package com.foshan.form.request;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;



@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="收款/付款(CommunityPaymentNotifyReq)")
public  class PaymentNotifyReq extends BaseRequest {


	/**
	 * 在线支付收款成功回调通知对象
	 */
	private static final long serialVersionUID = 2807627846069240604L;
	@ApiModelProperty(value = "支付结果状态:SUCCESS--成功收到款，FAIL--收款失败")
	private String returnCode;
	@ApiModelProperty(value = "业务系统支付订单号")
	private String clientTradeNo;
	@ApiModelProperty(value = "faith支付模块支付会话流水号")
	private String paymentSessionSn;
	@ApiModelProperty(value = "第三方系统交易流水号")
	private String outTradeNo;
	@ApiModelProperty(value = "成功支付的金额")
	private BigDecimal amount;
	
	@ApiModelProperty(value = "支付模块的商户号")
	private String merchantCode;
	@ApiModelProperty(value = "随机UUID")
	private String nonceStr;
	@ApiModelProperty(value = "加了支付模块商户secret的SM3签名")
	private String sign;
	
	@Override
	public String toString() {
		return "CommunityPaymentNotifyReq [returnCode=" + returnCode + ", clientTradeNo=" + clientTradeNo
				+ ", paymentSessionSn=" + paymentSessionSn + ", outTradeNo=" + outTradeNo + ", amount=" + amount
				+ ", merchantCode=" + merchantCode + ", nonceStr=" + nonceStr + ", sign=" + sign + "]";
	}
	
	
  
	
	
}
