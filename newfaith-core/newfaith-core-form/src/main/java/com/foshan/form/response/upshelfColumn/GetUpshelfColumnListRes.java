package com.foshan.form.response.upshelfColumn;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.UpshelfColumnForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


@ApiModel(value="获取栏目上架返回对象")
@JsonInclude(Include.NON_NULL)
public class GetUpshelfColumnListRes extends BasePageResponse {
		
	/**
	 * 
	 */
	private static final long serialVersionUID = -8405830011284534909L;
	@ApiModelProperty(value = "上架列表")
	private List<UpshelfColumnForm> upshelfColumnList = new ArrayList<UpshelfColumnForm>();

	public GetUpshelfColumnListRes() {
		super();
		// TODO Auto-generated constructor stub
	}

	public GetUpshelfColumnListRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public List<UpshelfColumnForm> getUpshelfColumnList() {
		return upshelfColumnList;
	}

	public void setUpshelfColumnList(List<UpshelfColumnForm> upshelfColumnList) {
		this.upshelfColumnList = upshelfColumnList;
	}


	
}
