package com.foshan.form.response.msg;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.MenuForm;
import com.foshan.form.message.MessageForm;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取消息(GetMessageRes)")
@JsonInclude(Include.NON_NULL)
public class GetMessageRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8666075052171709887L;
	@ApiModelProperty(value = "消息")
	private MessageForm messageForm = new MessageForm();

	

}
