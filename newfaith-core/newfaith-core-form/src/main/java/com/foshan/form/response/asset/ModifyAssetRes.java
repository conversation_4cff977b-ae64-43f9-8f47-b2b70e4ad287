package com.foshan.form.response.asset;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.AssetForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="修改媒资返回对象(AddAssetRes)")
@JsonInclude(Include.NON_NULL)
public class ModifyAssetRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -7514298830648833283L;
	@ApiModelProperty(value = "媒资对象")
	private AssetForm assetForm;

	
	public ModifyAssetRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	
}
