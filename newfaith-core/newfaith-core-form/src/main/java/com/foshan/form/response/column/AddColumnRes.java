package com.foshan.form.response.column;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.RegionForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="新增栏目返回对象(AddColumnRes)")
@JsonInclude(Include.NON_NULL)
public class AddColumnRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2226513837469130336L;
	@ApiModelProperty(value = "栏目Id",example="1")
	private Integer columnId;
	@ApiModelProperty(value = "栏目编号")
	private String columnCode;
	@ApiModelProperty(value = "栏目名称")
	private String columnName;
	@ApiModelProperty(value = "栏目上架状态 0--无效 1--有效",example="1")
	private Integer columnState;
	@ApiModelProperty(value = "栏目层级",example="1")
	private Integer columnLevel;
	@ApiModelProperty(value = "业务Id",example="1")
	private Integer serviceId;
	@ApiModelProperty(value = "是否公共栏目 0--不是  1--是",example="1")
	private Integer isGlobal;
	@ApiModelProperty(value = "栏目数据状态：0--无效 1--有效",example="1")
	private Integer state;
	@ApiModelProperty(value = "父级栏目Id",example="1")
	private Integer parentColumnId;
	@ApiModelProperty(value = "栏目所属区域列表")
	private List<RegionForm> regionList = new ArrayList<RegionForm>();

	public AddColumnRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	
	@Override
	public String toString() {
		return "AddColumnRes [columnId=" + columnId + ", columnCode=" + columnCode + ", columnName=" + columnName
				+ ", columnState=" + columnState + ", serviceId=" + serviceId + ", parentColumnId=" + parentColumnId
				+ "]";
	}

}
