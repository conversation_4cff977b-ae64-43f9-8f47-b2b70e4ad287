package com.foshan.form;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="角色对象(RoleForm)")
@JsonInclude(Include.NON_NULL)
public class RoleForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8795283615589375809L;
	@ApiModelProperty(value = "角色Id",example="1")
	private Integer roleId;
	@ApiModelProperty(value = "角色名称")
	private String roleName;
	@ApiModelProperty(value = "角色状态 0--无效  1--有效")
	private Integer roleState;
	@ApiModelProperty(value = "是否内置角色（1:内置角色，不能删除）")
	private boolean isBuiltIn;
	@ApiModelProperty(value = "名称,前端显示用")
	private String displayName;
	@ApiModelProperty(value = "绑定菜单 1:已绑定；0：否；",example="1")
	private Integer isBindingPermission;
	@ApiModelProperty(value = "绑定菜单 1:已绑定；0：否；",example="1")
	private Integer isBindingMenu;
	@ApiModelProperty(value = "角色权限列表")
	private List<PermissionForm> permissionList = new ArrayList<PermissionForm>();
	@ApiModelProperty(value = "分组角色权限列表")
	private List<PermissionGroupForm> permissionGroupList = new ArrayList<PermissionGroupForm>();

	@Override
	public int compareTo(Object o) {
		// TODO 自动生成的方法存根
		return 0;
	}

	@Override
	public String toString() {
		return "RoleForm [roleId=" + roleId + ", roleName=" + roleName + ", roleState=" + roleState + ", isBuiltIn="
				+ isBuiltIn + ", displayName=" + displayName + ", permissionList=" + permissionList
				+ ", permissionGroupList=" + permissionGroupList + "]";
	}
}
