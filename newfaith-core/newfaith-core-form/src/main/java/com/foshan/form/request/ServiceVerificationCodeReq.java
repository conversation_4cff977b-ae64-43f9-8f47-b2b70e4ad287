package com.foshan.form.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="业务验证码请求对象(ServiceVerificationCodeReq)")
public  class ServiceVerificationCodeReq extends BasePageRequest {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "验证码ID",example="1")
	private Integer serviceVerificationCodeId;
    @ApiModelProperty(value = "时间",example="1")
    private String createTime;
    @ApiModelProperty(value = "最后修改时间")
    private String lastModifyTime;
    @ApiModelProperty(value = "状态 0--无效数据  1--有效数据",example="1")
    private Integer state;
    @ApiModelProperty(value = "验证码")
    private String codes;
    @ApiModelProperty(value = "结束时间")
    private String endTime;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    @ApiModelProperty(value = "业务ID，多个以英文逗号隔开")
    private String serviceIdList;
	@ApiModelProperty(value = "指定智能卡，0否；1是；",example="1")
	private Integer assignSmartcard;
    @ApiModelProperty(value = "智能卡号，多个以英文逗号隔开")
    private String serviceSmartcardIdList;
	
}
