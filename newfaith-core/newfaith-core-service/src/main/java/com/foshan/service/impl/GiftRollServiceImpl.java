package com.foshan.service.impl;

import java.math.BigDecimal;
import java.util.Calendar;
import java.sql.Timestamp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foshan.dao.generic.Page;
import com.foshan.entity.GiftRollEntity;
import com.foshan.entity.GiftRollModelEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.GiftRollForm;
import com.foshan.form.GiftRollModelForm;
import com.foshan.form.request.GiftRollModelReq;
import com.foshan.form.request.GiftRollReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.gift.AddGiftRollModelRes;
import com.foshan.form.response.gift.AddGiftRollRes;
import com.foshan.form.response.gift.EnableGiftRollRes;
import com.foshan.form.response.gift.GetGiftRollListRes;
import com.foshan.form.response.gift.GetGiftRollModelListRes;
import com.foshan.form.response.gift.ModifyGiftRollModelRes;
import com.foshan.service.IGiftRollService;
import com.foshan.util.DateUtil;

@Transactional
@Service("giftRollService")
public class GiftRollServiceImpl extends GenericService implements IGiftRollService {

	@Override
	public IResponse addGiftRollModel(GiftRollModelReq req) {
		// TODO 自动生成的方法存根
		AddGiftRollModelRes res = new AddGiftRollModelRes();
		if (StringUtils.isNotEmpty(req.getModelName()) && null != req.getModelType() && null != req.getModelCount()
				&& null != req.getModelAmount() && StringUtils.isNotEmpty(req.getStartTime())) {
			GiftRollModelEntity model = new GiftRollModelEntity();
			BeanUtils.copyProperties(req, model);
			model.setModelAmount(new BigDecimal(req.getModelAmount()));
			model.setModelPeriodType(
					null != req.getModelPeriodType() ? req.getModelPeriodType() : EntityContext.RECORD_STATE_INVALID);
			model.setStartTime(Timestamp.valueOf(req.getStartTime()));
			model.setEndTime(Timestamp
					.valueOf(StringUtils.isNotEmpty(req.getEndTime()) ? req.getEndTime() : "2020-12-31 23:59:59"));
			model.setModelState(null != req.getModelState() ? req.getModelState() : EntityContext.RECORD_STATE_VALID);
			model.setIsThirdCard(
					null != req.getIsThirdCard() ? req.getIsThirdCard() : EntityContext.RECORD_STATE_INVALID);
			int modelId = (Integer) giftRollModelDao.save(model);
			res.setModelId(modelId);
			res.setModelName(req.getModelName());
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse modifyGiftRollModel(GiftRollModelReq req) {
		// TODO 自动生成的方法存根
		ModifyGiftRollModelRes res = new ModifyGiftRollModelRes();
		if (null != req.getModelId()) {
			GiftRollModelEntity model = giftRollModelDao.get(req.getModelId());
			if (null != model) {
				model.setModelName(
						StringUtils.isNotEmpty(req.getModelName()) ? req.getModelName() : model.getModelName());
				model.setModelAmount(
						null != req.getModelAmount() ? new BigDecimal(req.getModelAmount()) : model.getModelAmount());
				model.setModelCount(null != req.getModelCount() ? req.getModelCount() : model.getModelCount());
				model.setModelInfo(
						StringUtils.isNotEmpty(req.getModelInfo()) ? req.getModelInfo() : model.getModelInfo());
				model.setModelType(null != req.getModelType() ? req.getModelType() : model.getModelType());
//				model.setRemainingDays(
//						null != req.getRemainingDays() ? req.getRemainingDays() : model.getRemainingDays());
				model.setStartTime(StringUtils.isNotEmpty(req.getStartTime()) ? Timestamp.valueOf(req.getStartTime())
						: model.getStartTime());
				model.setEndTime(StringUtils.isNotEmpty(req.getEndTime()) ? Timestamp.valueOf(req.getEndTime())
						: model.getEndTime());
				model.setModelState(null != req.getModelState() ? req.getModelState() : model.getModelState());
				model.setIsThirdCard(null != req.getIsThirdCard() ? req.getIsThirdCard() : model.getIsThirdCard());
				model.setModelPeriodType(
						null != req.getModelPeriodType() ? req.getModelPeriodType() : model.getModelPeriodType());
				// model.setProductId(null != req.getProductId() ? req.getProductId() :
				// model.getProductId());
				BeanUtils.copyProperties(model, res);
				res.setEndTime(DateUtil.formatLongFormat(model.getEndTime()));
				res.setModelAmount(model.getModelAmount());
				res.setModelId(model.getId());
				res.setStartTime(DateUtil.formatLongFormat(model.getEndTime()));
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet("0001");
				res.setRetInfo("要修改的赠卷不存在！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse deleteGiftRollModel(GiftRollModelReq req) {
		// TODO 自动生成的方法存根
		return null;
	}

	@Override
	public IResponse getGiftRollModelList(GiftRollModelReq req) {
		// TODO 自动生成的方法存根
		GetGiftRollModelListRes res = new GetGiftRollModelListRes();
		Page<GiftRollModelEntity> page = new Page<GiftRollModelEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

		StringBuilder hql = new StringBuilder("select gift from GiftRollModelEntity gift ");

		if (null != req.getModelState()) {
			hql.append(" where gift.modelState=?1");
			page = giftRollModelDao.queryPage(page, hql.toString(), req.getModelState());
		} else {
			page = giftRollModelDao.queryPage(page, hql.toString());
		}

		BeanUtils.copyProperties(page, res);
		page.getResultList().forEach(o -> {
			res.getModelList()
					.add(new GiftRollModelForm(o.getId(), o.getModelName(), o.getModelAmount(), o.getModelCount(),
							o.getRemainingDays(), DateUtil.formatLongFormat(o.getStartTime()),
							DateUtil.formatLongFormat(o.getEndTime()), o.getModelType(), o.getModelPeriodType(),
							o.getModelInfo(), o.getIsThirdCard(), o.getProductId()));
		});

		return res;
	}

	@Override
	public IResponse addGiftRoll(GiftRollReq req) {
		// TODO 自动生成的方法存根
		AddGiftRollRes res = new AddGiftRollRes();
		if (null != req.getServiceId() && null != req.getModelId() && (StringUtils.isNotEmpty(req.getSmartcardId())
				|| StringUtils.isNotEmpty(req.getPhone()) || StringUtils.isNotEmpty(req.getUserCode()))) {
			GiftRollModelEntity model = giftRollModelDao.get(req.getModelId());
			if (null != model) {
				GiftRollEntity gift = new GiftRollEntity();
				BeanUtils.copyProperties(req, gift);
				gift.setGiftRollModel(model);
				gift.setGiftRollName(model.getModelName());
				if (model.getModelType() == EntityContext.GIFTROLL_MODEL_TYPE_PERIOD) {
					gift.setStartTime(model.getStartTime());
					gift.setEndTime(model.getEndTime());
				} else if (model.getModelType() == EntityContext.GIFTROLL_MODEL_TYPE_REMAIN) {
					Calendar cal = Calendar.getInstance();
					gift.setStartTime(Timestamp.valueOf(DateUtil.formatLongFormat(cal.getTime())));
					cal.add(Calendar.DAY_OF_YEAR, model.getRemainingDays());
					gift.setEndTime(Timestamp.valueOf(DateUtil.formatLongFormat(cal.getTime())));
				}
				gift.setGiftRollAmount(model.getModelAmount());
				gift.setGiftRollState(EntityContext.RECORD_STATE_VALID);
				Integer giftId = (Integer) giftRollDao.save(gift);

				res.setGiftId(giftId);
				res.setModelId(model.getId());
				res.setGiftName(model.getModelName());
				res.setGiftAmount(model.getModelAmount());
				res.setStartTime(DateUtil.formatLongFormat(gift.getStartTime()));
				res.setEndTime(DateUtil.formatLongFormat(gift.getEndTime()));
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

			} else {
				res.setRet("0001");
				res.setRetInfo("要增加奖券的不存在！！！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getGiftRollList(GiftRollReq req) {
		// TODO 自动生成的方法存根
		GetGiftRollListRes res = new GetGiftRollListRes();
		if (StringUtils.isNotEmpty(req.getSmartcardId()) || StringUtils.isNotEmpty(req.getPhone())
				|| StringUtils.isNotEmpty(req.getUserCode())) {
			Page<GiftRollEntity> page = new Page<GiftRollEntity>();
			page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
			page.setBeginCount((req.getRequestPage() - 1) * req.getPageSize());
			page.setCurrentPage(req.getRequestPage());

			StringBuilder hql = new StringBuilder("select a from GiftRollEntity a ");
			if (StringUtils.isNotEmpty(req.getSmartcardId())) {
				hql.append(" where a.smartcardId='"+req.getSmartcardId()+"' order by a.id desc");
				page = giftRollDao.queryPage(page, hql.toString());
			} else if (StringUtils.isEmpty(req.getSmartcardId()) && StringUtils.isNotEmpty(req.getPhone())) {
				hql.append(" where a.phone='"+req.getPhone()+"' order by a.id desc");
				page = giftRollDao.queryPage(page, hql.toString());
			} else if (StringUtils.isEmpty(req.getSmartcardId()) && StringUtils.isNotEmpty(req.getUserCode())) {
				hql.append(" where a.userCode='"+req.getUserCode()+"' order by a.id desc");
				page = giftRollDao.queryPage(page, hql.toString());
			}

			res.setTotalResult(page.getTotalCount());
			res.setPageSize(req.getPageSize());
			res.setCurrentPage(req.getRequestPage());
			res.setTotal(page.getTotalPage());

			page.getResultList().forEach(o -> {
				res.getGiftList().add(new GiftRollForm(o.getServiceId(), o.getColumnId(), o.getGroupId(), o.getId(),
						o.getSmartcardId(), o.getPhone(), o.getUserCode(), o.getGiftRollCode(), o.getGiftRollName(),
						o.getGiftRollAmount().intValue(), o.getGiftRollState(),
						DateUtil.formatLongFormat(o.getStartTime()), DateUtil.formatLongFormat(o.getEndTime()),
						DateUtil.formatLongFormat(o.getEnableGiftRollTime()), o.getGiftRollModel().getProductId()));
			});
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse enableGiftRoll(GiftRollReq req) {
		// TODO 自动生成的方法存根
		EnableGiftRollRes res = new EnableGiftRollRes();
		res.setGiftId(req.getGiftId());
		if (null != req.getGiftId()) {
			GiftRollEntity gift = giftRollDao.get(req.getGiftId());
			if (null != gift) {
				int enableGiftRollResult = gift.enableGiftRoll();
				if (enableGiftRollResult == 1) {
	//				if (null != req.getSmartcardId()) {
//						try {
	//						logger.info("开始访问Boss数据接口！！！");
	//						ObjectMapper mapper = new ObjectMapper();
	//						CloseableHttpClient httpclient = HttpClients.createDefault();
	//						// 获取boss用户信息
	//						URI infoUri = new URIBuilder().setScheme("http").setHost(ContextInfo.bossIp)
	//								.setPort(Integer.parseInt(ContextInfo.bossPort)).setPath(ContextInfo.bossInfoPath)
	//								.setParameter("jsonData", "{\"deviceId\":\"" + req.getSmartcardId() + "\"}")
	//								.build();
							//
	//						HttpGet infoGet = new HttpGet(infoUri);
	//						long start = System.currentTimeMillis();
	//						CloseableHttpResponse infoRes = httpclient.execute(infoGet);
	//						BossAccountInfo accountInfo = (BossAccountInfo) mapper
	//								.readValue(infoRes.getEntity().getContent(), BossAccountInfo.class);
	//						long e1 = System.currentTimeMillis();
	//						logger.info("boss接口返回数据(" + (e1 - start) + "毫秒):" + accountInfo);
	//						if (accountInfo.getStatus().equals("0")) {
	//							// 充值
	//							URI chargeUri = new URIBuilder().setScheme("http").setHost(ContextInfo.bossIp)
	//									.setPort(Integer.parseInt(ContextInfo.bossPort))
	//									.setPath(ContextInfo.bossChargePath)
	//									.setParameter("jsonData",
	//											"{\"custid\":\"" + accountInfo.getCustid() + "\",\"keyno\":\""
	//													+ req.getSmartcardId()
	//													+ "\",\"permark\":\"3\",\"servid\":\"0\",\"fees\":\""
	//													+ gift.getGiftRollAmount().intValue()
	//													+ "\",\"boxid\":\"1118\",\"boxtype\":\"2\"}")
	//									.build();
	//							HttpGet chargetGet = new HttpGet(chargeUri);
	//							logger.info("开始调用boss充值接口！！！");
	//							CloseableHttpResponse chargeRes = httpclient.execute(chargetGet);
	//							ChargeResult charge = (ChargeResult) mapper
	//									.readValue(chargeRes.getEntity().getContent(), ChargeResult.class);
	//							long e2 = System.currentTimeMillis();
	//							logger.info("充值接口返回(" + (e2 - e1) + "毫秒)：" + charge);
	//							if (!charge.getStatus().equals("0")) {
	//								res.setGiftId(gift.getId());
	//								res.setRet(charge.getStatus());
	//								res.setRetInfo(charge.getMessage());
	//								return res;
	//							}
	//						} else {
	//							res.setRet("0001");
	//							res.setRetInfo("要充值的用户不存在！！！");
	//							return res;
	//						}
	//					} catch (Exception ex) {
	//						res.setRet("0001");
	//						res.setRetInfo("赠券使用错误了！！！");
	//						return res;
	//					}
	//				}
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

				} else if (enableGiftRollResult == 0) {
					res.setRet("0001");
					res.setRetInfo("请在票券的有效期内使用！！！");
				} else {
					res.setRet("0001");
					res.setRetInfo("赠卷已经使用！！！");
				}

			} else {
				res.setRet("0001");
				res.setRetInfo("要使用的赠卷不存在！！！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}
