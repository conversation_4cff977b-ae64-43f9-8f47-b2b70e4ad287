package com.foshan.service.impl;

import cn.hutool.core.lang.UUID;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.WebsocketClientMessageForm;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.msg.GetMessageRes;
import com.foshan.form.response.msg.GetUnreadMessageCountRes;
import com.foshan.model.permssion.PrincipalModel;
import com.foshan.service.IMessageService;
import com.foshan.service.annotation.Audit;
import com.foshan.service.websocket.WebsocketClient;
import com.foshan.util.DateUtil;
import com.hazelcast.spring.cache.HazelcastCacheManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foshan.dao.generic.Page;
import com.foshan.entity.MessageEntity;
import com.foshan.form.message.MessageForm;
import com.foshan.form.request.MessageReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.msg.GetMessageListRes;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.model.permssion.AccountPrincipalModel;
import org.springframework.beans.BeanUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.Date;


@Transactional
@Service("messageService")
public class MessageServiceImpl extends GenericService implements IMessageService {
	private final static Logger logger  = LoggerFactory.getLogger(MessageServiceImpl.class);
	@Resource
	private HazelcastCacheManager cacheManager;
//	@Override
//	@Audit(operate = "新增消息")
//	public IResponse addMessage(MessageReq req) {
//		// TODO Auto-generated method stub
//		AddMessageRes res = new AddMessageRes();
//		if (StringUtils.isNotEmpty(req.getMessageCode())) {
//			MessageEntity message = new MessageEntity();
//			MessageForm messageForm = new MessageForm();
//			BeanUtils.copyProperties(req, message);
//			BeanUtils.copyProperties(req,res);
//			message.setPackageCount(0);
//			message.setPackageFlag(EntityContext.ASSET_PACKAGE_FLAG_INVALID);
//			if (null != req.getParentMessageId()) {
//				MessageEntity parentMessage = messageDao.get(req.getParentMessageId());
//				if (null != parentMessage) {
//					message.setParentMessage(parentMessage);
//					parentMessage.setPackageFlag(EntityContext.ASSET_PACKAGE_FLAG_VALID);
//					parentMessage.setPackageCount(
//							null != parentMessage.getPackageCount() ? parentMessage.getPackageCount() + 1 : 1);
//					messageForm.setParentMessageId(parentMessage.getId());
//				} else {
//					res.setRet("0001");
//					res.setRetInfo("要增加消息的消息包不存在！！！");
//				}
//			}
//			try {
//				Integer messageId = (Integer) messageDao.save(message);
//				messageForm.setMessageId(messageId);
//			} catch (Exception ex) {
//				logger.error(ex.getMessage());
//				res.setRet("0001");
//				res.setRetInfo("要增加的消息已存在！！！");
//				return res;
//			}
//			res.setMessageForm(messageForm);
//			res.setRet(ResponseContext.RES_SUCCESS_CODE);
//			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}
//		return res;
//	}
//
	@Override
	@Audit(operate = "阅读消息")
	public IResponse readMessage(MessageReq req) {
		// TODO Auto-generated method stub
		GenericResponse res = new GenericResponse();

		Integer receiverId = null;
		Integer receiverType = null;
		try{
			Object userObj = getPrincipal(true);
			if (userObj instanceof AccountPrincipalModel){
				AccountPrincipalModel account = (AccountPrincipalModel) userObj;
				receiverId = account.getId();
				receiverType = 0;
			}
			else if(userObj instanceof PrincipalModel){
				PrincipalModel user = (PrincipalModel) userObj;
				receiverId = user.getId();
				receiverType = 1;
			}else{
				res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
				return res;
			}

			if(req.getId() == null && req.getMessageCode() == null){
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO+"消息id和消息code不能同时为空！");
				return res;
			}

			StringBuilder hql =  new StringBuilder("SELECT m FROM MessageEntity m WHERE");
			if(req.getId() != null ){
				hql.append(" m.id = " + req.getId());
			}
			else{
				hql.append(" m.messageCode = '" + req.getMessageCode() + "'");
			}

			hql.append(" AND m.state = 1");

			MessageEntity entity = messageDao.findUnique(hql.toString());
			if(entity != null){
				if((entity.getReceiverId() != null && entity.getReceiverId().intValue() != receiverId) || (entity.getReceiverType() != null && entity.getReceiverType().intValue() != receiverType)){
					res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
					res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO + "当前用户没有权限阅读该消息!");
					return res;
				}
				entity.setStatus(1);
				entity.setReadTime(new Timestamp(new Date().getTime()));
				messageDao.update(entity);
				refreshUnreadCount(receiverId, receiverType);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}
			else{
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
			}

		}
		catch(Exception e){
			logger.info("阅读消息详情时出错{0}", e);
			res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getMessageList(MessageReq req) {
		// TODO Auto-generated method stub
		GetMessageListRes res = new GetMessageListRes();
		Integer receiverId = null;
		Integer receiverType = null;
        try{
            Object userObj = getPrincipal(true);
            if (userObj instanceof AccountPrincipalModel){
                AccountPrincipalModel account = (AccountPrincipalModel) userObj;
                receiverId = account.getId();
                receiverType = 0;
            }
            else if(userObj instanceof PrincipalModel){
                PrincipalModel user = (PrincipalModel) userObj;
                receiverId = user.getId();
                receiverType = 1;
            }else{
				res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
				return res;
			}

            Page<MessageEntity> page = new Page<MessageEntity>();
            page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
            page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1)
                    * page.getPageSize());
            page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

            StringBuilder hql =  new StringBuilder("select m from MessageEntity m where m.receiverId = " + receiverId + " and m.receiverType = " + receiverType);
            if(req.getStatus() != null){
                hql.append(" and m.status = " + req.getStatus());
            }
            hql.append(" and m.state = 1 order by m.createTime DESC");
            Page<MessageEntity> queryPage = messageDao.queryPage(page,hql.toString());
            res.setTotalResult(page.getTotalCount());
            res.setPageSize(page.getPageSize());
            res.setCurrentPage(page.getCurrentPage());
            res.setTotal(page.getTotalPage());

            page.getResultList().forEach(o -> {
                MessageForm messageForm = new MessageForm();
                BeanUtils.copyProperties(o, messageForm);
				if(o.getReadTime() != null)messageForm.setReadTime(DateUtil.format(o.getReadTime(),1) );
				if(o.getLastModifyTime() != null)messageForm.setLastModifyTime(DateUtil.format(o.getLastModifyTime(),1));
				if(o.getCreateTime() != null)messageForm.setCreateTime(DateUtil.format(o.getCreateTime(),1));
                res.getMesaageList().add(messageForm);
            });

            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} catch (Exception e) {
			logger.info("获取消息列表时出错{0}", e);
			res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
		}

		return res;
	}

	@Override
	public GetUnreadMessageCountRes getUnreadCount(MessageReq req) {
		GetUnreadMessageCountRes res = new GetUnreadMessageCountRes();
		Integer receiverId = null;
		Integer receiverType = null;
		Integer count = null;
		try{
			Object userObj = getPrincipal(true);
			if (userObj instanceof AccountPrincipalModel){
				AccountPrincipalModel account = (AccountPrincipalModel) userObj;
				receiverId = account.getId();
				receiverType = 0;
			}
			else if(userObj instanceof PrincipalModel){
				PrincipalModel user = (PrincipalModel) userObj;
				receiverId = user.getId();
				receiverType = 1;
			}else{
				res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
				return res;
			}

			// 先从Hazelcast缓存获
			Cache unreadCountMap = cacheManager.getCache("unreadMessageCount");
			String key = receiverType + ":" + receiverId;
			count = unreadCountMap.get(key, Integer.class);

			if (count == null) {
				// 缓存不存在，从数据库查询并更新缓存
				count = messageDao.countByHql("SELECT count(m.id) FROM MessageEntity m WHERE m.status = 0 AND m.state = 1 AND m.receiverId = " + receiverId + " AND m.receiverType = " + receiverType );
				unreadCountMap.put(key, count);
			}
		} catch (Exception e) {
			logger.info("获取未读消息数量时出错{}", e);
			res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
		}

		res.setCount(count);
		return res;
	}

	private Integer refreshUnreadCount(Integer receiverId,Integer receiverType) {
		Cache unreadCountMap = cacheManager.getCache("unreadMessageCount");
		String key = receiverType + ":" + receiverId;
		Integer count = null;
		count = messageDao.countByHql("SELECT count(m.id) FROM MessageEntity m WHERE m.status = 0 AND m.state = 1 AND m.receiverId = " + receiverId + " AND m.receiverType = " + receiverType );
		unreadCountMap.put(key, count);
		return count;
	}

	/**
	 * 发送websocket系统消息并持久化
	 * @param req com.foshan.form.request.MessageReq
	 * @return
	 */
	public GenericResponse sendMessage(MessageReq req) {
		// TODO Auto-generated method stub
		GenericResponse res = new GenericResponse();
		if(req.getReceiverType() == null ){
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO+"receiverType不能为空！");
			return res;
		}
		if(req.getReceiverId() == null ){
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO+"receiverId不能为空");
			return res;
		}
		if(req.getContent() == null ){
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO+"content不能为空！");
			return res;
		}
		if(req.getType() == null ){
			req.setType(0);
		}
		req.setStatus(0);
		req.setState(1);
		req.setMessageCode(UUID.fastUUID().toString());

		// 创建消息实体
		MessageEntity message = new MessageEntity();
		BeanUtils.copyProperties(req, message);

		// 保存到数据库
		messageDao.save(message);

		// 转换为DTO
		WebsocketClientMessageForm websocketClientMessageForm = new WebsocketClientMessageForm();
		websocketClientMessageForm.setDetailInterface(req.getUrl());
		websocketClientMessageForm.setRequestParameterName("warningMessageId");
		websocketClientMessageForm.setEventId(req.getMessageCode());
		websocketClientMessageForm.setSeverity(4);
		websocketClientMessageForm.setMessageTile("系统消息");
		websocketClientMessageForm.setMessageContent(req.getContent());
		websocketClientMessageForm.setSourceType(1);
		String json="";
		try {
			websocketClientMessageForm.setRemark(mapper.writeValueAsString(message));
			json = mapper.writeValueAsString(websocketClientMessageForm);
			WebsocketClient.sendMessageByClientType(json,2);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} catch (JsonProcessingException e) {
			logger.info("发送消息，jason转换出错：{}", e);
			res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
			throw new RuntimeException(e);
		} catch (IOException e) {
			logger.info("发送消息出错：{}", e);
			res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
            throw new RuntimeException(e);
        }
        // 刷新接收者的未读计数缓存
		refreshUnreadCount(message.getReceiverId(), message.getReceiverType());

		return res;
	}
	
	@Override
	public IResponse getMessageInfo(MessageReq req) {
		// TODO Auto-generated method stub
		GetMessageRes res = new GetMessageRes();
		MessageForm messageForm = new MessageForm();

		Integer receiverId = null;
		Integer receiverType = null;
		try{
			Object userObj = getPrincipal(true);
			if (userObj instanceof AccountPrincipalModel){
				AccountPrincipalModel account = (AccountPrincipalModel) userObj;
				receiverId = account.getId();
				receiverType = 0;
			}
			else if(userObj instanceof PrincipalModel){
				PrincipalModel user = (PrincipalModel) userObj;
				receiverId = user.getId();
				receiverType = 1;
			}else{
				res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
				return res;
			}

			if(req.getId() == null && req.getMessageCode() == null){
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO+"消息id和消息code不能同时为空！");
				return res;
			}

			StringBuilder hql =  new StringBuilder("SELECT m FROM MessageEntity m WHERE");
			if(req.getId() != null ){
				hql.append(" m.id = " + req.getId());
			}
			else{
				hql.append(" m.messageCode = '" + req.getMessageCode() + "'");
			}

			hql.append(" AND m.state = 1");

			MessageEntity entity = messageDao.findUnique(hql.toString());
			if(entity != null){
				if((entity.getReceiverId() != null && entity.getReceiverId().intValue() != receiverId) || (entity.getReceiverType() != null && entity.getReceiverType().intValue() != receiverType)){
					res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
					res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO + "当前用户没有权限查看该消息!");
					return res;
				}
				BeanUtils.copyProperties(entity, messageForm);
				if(entity.getReadTime() != null)messageForm.setReadTime(DateUtil.format(entity.getReadTime(),1) );
				if(entity.getLastModifyTime() != null)messageForm.setLastModifyTime(DateUtil.format(entity.getLastModifyTime(),1));
				if(entity.getCreateTime() != null)messageForm.setCreateTime(DateUtil.format(entity.getCreateTime(),1));
				res.setMessageForm(messageForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}
			else{
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
			}

		}
		catch(Exception e){
			logger.info("获取消息详情时出错{}", e);
			res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
		}

		return res;
	}

}
