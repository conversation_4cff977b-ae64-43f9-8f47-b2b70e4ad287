package com.foshan.service;

import com.foshan.form.request.UserReq;
import com.foshan.form.response.user.AddUserRes;
import com.foshan.form.response.user.DeleteUserRes;
import com.foshan.form.response.user.GetUserInfoRes;
import com.foshan.form.response.user.GetUserListRes;
import com.foshan.form.response.user.ModifyUserRes;


public interface IUserService {
	public AddUserRes addUser(UserReq req);
	public GetUserInfoRes getUserInfo(UserReq req);
	public GetUserListRes getUserList(UserReq req);
	public DeleteUserRes deleteUser(UserReq req);
	public ModifyUserRes modifyUser(UserReq req);
}
