package com.foshan.service.impl;


import com.foshan.dao.generic.Page;
import com.foshan.entity.AccountEntity;
import com.foshan.entity.PointsPoolEntity;
import com.foshan.entity.UserEntity;
import com.foshan.form.PointsPoolForm;
import com.foshan.form.request.PointsPoolReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.pointsPool.AddPointsPoolRes;
import com.foshan.form.response.pointsPool.GetPointsPoolInfoRes;
import com.foshan.form.response.pointsPool.GetPointsPoolListRes;
import com.foshan.form.response.pointsPool.ModifyPointsPoolRes;
import com.foshan.service.IPointsPoolService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/17
 * @description 积分池服务实现类
 */

@Transactional
@Service("pointsPoolService")
public class PointsPoolServiceImpl extends GenericService implements IPointsPoolService {

    @Override
    public IResponse addPointsPool(PointsPoolReq req) {
        AddPointsPoolRes res = new AddPointsPoolRes();

        Object userObj = getPrincipal(false);
        if (userObj instanceof UserEntity) {
            if (StringUtils.isNotEmpty(req.getPoolName())) {
                PointsPoolEntity pointsPoolEntity = new PointsPoolEntity();
                pointsPoolEntity.setPoolName(req.getPoolName());
                pointsPoolEntity.setPoolComment(StringUtils.isNotEmpty(req.getPoolComment())? req.getPoolComment() : "");
                pointsPoolEntity.setCreateTime(new Timestamp(new Date().getTime()));
                pointsPoolDao.save(pointsPoolEntity);

                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_INFO);
            res.setRetInfo(ResponseContext.RES_PERM_ELEMENT_NULL_CODE);
        }
        return res;
    }

    @Override
    public IResponse modifyPointsPool(PointsPoolReq req) {
        ModifyPointsPoolRes res = new ModifyPointsPoolRes();

        Object userObj = getPrincipal(false);
        if (userObj instanceof UserEntity) {
            if (null != req.getPointsPoolId()) {
                PointsPoolEntity pointsPoolEntity = pointsPoolDao.get(req.getPointsPoolId());
                if (pointsPoolEntity != null) {
                    pointsPoolEntity.setPoolName(StringUtils.isNotEmpty(req.getPoolName()) ? req.getPoolName() : pointsPoolEntity.getPoolName());
                    pointsPoolEntity.setPoolComment(StringUtils.isNotEmpty(req.getPoolComment()) ? req.getPoolComment() : pointsPoolEntity.getPoolComment());
                    pointsPoolDao.update(pointsPoolEntity);

                    res.setRet(ResponseContext.RES_SUCCESS_CODE);
                    res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
                } else {
                    res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                    res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
                }
            } else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_INFO);
            res.setRetInfo(ResponseContext.RES_PERM_ELEMENT_NULL_CODE);
        }
        return res;
    }

    @Override
    public IResponse deletePointsPool(PointsPoolReq req) {
        GenericResponse res = new GenericResponse();

        Object userObj = getPrincipal(false);
        if (userObj instanceof UserEntity) {
            if (null != req.getPointsPoolId()) {
                pointsPoolDao.deleteById(req.getPointsPoolId());
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_INFO);
            res.setRetInfo(ResponseContext.RES_PERM_ELEMENT_NULL_CODE);
        }
        return res;
    }

    @Override
    public IResponse getPointsPoolInfo(PointsPoolReq req) {
        GetPointsPoolInfoRes res = new GetPointsPoolInfoRes();

        Object userObj = getPrincipal(false);
        if (userObj instanceof UserEntity) {
            if (null != req.getPointsPoolId()) {
                PointsPoolEntity pointsPoolEntity = pointsPoolDao.get(req.getPointsPoolId());
                if (null != pointsPoolEntity) {
                    PointsPoolForm pointsPoolForm = new PointsPoolForm();
                    pointsPoolForm.setPointsPoolId(pointsPoolEntity.getId());
                    pointsPoolForm.setPoolName(pointsPoolEntity.getPoolName());
                    pointsPoolForm.setPoolComment(pointsPoolEntity.getPoolComment());
                    pointsPoolEntity.setCreateTime(pointsPoolEntity.getCreateTime());
                    res.setPointsPoolForm(pointsPoolForm);
                    res.setRet(ResponseContext.RES_SUCCESS_CODE);
                    res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
                } else {
                    res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                    res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
                }
            } else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_INFO);
            res.setRetInfo(ResponseContext.RES_PERM_ELEMENT_NULL_CODE);
        }
        return res;
    }

    @Override
    public IResponse getPointsPoolList(PointsPoolReq req) {
        GetPointsPoolListRes res = new GetPointsPoolListRes();

        Object userObj = getPrincipal(false);
            //if (userObj instanceof UserEntity || userObj instanceof AccountEntity) {
            if (null != userObj) {
                Page<PointsPoolEntity> page = new Page<PointsPoolEntity>();
                page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
                page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
                page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

                StringBuilder hql = new StringBuilder("select a from PointsPoolEntity a where 1=1");

                if (StringUtils.isNotEmpty(req.getPoolName())) {
                    hql.append(" and a.poolName like " + "'%").append(req.getPoolName()).append("%'");
                }

                page = pointsPoolDao.queryPage(page, hql.toString());
                res.setTotalResult(page.getTotalCount());
                res.setPageSize(page.getPageSize());
                res.setCurrentPage(page.getCurrentPage());
                res.setTotal(page.getTotalPage());

                page.getResultList().forEach(o -> res.getPointsPoolList().add(new PointsPoolForm(o.getId(), o.getPoolName(), o.getPoolComment())));
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            }else {
                res.setRet(ResponseContext.RES_PERM_ELEMENT_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
            }
        return res;
    }
}
