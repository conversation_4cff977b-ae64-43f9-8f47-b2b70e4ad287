package com.foshan.service.impl;

import static java.util.Comparator.comparingInt;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.dao.IAccountDao;
import com.foshan.dao.IAccountExchangeRealDao;
import com.foshan.dao.IAccountPointsDao;
import com.foshan.dao.IAddressDao;
import com.foshan.dao.IArticleDao;
import com.foshan.dao.IAssetDao;
import com.foshan.dao.IAssetInteractionDao;
import com.foshan.dao.IAssetSpecDao;
import com.foshan.dao.IColumnDao;
import com.foshan.dao.IDepartmentDao;
import com.foshan.dao.IDictionaryDao;
import com.foshan.dao.IDictionaryDataDao;
import com.foshan.dao.IGiftRollDao;
import com.foshan.dao.IGiftRollModelDao;
import com.foshan.dao.IJobDao;
import com.foshan.dao.IJobGroupDao;
import com.foshan.dao.IMenuDao;
import com.foshan.dao.IPermissionDao;
import com.foshan.dao.IPlatformUserDao;
import com.foshan.dao.IPluginDao;
import com.foshan.dao.IPointsBehaviorDao;
import com.foshan.dao.IPointsLevelDao;
import com.foshan.dao.IPointsMemberAccountDao;
import com.foshan.dao.IPointsPlanDao;
import com.foshan.dao.IPointsPoolDao;
import com.foshan.dao.IPointsPrizeDao;
import com.foshan.dao.IRealPointsDao;
import com.foshan.dao.IRegionDao;
import com.foshan.dao.IRoleDao;
import com.foshan.dao.IServiceDao;
import com.foshan.dao.IServiceSmartcardDao;
import com.foshan.dao.IServiceVerificationCodeDao;
import com.foshan.dao.IUpshelfColumnDao;
import com.foshan.dao.IUrlDao;
import com.foshan.dao.IUserDao;
import com.foshan.dao.IVirtualPointsCodeDao;
import com.foshan.dao.IVisitDayDao;
import com.foshan.dao.IVisitHistoryDao;
import com.foshan.dao.IVisitMonthDao;
import com.foshan.dao.IVisitRealDao;
import com.foshan.dao.IVisitYearDao;
import com.foshan.dao.IWhiteListDao;
import com.foshan.dao.IWxDepartmentDao;
import com.foshan.dao.IWxParameterDao;
import com.foshan.dao.IWxServiceAccountDao;
import com.foshan.dao.IWxServiceDao;
import com.foshan.dao.IMessageDao;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.ColumnEntity;
import com.foshan.entity.DepartmentEntity;
import com.foshan.entity.PointsLevelEntity;
import com.foshan.entity.RegionEntity;
import com.foshan.entity.RoleEntity;
import com.foshan.entity.ServiceEntity;
import com.foshan.entity.UserEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.AssetForm;
import com.foshan.form.ColumnForm;
import com.foshan.form.PointsLevelForm;
import com.foshan.form.ServiceForm;
import com.foshan.model.permssion.PrincipalModel;
import com.foshan.util.ContextInfo;
import com.foshan.util.DateUtil;
import com.foshan.util.PlatformPoint;

public class GenericService {
	protected ObjectMapper mapper = new ObjectMapper();
	@Autowired
	protected ContextInfo contextInfo;
	@Resource(name = "columnDao")
	public IColumnDao columnDao;
	@Resource(name = "regionDao")
	public IRegionDao regionDao;
	@Resource(name = "visitRealDao")
	public IVisitRealDao visitRealDao;
	@Resource(name = "visitHistoryDao")
	public IVisitHistoryDao visitHistoryDao;
	@Resource(name = "assetDao")
	public IAssetDao assetDao;
	@Resource(name = "assetSpecDao")
	public IAssetSpecDao assetSpecDao;
	@Resource(name = "serviceDao")
	public IServiceDao serviceDao;
	@Resource(name = "accountDao")
	public IAccountDao accountDao;
	@Resource(name = "jobDao")
	public IJobDao jobDao;
	@Resource(name = "jobGroupDao")
	public IJobGroupDao jobGroupDao;
	@Resource(name = "visitDayDao")
	public IVisitDayDao visitDayDao;
	@Resource(name = "visitMonthDao")
	public IVisitMonthDao visitMonthDao;
	@Resource(name = "visitYearDao")
	public IVisitYearDao visitYearDao;
	@Resource(name = "realPointsDao")
	public IRealPointsDao realPointsDao;
	@Resource(name = "accountPointsDao")
	public IAccountPointsDao accountPointsDao;
	@Resource(name = "pointsPrizeDao")
	public IPointsPrizeDao pointsPrizeDao;
	@Resource(name = "pointsLevelDao")
	public IPointsLevelDao pointsLevelDao;
	@Resource(name = "pointsPlanDao")
	public IPointsPlanDao pointsPlanDao;
	@Resource(name = "accountExchangeRealDao")
	public IAccountExchangeRealDao accountExchangeRealDao;
	@Resource(name = "virtualPointsCodeDao")
	public IVirtualPointsCodeDao virtualPointsCodeDao;
	@Resource(name = "userDao")
	public IUserDao userDao;
	@Resource(name = "roleDao")
	public IRoleDao roleDao;
	@Resource(name = "permissionDao")
	public IPermissionDao permissionDao;
	@Resource(name = "addressDao")
	public IAddressDao addressDao;
	@Resource(name = "pluginDao")
	public IPluginDao pluginDao;
	@Resource(name = "platformUserDao")
	public IPlatformUserDao platformUserDao;
	@Resource(name = "giftRollModelDao")
	public IGiftRollModelDao giftRollModelDao;
	@Resource(name = "giftRollDao")
	public IGiftRollDao giftRollDao;
	@Resource(name = "whiteListDao")
	public IWhiteListDao whiteListDao;
	@Resource(name = "upshelfColumnDao")
	public IUpshelfColumnDao upshelfColumnDao;
	@Resource(name = "urlDao")
	public IUrlDao urlDao;
	@Resource(name = "articleDao")
	public IArticleDao articleDao;
	@Resource(name = "wxDepartmentDao")
	public IWxDepartmentDao wxDepartmentDao;
	@Resource(name = "assetInteractionDao")
	public IAssetInteractionDao assetInteractionDao;
    @Resource(name = "serviceVerificationCodeDao")
    protected IServiceVerificationCodeDao serviceVerificationCodeDao;
    @Resource(name = "serviceSmartcardDao")
    protected IServiceSmartcardDao serviceSmartcardDao;
    @Resource(name = "dictionaryDao")
    protected IDictionaryDao dictionaryDao;
    @Resource(name = "dictionaryDataDao")
    protected IDictionaryDataDao dictionaryDataDao;
    @Resource(name = "departmentDao")
    protected IDepartmentDao departmentDao;
    @Resource(name = "menuDao")
    protected IMenuDao menuDao;
    @Resource(name = "wxParameterDao")
    protected IWxParameterDao wxParameterDao;
    @Resource(name = "pointsMemberAccountDao")
    protected IPointsMemberAccountDao pointsMemberAccountDao;
    @Resource(name = "pointsPoolDao")
    protected IPointsPoolDao pointsPoolDao;
    @Resource(name = "pointsBehaviorDao")
    protected IPointsBehaviorDao pointsBehaviorDao;
    @Resource(name = "wxServiceAccountDao")
    protected IWxServiceAccountDao wxServiceAccountDao;
    @Resource(name = "wxServiceDao")
    protected IWxServiceDao wxServiceDao;
	@Resource(name = "messageDao")
	protected IMessageDao messageDao;
	 

	@SuppressWarnings("unchecked")
	protected HashMap<String, Object> serviceList(Set<ServiceEntity> serviceSet, Integer parentServiceId, Integer depth,
			Integer visitFlag, String startDate, String endDate) {
		HashMap<String, Object> serviceList = new HashMap<String, Object>();
		Integer serviceVisits = 0;
		List<ServiceForm> serviceFormList = new ArrayList<ServiceForm>();
		depth--;
		for (ServiceEntity o : serviceSet) {
			Integer serviceVisit = 0;
			ServiceForm serviceForm = new ServiceForm();
			serviceForm.setServiceId(o.getId());
			serviceForm.setServiceName(o.getServiceName());
			serviceForm.setServiceGroup(o.getServiceGroup());
			serviceForm.setServiceCode(o.getServiceCode());
			serviceForm.setCreateTime(DateUtil.formatLongFormat(o.getCreateTime()));
			serviceForm.setServiceState(o.getServiceState());
			serviceForm.setParentServiceId(parentServiceId);
			serviceForm.setOttUrl(o.getOttUrl());
			serviceForm.setStbUrl(o.getStbUrl());
			// 逐级递归判断业务下是否存在子业务，并且根据设置的查询深度进行数据抓取
			Set<ServiceEntity> serviceSet1 = o.getSubServiceSet();

			if (serviceSet1.size() > 0 && depth >= 1) {
				serviceForm.getSubServiceList().addAll(
						(List<ServiceForm>) serviceList(serviceSet1, o.getId(), depth, visitFlag, startDate, endDate)
								.get("serviceList"));
			}

			// 如果该业务不属于顶层业务，则需要根据业务主入口栏目递归逐级查找该栏目下的子栏目、投票分组、问卷情况
			ColumnEntity column = o.getColumn();
			if (null != column) {
				serviceForm.setServiceColumnId(column.getId());
				// 逐级递归查找子栏目
				Set<ColumnEntity> columnSet = column.getSubColumnList();

				if (columnSet.size() > 0 && depth >= 1) {
					serviceForm.getSubColumnList().addAll((List<ColumnForm>) columnList(columnSet, o.getId(),
							column.getId(), depth, visitFlag, startDate, endDate).get("columnList"));
				}

				if (visitFlag == EntityContext.COUNT_VISIT_ON) {
					serviceVisit += (Integer) columnList(columnSet, parentServiceId, column.getId(), depth, visitFlag,
							startDate, endDate).get("serviceVisits");
					serviceVisit += getColumnVisit(parentServiceId, column.getId(), startDate, endDate);
				}
			}
			serviceFormList.add(serviceForm);
			// 根据访问量统计开关统计当前栏目下的分组访问量，且访问量统计与查询深度无关
			if (visitFlag == EntityContext.COUNT_VISIT_ON) {
				serviceVisit += (Integer) serviceList(serviceSet1, o.getId(), depth, visitFlag, startDate, endDate)
						.get("serviceVisits");
				serviceForm.setTotalVisits(serviceVisit);
				serviceVisits += serviceVisit;
			}

		}
		serviceFormList.sort(comparingInt(ServiceForm::getServiceId));
		serviceList.put("serviceList", serviceFormList);
		serviceList.put("serviceVisits", serviceVisits);
		return serviceList;
	}

	@SuppressWarnings("unchecked")
	protected HashMap<String, Object> columnList(Set<ColumnEntity> columnSet, Integer serviceId, Integer columnId,
			Integer depth, Integer visitFlag, String startDate, String endDate) {
		HashMap<String, Object> columnList = new HashMap<String, Object>();
		Integer serviceVisits = 0;
		List<ColumnForm> columnFormList = new ArrayList<ColumnForm>();
		depth--;
		for (ColumnEntity o : columnSet) {
			ColumnForm columnForm = new ColumnForm();
			Set<ColumnEntity> columnSet1 = o.getSubColumnList();
			if (o.getColumnState() == EntityContext.RECORD_STATE_VALID) {
				columnForm.setColumnId(o.getId());
				columnForm.setColumnName(o.getColumnName());
				columnForm.setServiceId(serviceId);
				columnForm.setColumnCode(o.getColumnCode());
				columnForm.setColumnInfo(o.getColumnInfo());
				columnForm.setColumnType(o.getColumnType());
				columnForm.setColumnImage(getAsset(o.getColumnImage()));
				columnForm.setColumnPhoneImage(getAsset(o.getColumnPhoneImage()));
				columnForm.setColumnState(o.getColumnState());
				columnForm.setColumnLevel(o.getColumnLevel());
				columnForm.setParentColumnId(columnId);
				// 查询子栏目
				if (columnSet1.size() > 0 && depth >= 1) {
					columnForm.getSubColumnList().addAll((List<ColumnForm>) columnList(columnSet1, serviceId, o.getId(),
							depth, visitFlag, startDate, endDate).get("columnList"));
				}

				columnFormList.add(columnForm);
			}

			// 根据访问量统计开关统计当前栏目下的分组访问量，且访问量统计与查询深度无关
			if (visitFlag == EntityContext.COUNT_VISIT_ON) {
				columnForm.setTotalVisits(getColumnVisit(serviceId, o.getId(), startDate, endDate));
			}
		}
		columnFormList.sort(comparingInt(ColumnForm::getColumnId));
		columnList.put("columnList", columnFormList);
		columnList.put("serviceVisits", serviceVisits);
		return columnList;
	}

	protected Integer getColumnVisit(Integer serviceId, Integer columnId, String startDate, String endDate) {
		Long result = 0L;
		if (StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate)) {
			result = (Long) visitDayDao.createQuery(
					"select sum(a.visitNum) from VisitDayEntity a where a.serviceId=? and a.columnId=? and a.visitDate>=? and a.visitDate<=?")
					.setParameter(0, serviceId).setParameter(1, columnId).setParameter(2, startDate)
					.setParameter(3, endDate).uniqueResult();
		} else {
			result = (Long) visitYearDao
					.createQuery("select sum(a.visitNum) from VisitYearEntity a where serviceId=? and a.columnId=?")
					.setParameter(0, serviceId).setParameter(1, columnId).uniqueResult();
		}
		return null != result ? result.intValue() : 0;
	}

	@SuppressWarnings("unchecked")
	protected Integer getServiceVisit(Integer serviceId, String startDate, String endDate) {
		Long result = 0L;
		Long temp = 0L;
		List<Integer> subServiceIdList = (List<Integer>) serviceDao
				.createQuery("select a.id from ServiceEntity a inner join a.parentService b where b.id=?", serviceId)
				.list();
		if (subServiceIdList.size() > 0) {
			for (Integer subServiceId : subServiceIdList) {
				result += getServiceVisit(subServiceId, startDate, endDate);
			}
		}
		if (StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate)) {
			temp = (Long) visitDayDao.createQuery(
					"select sum(a.visitNum) from VisitDayEntity a where a.serviceId=? and a.visitDate>=? and a.visitDate<=?",
					serviceId, startDate, endDate).uniqueResult();
			result += (null != temp ? temp : 0);
		} else {
			temp = (Long) visitYearDao
					.createQuery("select sum(a.visitNum) from VisitYearEntity a where a.serviceId=?", serviceId)
					.uniqueResult();
			result += (null != temp ? temp : 0);
		}

		return result.intValue();
	}

	protected AssetForm getAsset(Integer assetId) {
		return getAsset(assetDao.get(assetId));
	}
	
	protected AssetForm getAsset(AssetEntity asset) {
		return getAsset(asset, EntityContext.PRODUCT_LIST_DETAIL_FLAG_ALL);
	}

	protected AssetForm getAsset(AssetEntity asset, Integer needDetail) {
		
		AssetForm assetForm = new AssetForm();

		if (null != asset) {
			assetForm.setAssetId(asset.getId());
			assetForm.setAssetType(asset.getAssetType());
			assetForm.setImageFile(asset.getImageFile());
			assetForm.setSmallImageFile(asset.getSmallImageFile());
			assetForm.setIsCover(asset.getIsCover());
			assetForm.setPackageFlag(asset.getPackageFlag());
			assetForm.setTimeLength(asset.getTimeLength());
			assetForm.setSummaryShort(asset.getSummaryShort());
			assetForm.setParameterInfo(asset.getParameterInfo());
			assetForm.setAssetCode(asset.getAssetCode());
			assetForm.setAssetName(asset.getAssetName());
			assetForm.setAssetOrders(asset.getAssetOrders());
			assetForm.setPublishedTime(
					asset.getPublishedTime() != null ? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(asset.getPublishedTime())
							: "");
			assetForm.setPackageOrders(asset.getPackageCount());
			assetForm.setServiceCode(asset.getAssetCode());
			assetForm.setValidTime(asset.getValidTime() != null
					? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(asset.getValidTime())
					: "");
//			assetForm.setFolderAssetId(asset.getFolderAssetId());
			assetForm.setServiceId(asset.getServiceId());
			assetForm.setMiddleImageFile(asset.getMiddleImageFile());
			assetForm.setRecommendCount(asset.getRecommendCount());
			assetForm.setDirector(asset.getDirector());
			assetForm.setActorsDisplay(asset.getActorsDisplay());
			assetForm.setBroadcastCount(asset.getBroadcastCount());
			assetForm.setPreviewAssetId(asset.getPreviewAssetId());
			assetForm.setPreviewProviderId(asset.getPreviewProviderId());
			if (needDetail == EntityContext.PRODUCT_LIST_DETAIL_FLAG_ALL) {
				assetForm.setPackageCount(asset.getSubAssetSet().size());
				asset.getSubAssetSet().forEach(o -> {
					AssetForm subForm = new AssetForm();
					subForm.setAssetId(o.getId());
					subForm.setAssetType(o.getAssetType());
					subForm.setImageFile(o.getImageFile());
					subForm.setSmallImageFile(o.getSmallImageFile());
					subForm.setIsCover(o.getIsCover());
					subForm.setTimeLength(o.getTimeLength());
					subForm.setSummaryShort(o.getSummaryShort());
					subForm.setParameterInfo(o.getParameterInfo());
					subForm.setAssetCode(o.getAssetCode());
					subForm.setAssetName(o.getAssetName());
					subForm.setAssetOrders(o.getAssetOrders());
//					subForm.setFolderAssetId(o.getFolderAssetId());
					subForm.setServiceId(o.getServiceId());
					subForm.setDirector(o.getDirector());
					subForm.setActorsDisplay(o.getActorsDisplay());
					subForm.setBroadcastCount(o.getBroadcastCount());
					subForm.setPreviewAssetId(o.getPreviewAssetId());
					subForm.setPreviewProviderId(o.getPreviewProviderId());
					subForm.setPublishedTime(asset.getPublishedTime() != null
							? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(asset.getPublishedTime())
							: "");
					subForm.setRecommendCount(o.getRecommendCount());
					subForm.setPackageCount(o.getPackageCount());
					subForm.setPackageOrders(o.getPackageCount());
					subForm.setServiceCode(o.getAssetCode());
					subForm.setValidTime(asset.getValidTime() != null
							? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(asset.getValidTime())
							: "");
					subForm.setMiddleImageFile(o.getMiddleImageFile());
					assetForm.getSubAssetList().add(subForm);
				});
			}
		}
		
		
		return assetForm;
	}
	
	/**
	 *  查询当前登录用户的实体
	 * @param returnFullEntity 是否返回完整entity，true返回完整的entity，false返回shire中存的entity。shiro缓存了id、电话、微信openid，设置为false时，只有这三个数据。
	 * @return PlatformUserEntity或AccountEntity的object，使用的时候进行强转 用户没登录或rememberme
	 *         cookie过期则返回null
	 *         
	 *         ******** modify by Genie,直接返回principal,会话状态失效者返回null。 
	 */
	protected Object getPrincipal(boolean returnFullEntity){
		Subject curUser = SecurityUtils.getSubject();
		PrincipalCollection principals = curUser.getPrincipals();
		if(null!=principals&&!principals.isEmpty()){
			@SuppressWarnings("unchecked")
			List<Object> principalList = principals.asList();
			return principalList.get(1);
		}else{
			return null;
		}
		
	}
	
	/**
	 * 获取当前登录的管理员用户
	 * @return
	 */
	protected UserEntity getCurrentUser() {
		UserEntity result = null;
		Object principal = getPrincipal(true);
		if(principal == null) {
			return result;
		}
		if(principal instanceof PrincipalModel) {
			PrincipalModel userPrincipalModel = (PrincipalModel) principal;
			result = userDao.get(userPrincipalModel.getId());
		}
		return result;
	}
	
	public String parsePointsLevel(Integer totalPoints) {
		String levelName = "";
		for (Object o : PlatformPoint.levelList) {
			PointsLevelForm level = (PointsLevelForm) o;
			if ((level.getMinPoints() <= totalPoints && null != level.getMaxPoints()
					&& level.getMaxPoints() > totalPoints)
					|| (level.getMinPoints() <= totalPoints && null == level.getMaxPoints())) {
				levelName = level.getLevelName();
				break;
			}
		}

		return levelName;
	}
	
	
	public String parsePointsLevel(Integer totalPoints,Integer pointsPoolId) {
		String levelName = "";
		List<PointsLevelEntity> pointsLevelList =
				pointsLevelDao.getListByHql("select a from PointsLevelEntity a where a.pointsPool.id = " + pointsPoolId);
		for (PointsLevelEntity level : pointsLevelList) {
			if ((level.getMinPoints() <= totalPoints && null != level.getMaxPoints()
					&& level.getMaxPoints() > totalPoints)
					|| (level.getMinPoints() <= totalPoints && null == level.getMaxPoints())) {
				levelName = level.getLevelName();
				break;
			}
		}

		return levelName;
	}
	/**
	 * 根据角色列表获取所有子角色的roleId
	 * @param curRoles 
	 * @return
	 */
	public List<Integer> getAllSubRoles(List<RoleEntity> curRoles) {
		List<Integer> roleIds = new ArrayList<Integer>();
		if (curRoles.size() > 0) {
			curRoles.forEach(o -> {
				roleIds.add(o.getId());
				roleIds.addAll(getAllSubRoles(o.getSubRoleList()));
			});
		}
		return roleIds;
	}
	
	/**
	 * 根据区域列表获取所有子区域id
	 * 
	 * @param curRegions
	 * @return
	 */
	public List<Integer> getAllSubRegions(List<RegionEntity> curRegions) {
		List<Integer> regionIds = new ArrayList<Integer>();
		if (curRegions.size() > 0) {
			curRegions.forEach(o -> {
				regionIds.add(o.getId());
				regionIds.addAll(getAllSubRegions(o.getSubRegionList().stream().collect(Collectors.toList())));
			});
		}
		return regionIds;
	}

	/**
	 * 根据DepartmentCode 查询部门信息
	 * 
	 * @param deptCode 部门编号
	 * @return
	 */
	protected DepartmentEntity getDepartmentByCode(String deptCode) {
		return departmentDao.getUniqueByHql(
				"select d from DepartmentEntity d where d.state = 1 and d.departmentCode = '" + deptCode + "'");
	}
	
	
}
