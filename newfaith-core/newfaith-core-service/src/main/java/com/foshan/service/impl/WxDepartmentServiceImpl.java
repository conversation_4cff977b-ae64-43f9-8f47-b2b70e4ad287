package com.foshan.service.impl;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.entity.AccountEntity;
import com.foshan.entity.WxDepartmentEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.IWeiXinApiService;
import com.foshan.service.IWxDepartmentService;
import com.foshan.util.SpringHandler;
import com.foshan.util.WeiXinApiUtil;

@Transactional
@Service("wxDepartmentService")
public class WxDepartmentServiceImpl extends GenericService implements IWxDepartmentService {
	
	@SuppressWarnings("unchecked")
	public IResponse synchronousDepartment() {
		GenericResponse res = new GenericResponse();
		try {
			List<AccountEntity> memberList = accountDao
					.getListByHql("select a from AccountEntity a where a.wxEnterpriceMiniProgramUserid IS NOT NULL", "");
			//String token = WeiXinApiUtil.getAccessToken(2);
			IWeiXinApiService weiXinApiService =  (IWeiXinApiService) SpringHandler.getBean("weiXinApiService");
		    String token =  weiXinApiService.getAccessToken(2);
			@SuppressWarnings("rawtypes")
			Map<String, Map> map = getDepartmentName(token);
			Map<String, String> nameMap = map.get("depName");
			Map<String, WxDepartmentEntity> depMap = map.get("department");
			
			memberList.forEach(o -> {
				String jsonStr = WeiXinApiUtil.getCompanyWeChat(WeiXinApiUtil.COMPANY_WEIXIN_APIURL_GETUSERINFO,
						"?access_token=" + token + "&userid=" + o.getWxEnterpriceMiniProgramUserid());
				if (StringUtils.isNotEmpty(jsonStr)) {
					JsonNode node = null;
					try {
						node = new ObjectMapper().readTree(jsonStr);
						Iterator<String> keys = node.fieldNames();
						while (keys.hasNext()) {
							String key = keys.next();
							if (key.equals("department")) {
								JsonNode arryNode = (JsonNode) node.get("department");
								String departmentId = "";
								for (JsonNode n : arryNode) {
									departmentId = n.asText();
									break;
								}
								if (StringUtils.isNotEmpty(departmentId)) {
									String depName = getName(nameMap, departmentId);
									o.setWxEnterpriceDepartmentName(depName);
									o.setParentWxDepartmentEntity(depMap.get(departmentId));
								}
							}
						}
					} catch (IOException e) {
						e.printStackTrace();
					}

				}
			});

		} catch (Exception e) {
			e.printStackTrace();
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	public String getName(Map<String, String> map, String id) {
		String name = "";
		String[] n = map.get(id).split("_");
		String i = n[1];
		if (!i.equals("0")) {
			name = getName(map, i) + "/" + n[0];
		} else {
			name = n[0];
		}
		return name;
	}

	@SuppressWarnings("rawtypes")
	public Map<String, Map> getDepartmentName(String token) {
		String jsonStr = WeiXinApiUtil.getCompanyWeChat(WeiXinApiUtil.COMPANY_WEIXIN_APIURL_DEPARTMENT,
				"?access_token=" + token + "&id=");
		Map<String, Map> map = new HashMap<String, Map>();
		Map<String, String> nameMap = new HashMap<String, String>();
		Map<String, WxDepartmentEntity> depMap = new HashMap<String, WxDepartmentEntity>();
		wxDepartmentDao.executeUpdate("update WxDepartmentEntity a set a.state='"
				+ EntityContext.RECORD_STATE_INVALID +"'");
		try {
			for(int i=0;i<2;i++) {//写入两次，主要是因为数组里的有些子部门出现在父部门之前，写入子部门时，查不到父部门，所以防止他们关系没有正确绑定
				JsonNode node = new ObjectMapper().readTree(jsonStr);
				Iterator<String> keys = node.fieldNames();
				while (keys.hasNext()) {
					String key = keys.next();
					if (key.equals("department")) {
						String userList = node.get(key).toString();
						JsonNode subNode = new ObjectMapper().readTree(userList);
						for (JsonNode s : subNode) {
							// Map<String,String> map = new HashMap<String,String>();
							String id = s.get("id").asText();
							WxDepartmentEntity department = wxDepartmentDao.getUniqueByNProperty("dtId",Integer.valueOf(id));
							if(null == department) {
								department = new WxDepartmentEntity();
							}
							String name = s.get("name").asText();
							String parentId = s.get("parentid").asText();
							String order = s.get("order").asText();
							department.setDtId(Integer.valueOf(id));
							department.setName(name);
							department.setOrders(Long.valueOf(order));
							department.setState(EntityContext.RECORD_STATE_VALID);
							department.setLastModifyTime(new Timestamp(new Date().getTime()));
							WxDepartmentEntity parentDepartment = wxDepartmentDao.getUniqueByNProperty("dtId",Integer.valueOf(parentId));
							department.setParentWxDepartmentEntity(parentDepartment);
							department.setDepartmentPath(null!=parentDepartment ? parentDepartment.getDepartmentPath()+"/"+name : name);
							wxDepartmentDao.saveOrUpdate(department);
							depMap.put(id, department);
							nameMap.put(id, name + "_" + parentId);
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		map.put("depName", nameMap);
		map.put("department", depMap);
		return map;
	}
}
