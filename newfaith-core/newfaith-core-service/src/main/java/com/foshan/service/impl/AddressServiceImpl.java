package com.foshan.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.entity.AddressEntity;
import com.foshan.entity.RegionEntity;
import com.foshan.form.PlatAddressForm;
import com.foshan.form.request.GetAddressListReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.address.GetAddressListByParentIdListRes;
import com.foshan.form.response.address.GetAddressListRes;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.IAddressService;

@Transactional
@Service("addressService")
public class AddressServiceImpl extends GenericService implements IAddressService {

	@Override
	public IResponse getAddressList() {
		// TODO Auto-generated method stub
		GetAddressListRes res = new GetAddressListRes();
		StringBuilder hql = new StringBuilder("select a from AddressEntity a where a.parentAddress is not null");
		List<AddressEntity> addressList = addressDao.getListByHql(hql.toString());
		res.setAddress(parseAddress(addressList));
		return res;
	}
	
	@Override
	public IResponse getAddressList(GetAddressListReq req) {
		GetAddressListByParentIdListRes res = new GetAddressListByParentIdListRes();
		StringBuilder hql = new StringBuilder("select a from AddressEntity a");
		Map<String,Object> param = new HashMap<String,Object>();

		Integer level = req.getLevel();
		String nodeName = req.getNodeName();
		Integer parentId = req.getParentId();
		if(parentId!=null) {
			hql.append(" inner join a.parentAddress p");
			hql.append(" where p.id = :parentId");
			param.put("parentId", parentId);
		}
		if(level!=null) {
			hql.append(hql.toString().contains("where") ? " and a.level = :level"  :" where a.level = :level");
			param.put("level", level);
		}
		if(StringUtils.isNotEmpty(nodeName)) {
			hql.append(hql.toString().contains("where") ? " and a.location like :nodeName" : " where a.location like :nodeName");
			param.put("nodeName", "%" + nodeName + "%");
		}
       
		List<AddressEntity> addressList = addressDao.query(hql.toString(),param);
		List<PlatAddressForm> addressFormList = new ArrayList<PlatAddressForm>();
		for(AddressEntity address : addressList) {
			PlatAddressForm form =new PlatAddressForm();
			form.setAddressId(address.getId());
			form.setLevel(address.getLevel());
			if(address.getParentAddress() != null) {
				form.setParentId(parentId);
			}
			if(1 == address.getLevel() ) {
				form.setNodeName(address.getProvince());;
			}
			else if(2 ==address.getLevel() ) {
				form.setNodeName(address.getCity());;
			}
			else if(3 == address.getLevel()) {
				form.setNodeName(address.getDistrict());;
			}
			else if(4 == address.getLevel()) {
				form.setNodeName(address.getTowns());;
			}
			form.setLocation(address.getLocation());
			
			RegionEntity region =  regionDao.get(address.getRegionId());
			if(region != null) {
    			form.setRegionId(region.getId());
    			form.setRegionCode(region.getRegionCode());
    			form.setStartRegionCode(region.getStartRegionCode());
    			form.setEndRegionCode(region.getEndRegionCode());
    		}
			
			addressFormList.add(form);
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		res.setAddressList(addressFormList);
		return res;
	}

	public PlatAddressForm parseAddress(List<AddressEntity> addressList) {

		PlatAddressForm countryForm = new PlatAddressForm();
		countryForm.setNodeName("中国");
		HashMap<String, List<AddressEntity>> provinceMap = (HashMap<String, List<AddressEntity>>) addressList.stream()
				.collect(Collectors.groupingBy(AddressEntity::getProvince));
		for (Iterator<String> provinceItr = provinceMap.keySet().iterator(); provinceItr.hasNext();) {
			PlatAddressForm provinceForm = new PlatAddressForm();
			String province = provinceItr.next();
			provinceForm.setNodeName(province);
			List<AddressEntity> provinceList = provinceMap.get(province);
			HashMap<String, List<AddressEntity>> cityMap = (HashMap<String, List<AddressEntity>>) provinceList.stream()
					.collect(Collectors.groupingBy(AddressEntity::getCity));
			for (Iterator<String> cityItr = cityMap.keySet().iterator(); cityItr.hasNext();) {
				PlatAddressForm cityForm = new PlatAddressForm();
				String city = cityItr.next();
				cityForm.setNodeName(city);
				List<AddressEntity> cityList = cityMap.get(city);
				HashMap<String, List<AddressEntity>> districtMap = (HashMap<String, List<AddressEntity>>) cityList
						.stream().collect(Collectors.groupingBy(AddressEntity::getDistrict));
				for (Iterator<String> districtItr = districtMap.keySet().iterator(); districtItr.hasNext();) {
					PlatAddressForm districtForm = new PlatAddressForm();
					String district = districtItr.next();
					districtForm.setNodeName(district);
					List<AddressEntity> townsList = districtMap.get(district);
					if (townsList.size() == 1 && StringUtils.isEmpty(townsList.iterator().next().getTowns())) {
						AddressEntity towns = townsList.iterator().next();
						districtForm.setAddressId(towns.getId());
						districtForm.setNodeName(towns.getDistrict());
						districtForm.setLocation(towns.getLocation());
						districtForm.setRegionId(towns.getRegionId());
					} else if (townsList.size() > 1 || (townsList.size() == 1
							&& StringUtils.isNotEmpty(townsList.iterator().next().getTowns()))) {
						HashMap<String, List<AddressEntity>> townsMap = (HashMap<String, List<AddressEntity>>) townsList
								.stream().collect(Collectors.groupingBy(AddressEntity::getTowns));
						for (Iterator<String> townItr = townsMap.keySet().iterator(); townItr.hasNext();) {
							PlatAddressForm townsForm = new PlatAddressForm();
							AddressEntity towns = townsMap.get(townItr.next()).iterator().next();
							townsForm.setAddressId(towns.getId());
							townsForm.setNodeName(towns.getTowns());
							townsForm.setLocation(towns.getLocation());
							townsForm.setRegionId(towns.getRegionId());
							districtForm.getChildren().add(townsForm);
						}
					}
					cityForm.getChildren().add(districtForm);
				}
				provinceForm.getChildren().add(cityForm);
			}
			countryForm.getChildren().add(provinceForm);
		}

		return countryForm;
	}

}
