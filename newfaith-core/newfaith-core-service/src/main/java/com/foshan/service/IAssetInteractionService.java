package com.foshan.service;

import com.foshan.form.request.AssetInteractionReq;
import com.foshan.form.response.IResponse;

public interface IAssetInteractionService {
	public IResponse getCommentListByAssetId(AssetInteractionReq req);
	public IResponse getCommentById(AssetInteractionReq req);
	public IResponse recommendAsset(AssetInteractionReq req);

	IResponse tvRecommendAsset(AssetInteractionReq req);

	public IResponse recommendComment(AssetInteractionReq req);
	public IResponse checkin(AssetInteractionReq req);
	public IResponse addAssetComment(AssetInteractionReq req);
	public IResponse getCommentListByMemberId(AssetInteractionReq req);
	public IResponse getPlayHistorytListByMemberId(AssetInteractionReq req);
	public IResponse addPlayRecord(AssetInteractionReq req);

    IResponse recommendAssetWithLimit(AssetInteractionReq req);
}
