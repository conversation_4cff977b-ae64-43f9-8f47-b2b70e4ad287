package com.foshan.service;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.multipart.MultipartFile;

import com.foshan.form.response.IResponse;
import com.foshan.form.request.FileShardReq;
import com.foshan.form.request.UploadReq;

public interface IUploadService {
	/**
	 * 纯粹的上传文件，返回相应文件的url列表
	 */
	//public IResponse uploadFile(HttpServletRequest request,UploadReq req) throws IllegalStateException, IOException;
	public IResponse uploadFile(HttpServletRequest request,String realUrl ,String virtualUrl, boolean saveState,Integer assetType,String fileSuffix) throws IllegalStateException, IOException;
	public IResponse uploadFile(HttpServletRequest request,MultipartFile[] file,UploadReq req);
	public IResponse deleteImage(UploadReq req);
	public IResponse uploadFile(MultipartFile[] multipartFile,HttpServletRequest request,UploadReq req) throws IllegalStateException, IOException;
	public IResponse uploadShard(FileShardReq req);
}
