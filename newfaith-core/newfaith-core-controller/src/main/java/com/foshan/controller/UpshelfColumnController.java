package com.foshan.controller;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.request.UpshelfColumnReq;
import com.foshan.form.response.upshelfColumn.AddUpshelfColumnRes;
import com.foshan.form.response.upshelfColumn.DeleteUpshelfColumnRes;
import com.foshan.form.response.upshelfColumn.GetUpshelfColumnInfoRes;
import com.foshan.form.response.upshelfColumn.GetUpshelfColumnListRes;
import com.foshan.form.response.upshelfColumn.ModifyUpshelfColumnRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "融合平台--栏目上架模块")
@RestController
public class UpshelfColumnController extends BaseController{

	// 获取产品类别列表
	@ApiOperation(value = "获取栏目上架列表(GetUpshelfColumnList)", httpMethod = "POST", notes = "获取栏目上架列表")
	@ResponseBody
	@RequestMapping(value = "/getUpshelfColumnList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetUpshelfColumnListRes getUpshelfColumnList(@RequestBody UpshelfColumnReq req, HttpServletRequest request) throws JsonProcessingException {

		GetUpshelfColumnListRes res = (GetUpshelfColumnListRes) upshelfColumnService.getUpshelfColumnList(req);
		return res;
	}
	
	// 获取产品信息
	@ApiOperation(value = "获取栏目上架详情信息(GetUpshelfColumnInfo)", httpMethod = "POST", notes = "获取栏目上架详情信息(<p>1：specificationId和productId不能同时为空；")
	@ResponseBody
	@RequestMapping(value = "/getUpshelfColumnInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetUpshelfColumnInfoRes getUpshelfColumnInfo(@RequestBody UpshelfColumnReq req, HttpServletRequest request) throws JsonProcessingException {

		GetUpshelfColumnInfoRes res = (GetUpshelfColumnInfoRes) upshelfColumnService.getUpshelfColumnInfo(req);
		return res;
	}
	
	// 修改栏目上架媒资
	@ApiOperation(value = "修改栏目上架媒资(modifyUpshelfColumn)", httpMethod = "POST", notes = "修改栏目上架媒资(<p>1：UpshelfColumnId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyUpshelfColumn", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyUpshelfColumnRes modifyUpshelfColumn(@RequestBody UpshelfColumnReq req, HttpServletRequest request) throws JsonProcessingException {

		ModifyUpshelfColumnRes res = (ModifyUpshelfColumnRes) upshelfColumnService.modifyUpshelfColumn(req);
		return res;
	}
	
	// 新增栏目上架资源
	@ApiOperation(value = "新增栏目上架资源(addUpshelfColumn)", httpMethod = "POST", notes = "新增栏目上架资源(<p>1：")
	@ResponseBody
	@RequestMapping(value = "/addUpshelfColumn", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddUpshelfColumnRes addUpshelfColumn(@RequestBody UpshelfColumnReq req, HttpServletRequest request) throws JsonProcessingException {

		AddUpshelfColumnRes res = (AddUpshelfColumnRes) upshelfColumnService.addUpshelfColumn(req);
		return res;
	}
	
	
	// 删除栏目上架资源
	@ApiOperation(value = "删除栏目上架资源(deleteUpshelfColumn)", httpMethod = "POST", notes = "删除栏目上架资源(<p>1：UpshelfColumnId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteUpshelfColumn", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DeleteUpshelfColumnRes deleteUpshelfColumn(@RequestBody UpshelfColumnReq req, HttpServletRequest request) throws JsonProcessingException {

		DeleteUpshelfColumnRes res = (DeleteUpshelfColumnRes) upshelfColumnService.deleteUpshelfColumn(req);
		return res;
	}
	
	
	// 修改排序
	@ApiOperation(value = "修改排序(setOrderNumber)", httpMethod = "POST", notes = "修改排序(<p>1：UpshelfColumnId和OrderNumber不能同时为空；")
	@ResponseBody
	@RequestMapping(value = "/setOrderNumber", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyUpshelfColumnRes setOrderNumber(@RequestBody UpshelfColumnReq req, HttpServletRequest request) throws JsonProcessingException {

		ModifyUpshelfColumnRes res = (ModifyUpshelfColumnRes) upshelfColumnService.setOrderNumber(req);
		return res;
	}

}
