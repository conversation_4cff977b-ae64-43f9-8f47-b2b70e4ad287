package com.foshan.controller;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.request.BasePageRequest;
import com.foshan.form.request.CancelReq;
import com.foshan.form.request.CheckInReq;
import com.foshan.form.request.ExchangeReq;
import com.foshan.form.request.GetExchangeInfoListReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.points.AddExchangeRes;
import com.foshan.form.response.points.CheckInRes;
import com.foshan.form.response.points.GetCancelListRes;
import com.foshan.form.response.points.GetCheckInInfoRes;
import com.foshan.form.response.points.GetExchangeInfoListRes;
import com.foshan.form.response.points.GetExchangeListRes;
import com.foshan.form.response.points.GetPointsPrizeListRes;
import com.foshan.form.response.points.ModifyExchangeRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
@Api(tags = "融合平台--积分模块")
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
public class PointsController extends BaseController {

	// 签到
	@ApiOperation(value = "签到(CheckIn)", httpMethod = "POST", notes = "签到<p>1:deviceType和smartcardId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/checkIn", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public CheckInRes checkIn(@RequestBody CheckInReq req, HttpServletRequest request)
			throws JsonProcessingException {

		CheckInRes res = (CheckInRes) pointsService.addCheckIn(req);
		return res;
	}

	// 获取用户签到信息
	@ApiOperation(value = "获取用户签到信息(GetCheckInInfo)", httpMethod = "POST", notes = "获取用户签到信息<p>1:deviceType和smartcardId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCheckInInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCheckInInfoRes getCheckInInfo(@RequestBody CheckInReq req, HttpServletRequest request)
			throws JsonProcessingException {

		GetCheckInInfoRes res = (GetCheckInInfoRes) pointsService.getCheckInInfo(req);
		return res;
	}

	// 获取积分奖品列表
	@ApiOperation(value = "获取积分奖品列表(GetPointsPrizeList)", httpMethod = "POST", notes = "获取积分奖品列表<p>1:smartcardId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getPointsPrizeList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetPointsPrizeListRes getPointsPrizeList(@RequestBody BasePageRequest req, HttpServletRequest request)
			throws JsonProcessingException {

		GetPointsPrizeListRes res = (GetPointsPrizeListRes) pointsService.getPointsPrizeList(req);
		return res;
	}

	// 兑换接口
	@ApiOperation(value = "兑换接口(Exchange)", httpMethod = "POST", notes = "兑换接口<p>1:prizeId、smartcardId、weixinOpenId、weixin、weixinAvatar、exchangeNum不能为空；")
	@ResponseBody
	@RequestMapping(value = "/exchange", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddExchangeRes exchange(@RequestBody ExchangeReq req, HttpServletRequest request)
			throws JsonProcessingException {

		AddExchangeRes res = (AddExchangeRes) pointsService.addExchange(req);
		return res;
	}

	// 获取兑换列表
	@ApiOperation(value = "获取兑换列表(GetExchangeList)", httpMethod = "POST", notes = "获取兑换列表<p>1:smartcardId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getExchangeList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetExchangeListRes getExchangeList(@RequestBody BasePageRequest req, HttpServletRequest request)
			throws JsonProcessingException {

		GetExchangeListRes res = (GetExchangeListRes) pointsService.getExchangeList(req);
		return res;
	}

	// 获取核销列表
	@ApiOperation(value = "获取核销列表(getCancelList)", httpMethod = "POST", notes = "获取核销列表<p>1：cancelHallUserId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCancelList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCancelListRes getExchangeList(@RequestBody CancelReq req, HttpServletRequest request)
			throws JsonProcessingException {

		GetCancelListRes res = (GetCancelListRes) pointsService.getCancelList(req);
		return res;
	}

	// 核销
	@ApiOperation(value = "核销(AddCancel)", httpMethod = "POST", notes = "核销<p>1：cancelHall、cancelHallUser、cancelHallUserId、cancelRegion不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addCancel", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addCancel(@RequestBody CancelReq req, HttpServletRequest request)
			throws JsonProcessingException {

		GenericResponse res = (GenericResponse) pointsService.addCancel(req);
		return res;
	}

	// 获取兑奖情况列表
	@ApiOperation(value = "获取兑奖情况列表(GetExchangeInfoList)", httpMethod = "POST", notes = "获取兑奖情况列表")
	@ResponseBody
	@RequestMapping(value = "/getExchangeInfoList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetExchangeInfoListRes getExchangeInfoList(@RequestBody GetExchangeInfoListReq req, HttpServletRequest request)
			throws JsonProcessingException {

		GetExchangeInfoListRes res = (GetExchangeInfoListRes) pointsService.getExchangeInfoList(req);
		return res;
	}

	// 修改兑换订单接口
	@ApiOperation(value = "修改兑奖订单接口(ModifyExchange)", httpMethod = "POST", notes = "修改兑奖订单接口<p>1:realId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyExchange", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyExchangeRes modifyExchange(@RequestBody ExchangeReq req, HttpServletRequest request)
			throws JsonProcessingException {

		ModifyExchangeRes res = (ModifyExchangeRes) pointsService.modifyExchange(req);
		return res;
	}
}
