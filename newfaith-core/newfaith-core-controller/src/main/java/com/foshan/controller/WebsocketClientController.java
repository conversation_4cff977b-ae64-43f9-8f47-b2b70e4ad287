package com.foshan.controller;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.request.UserReq;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.websocket.GetOnlineCountRes;
import com.foshan.service.websocket.WebsocketClient;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "融合平台--webSocket")
@RestController
public class WebsocketClientController extends BaseController {

	@ApiOperation(value = "查看在线人数(getOnlineCount)", httpMethod = "POST", notes = "查看在线人数")
	@ResponseBody
	@RequestMapping(value = "/getOnlineCount", method = {
			RequestMethod.POST }, consumes = MediaType. APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetOnlineCountRes getOnlineCount(@RequestBody UserReq req, HttpServletRequest request)
			throws JsonProcessingException {

		GetOnlineCountRes res = new GetOnlineCountRes();
		res.setOnlineCount(WebsocketClient.getOnlineCount());
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

}
