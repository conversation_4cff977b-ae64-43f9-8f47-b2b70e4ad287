package com.foshan.controller;

import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.request.ArticleReq;
import com.foshan.form.response.IResponse;
import com.foshan.service.IArticleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "融合平台--文章模块")
@RestController
@RequestMapping("/article")
public class ArticleController extends BaseController {

    @Autowired
    private IArticleService articleService;

    @ApiOperation(value = "获取文章列表", notes = "获取文章列表")
    @RequestMapping(value = "/getArticleList", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public IResponse getArticleList(@RequestBody ArticleReq req, HttpServletRequest request) throws JsonProcessingException {
        return articleService.getArticleList(req);
    }

    @ApiOperation(value = "新增文章", notes = "新增文章")
    @RequestMapping(value = "/addArticle", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public IResponse addArticle(@RequestBody ArticleReq req, HttpServletRequest request) throws JsonProcessingException {
        return articleService.addArticle(req);
    }

    @ApiOperation(value = "修改文章", notes = "修改文章")
    @RequestMapping(value = "/modifyArticle", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public IResponse modifyArticle(@RequestBody ArticleReq req, HttpServletRequest request) throws JsonProcessingException {
        return articleService.modifyArticle(req);
    }

    @ApiOperation(value = "删除文章", notes = "删除文章")
    @RequestMapping(value = "/deleteArticle", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public IResponse deleteArticle(@RequestBody ArticleReq req, HttpServletRequest request) throws JsonProcessingException {
        return articleService.deleteArticle(req);
    }

    @ApiOperation(value = "获取文章详情", notes = "获取文章详情")
    @RequestMapping(value = "/getArticleInfo", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public IResponse getArticleInfo(@RequestBody ArticleReq req, HttpServletRequest request) throws JsonProcessingException {
        return articleService.getArticleInfo(req);
    }

    @ApiOperation(value = "审核文章", notes = "审核文章（通过/拒绝）")
    @RequestMapping(value = "/auditArticle", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public IResponse auditArticle(@RequestBody ArticleReq req, HttpServletRequest request) throws JsonProcessingException {
        return articleService.auditArticle(req);
    }
}
