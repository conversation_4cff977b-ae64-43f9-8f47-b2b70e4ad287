package com.foshan.controller.exception;

import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import com.foshan.form.response.BasePageResponse;
import com.foshan.form.response.context.ResponseContext;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@ControllerAdvice
public class ControllerExceptionAdvice {
	

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public BasePageResponse handlerException(Exception e) {
    	BasePageResponse res = new BasePageResponse();
    	if(e.getCause() instanceof SensitiveWordsException){
    		res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
    		res.setRetInfo(e.getCause().getMessage());
    	}else {
    		res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
    		res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
    	}

		log.info("GloableException:{}",e);
        e.printStackTrace();
        return res;
    }

}
