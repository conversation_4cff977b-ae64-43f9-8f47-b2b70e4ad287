package com.foshan.controller;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.request.PointsPlanReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.pointsPlan.AddPointsPlanRes;
import com.foshan.form.response.pointsPlan.GetPointsPlanInfoRes;
import com.foshan.form.response.pointsPlan.GetPointsPlanListRes;
import com.foshan.form.response.pointsPlan.ModifyPointsPlanRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "融合平台--积分策略模块")
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
public class PointsPlanController extends BaseController {
	private final static Logger logger = LoggerFactory.getLogger(PointsController.class);

	// 新增积分策略
	@ApiOperation(value = "新增积分策略(addPointsPlan)", httpMethod = "POST", notes = "新增积分策略<p>1:serviceId、pointsType、deviceType、startTime、endTime不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addPointsPlan", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public AddPointsPlanRes addPointsPlan(@RequestBody PointsPlanReq req, HttpServletRequest request)
			throws JsonProcessingException {
		long start = System.currentTimeMillis();
		logger.info("接收==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==请求数据："
				+ mapper.writeValueAsString(req));
		AddPointsPlanRes res = (AddPointsPlanRes) pointsPlanService.addPointsPlan(req);
		long end = System.currentTimeMillis();
		logger.info(
				"==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==处理时间:" + (end - start) + "毫秒!");
		return res;
	}

	// 修改积分策略
	@ApiOperation(value = "修改积分策略(modifyPointsPlan)", httpMethod = "POST", notes = "修改积分策略<p>1:pointsPlanId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyPointsPlan", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public ModifyPointsPlanRes modifyPointsPlan(@RequestBody PointsPlanReq req, HttpServletRequest request)
			throws JsonProcessingException {
		long start = System.currentTimeMillis();
		logger.info("接收==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==请求数据："
				+ mapper.writeValueAsString(req));
		ModifyPointsPlanRes res = (ModifyPointsPlanRes) pointsPlanService.modifyPointsPlan(req);
		long end = System.currentTimeMillis();
		logger.info(
				"==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==处理时间:" + (end - start) + "毫秒!");
		return res;
	}

	// 删除积分策略
	@ApiOperation(value = "删除积分策略(deletePointsPlan)", httpMethod = "POST", notes = "删除积分策略<p>1:pointsPlanId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deletePointsPlan", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public GenericResponse deletePointsPlan(@RequestBody PointsPlanReq req, HttpServletRequest request)
			throws JsonProcessingException {
		long start = System.currentTimeMillis();
		logger.info("接收==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==请求数据："
				+ mapper.writeValueAsString(req));
		GenericResponse res = (GenericResponse) pointsPlanService.deletePointsPlan(req);
		long end = System.currentTimeMillis();
		logger.info(
				"==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==处理时间:" + (end - start) + "毫秒!");
		return res;
	}

	// 获取积分策略信息
	@ApiOperation(value = "获取积分策略信息 (getPointsPlanInfo)", httpMethod = "POST", notes = "获取积分策略信息<p>1:pointsPlanId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getPointsPlanInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public GetPointsPlanInfoRes getPointsPlanInfo(@RequestBody PointsPlanReq req, HttpServletRequest request)
			throws JsonProcessingException {
		long start = System.currentTimeMillis();
		logger.info("接收==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==请求数据："
				+ mapper.writeValueAsString(req));
		GetPointsPlanInfoRes res = (GetPointsPlanInfoRes) pointsPlanService.getPointsPlanInfo(req);
		long end = System.currentTimeMillis();
		logger.info(
				"==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==处理时间:" + (end - start) + "毫秒!");
		return res;
	}

	// 获取积分策略列表
	@ApiOperation(value = "获取积分策略列表(getPointsPlanList)", httpMethod = "POST", notes = "获取积分策略列表<p>1:serviceId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getPointsPlanList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public GetPointsPlanListRes getPointsPlanList(@RequestBody PointsPlanReq req, HttpServletRequest request)
			throws JsonProcessingException {
		long start = System.currentTimeMillis();
		logger.info("接收==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==请求数据："
				+ mapper.writeValueAsString(req));
		GetPointsPlanListRes res = (GetPointsPlanListRes) pointsPlanService.getPointsPlanList(req);
		long end = System.currentTimeMillis();
		logger.info(
				"==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==处理时间:" + (end - start) + "毫秒!");
		return res;
	}

	// 启用/禁用策略
	@ApiOperation(value = "启用/禁用策略 (changePointsPlanState)", httpMethod = "POST", notes = "启用/禁用策略<p>1:pointsPlanId、planState不能为空；")
	@ResponseBody
	@RequestMapping(value = "/changePointsPlanState", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public AddPointsPlanRes changePointsPlanState(@RequestBody PointsPlanReq req, HttpServletRequest request)
			throws JsonProcessingException {
		long start = System.currentTimeMillis();
		logger.info("接收==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==请求数据："
				+ mapper.writeValueAsString(req));
		AddPointsPlanRes res = (AddPointsPlanRes) pointsPlanService.changePointsPlanState(req);
		long end = System.currentTimeMillis();
		logger.info(
				"==>" + Thread.currentThread().getStackTrace()[1].getMethodName() + "<==处理时间:" + (end - start) + "毫秒!");
		return res;
	}
}
