package com.foshan.controller;

import javax.annotation.Resource;

import com.foshan.service.*;
import org.springframework.beans.factory.annotation.Autowired;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.foshan.util.ContextInfo;
import com.hazelcast.spring.cache.HazelcastCacheManager;

public class BaseController {
	
	protected ObjectMapper mapper = new ObjectMapper();
	@Autowired
	protected ContextInfo contextInfo;
	@Resource(name = "serviceService")
	protected IServiceService serviceService;
	@Resource(name = "visitRealService")
	protected IVisitRealService visitRealService;
	@Resource(name = "visitHistoryService")
	protected IVisitHistoryService visitHistoryService;
	@Resource(name = "assetService")
	protected IAssetService assetService;
	@Resource(name = "columnService")
	protected IColumnService columnService;
	@Resource(name = "regionService")
	protected IRegionService regionService;
	@Resource(name = "statisticalService")
	protected IStatisticalService statisticalService;
	@Resource(name = "accountService")
	protected IAccountService accountService;
	@Resource(name = "jobService")
	protected IJobService jobService;
	@Resource(name = "pointsService")
	protected IPointsService pointsService;
	@Resource(name = "addressService")
	protected IAddressService addressService;
	@Resource(name = "uploadService")
	protected IUploadService uploadService;
	@Resource(name = "roleService")
	protected IRoleService roleService;
	@Resource(name = "permissionService")
	protected IPermissionService permissionService;
	@Resource(name = "giftRollService")
	protected IGiftRollService giftRollService;
	@Resource(name = "assetSpecService")
	protected IAssetSpecService assetSpecService;
	@Resource(name = "userService")
	protected IUserService userService;
	@Resource(name = "upshelfColumnService")
	protected IUpshelfColumnService upshelfColumnService;
	@Resource(name = "weiXinApiService")
	protected IWeiXinApiService weiXinApiService;
	@Resource(name = "assetInteractionService")
	protected IAssetInteractionService assetInteractionService;
	@Resource(name="wxDepartmentService")
	protected IWxDepartmentService wxDepartmentService;
	@Resource(name="serviceVerificationCodeService")
	protected IServiceVerificationCodeService serviceVerificationCodeService;
	@Resource(name="serviceSmartcardService")
	protected IServiceSmartcardService serviceSmartcardService;
	@Resource(name = "pluginService")
	protected IPluginService pluginService;
	@Resource
	protected HazelcastCacheManager cacheManager;
	@Resource(name = "dictionaryService")
	protected IDictionaryService dictionaryService;
	@Resource(name = "dictionaryDataService")
	protected IDictionaryDataService dictionaryDataService;
	@Resource(name = "menuService")
	protected IMenuService menuService;
	@Resource(name = "wxParameterService")
	protected IWxParameterService wxParameterService;
	@Resource(name="pointsBehaviorService")
	protected IPointsBehaviorService pointsBehaviorService;
	@Resource(name = "pointsPoolService")
	protected IPointsPoolService pointsPoolService;
	@Resource(name="pointsPlanService")
	protected IPointsPlanService pointsPlanService;
	@Resource(name="pointsLevelService")
	protected IPointsLevelService pointsLevelService;
	@Resource(name="departmentService")
	protected IDepartmentService departmentService;
	@Resource(name="messageService")
	protected IMessageService messageService;
	
	
	public BaseController() {
		// mapper.setSerializationInclusion(Include.NON_EMPTY);
		mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
	}

}
