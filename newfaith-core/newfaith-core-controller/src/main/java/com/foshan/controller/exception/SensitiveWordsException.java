package com.foshan.controller.exception;



public class SensitiveWordsException extends Exception{

    /**
	 * 
	 */
	private static final long serialVersionUID = 5427227048136048422L;

	/**
     * Creates a new AuthorizationException.
     */
    public SensitiveWordsException() {
        super();
    }

    /**
     * Constructs a new AuthorizationException.
     *
     * @param message the reason for the exception
     */
    public SensitiveWordsException(String message) {
        super(message);
    }

    /**
     * Constructs a new AuthorizationException.
     *
     * @param cause the underlying Throwable that caused this exception to be thrown.
     */
    public SensitiveWordsException(Throwable cause) {
        super(cause);
    }

    /**
     * Constructs a new AuthorizationException.
     *
     * @param message the reason for the exception
     * @param cause   the underlying Throwable that caused this exception to be thrown.
     */
    public SensitiveWordsException(String message, Throwable cause) {
        super(message, cause);
    }
}
