package com.foshan.controller;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.request.MessageReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.msg.GetUnreadMessageCountRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import com.foshan.form.response.msg.GetMessageListRes;
import com.foshan.form.response.msg.GetMessageRes;
import javax.servlet.http.HttpServletRequest;

@Api(tags = "融合平台--消息模块")
@RestController
public class MessageController extends BaseController {

//	@ApiOperation(value = "新增消息(AddMessage)", httpMethod = "POST", notes = "新增消息<p>1:messageCode不能为空；")
//	@ResponseBody
//	@RequestMapping(value = "/addMessage", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public AddMessageRes addMessage(@RequestBody MessageReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		AddMessageRes res =(AddMessageRes)messageService.addMessage(req);
//		return res;
//	}

//	@ApiOperation(value = "修改消息(ModifyMessage)", httpMethod = "POST", notes = "修改消息<p>1:messageId不能为空；")
//	@ResponseBody
//	@RequestMapping(value = "/modifyMessage", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public ModifyMessageRes modifyMessage(@RequestBody MessageReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		ModifyMessageRes res =(ModifyMessageRes)messageService.modifyMessage(req);
//		return res;
//	}
	
	@ApiOperation(value = "获取消息列表(getMessageList)", httpMethod = "POST", notes = "receiverType,receiverId,status")
	@ResponseBody
	@RequestMapping(value = "/getMessageList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetMessageListRes getMessageList(@RequestBody MessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetMessageListRes res =(GetMessageListRes)messageService.getMessageList(req);
		return res;
	}
	
	
	@ApiOperation(value = "获取消息详情(getMessageInfo)", httpMethod = "POST", notes = "获取消息详情<p>1:messageId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getMessageInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetMessageRes getMessageInfo(@RequestBody MessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetMessageRes res =(GetMessageRes)messageService.getMessageInfo(req);
		return res;
	}

	@ApiOperation(value = "获取消息未读消息数量(getUnreadCount)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/getUnreadCount", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetUnreadMessageCountRes getUnreadCount(@RequestBody MessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetUnreadMessageCountRes res =(GetUnreadMessageCountRes)messageService.getUnreadCount(req);
		return res;
	}
	
	@ApiOperation(value = "阅读消息(readMessage)", httpMethod = "POST", notes = "获取消息详情<p>1:id、messsageCode不能同时为空；")
	@ResponseBody
	@RequestMapping(value = "/readMessage", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse readMessage(@RequestBody MessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		
		return (GenericResponse)messageService.readMessage(req);
	}

	@ApiOperation(value = "测试发送消息(sendMessage)", httpMethod = "POST", notes = "获取消息详情<p>1:id、messsageCode不能同时为空；")
	@ResponseBody
	@RequestMapping(value = "/sendMessage", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse sendMessage(@RequestBody MessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		return (GenericResponse)messageService.sendMessage(req);
	}

//	@RequestMapping(value = "/deleteMessage", method = { RequestMethod.POST, RequestMethod.GET })
//	public void deleteMessage(String jsonData, HttpServletRequest request, HttpServletResponse response)
//			throws JsonParseException, JsonMappingException, IOException {
//		long start = System.currentTimeMillis();
//		jsonData = URLDecoder.decode(jsonData, "UTF-8");
//		logger.info("接收==>"+Thread.currentThread() .getStackTrace()[1].getMethodName()+"<==请求数据：" + jsonData);
//		MessageReq req = (MessageReq) mapper.readValue(jsonData, MessageReq.class);
//		jsonOutByJackson(response, mapper, messageService.deleteMessage(req));
//		long end = System.currentTimeMillis();
//		logger.info("==>"+Thread.currentThread() .getStackTrace()[1].getMethodName()+"<==处理时间:" + (end - start) + "毫秒!");
//	}
}
