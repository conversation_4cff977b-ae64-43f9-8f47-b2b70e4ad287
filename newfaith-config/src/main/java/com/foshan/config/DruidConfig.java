package com.foshan.config;

import java.sql.SQLException;

import org.springframework.boot.autoconfigure.quartz.QuartzDataSource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
public class DruidConfig {

    @Primary
    @Bean(name = "faithDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.druid.faith")
    DruidDataSource faithDataSource() {
		log.info("FaithDataSource构建完成！！！");
		DruidDataSource druidDataSource = DruidDataSourceBuilder.create().build();
		try {
			druidDataSource.setFilters("stat,wall");
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return druidDataSource;
	}

    @Bean(name = "quartzDataSource")
    @QuartzDataSource
    @ConfigurationProperties(prefix = "spring.datasource.druid.quartz")
    DruidDataSource quartzDataSource() {
		log.info("QuartzDataSource构建完成！！！");
		//return DruidDataSourceBuilder.create().build();
		DruidDataSource druidDataSource = DruidDataSourceBuilder.create().build();
		try {
			druidDataSource.setFilters("stat,wall");
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return druidDataSource;
	}
}
