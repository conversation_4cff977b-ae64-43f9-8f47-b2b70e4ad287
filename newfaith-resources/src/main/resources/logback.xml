<?xml version="1.0" encoding="utf-8"?>
<configuration debug="false" scan="false"
	scanPeriod="600 seconds">
	<!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径 -->
	<property name="LOG_HOME" value="/data/communitylog" />
	
	
	<!-- 控制台输出 -->
	<appender name="STDOUT"
		class="ch.qos.logback.core.ConsoleAppender">
		<!--日志输出代码 -->
		<layout class="ch.qos.logback.classic.PatternLayout">
			<!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
			<pattern>【%d{yyyy-MM-dd HH:mm:ss.SSS}】-【%-5level】-【%thread】-【%logger】--%msg%n
			</pattern>
		</layout>
	</appender>


	<!-- 按照每天生成日志文件 -->
	<appender name="FILE"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy
			class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>${LOG_HOME}/community_%d{yyyyMMdd}_%i.log
			</fileNamePattern>
			<!-- 如果按天来回滚，则最大保存时间为 （数字+1） 天，（数字+1）天之前的都将被清理掉 -->
			<maxFileSize>100MB</maxFileSize>
			<maxHistory>182</maxHistory>

		</rollingPolicy>
		<append>true</append>
		<encoder
			class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>【%d{yyyy-MM-dd HH:mm:ss.SSS}】-【%-5level】-【%thread】-【%logger】--%msg%n
			</pattern>
			<charset>utf-8</charset>
		</encoder>
	</appender>


    <logger name="org.hibernate.type.descriptor.sql.BasicBinder"  level="debug" />  
    <logger name="org.hibernate.type.descriptor.sql.BasicExtractor"  level="debug" />  
    <logger name="org.hibernate.SQL" level="info" />
 	<logger name="org.hibernate.type" level="debug" />
	<logger name="org.hibernate.engine.QueryParameters" level="debug" />  
 	<logger name="org.hibernate.engine.query.HQLQueryPlan" level="DEBUG" />

	<!-- 日志输出级别 -->
	<root level="info">
		<appender-ref ref="STDOUT" />
		<appender-ref ref="FILE" />
	</root>

</configuration>