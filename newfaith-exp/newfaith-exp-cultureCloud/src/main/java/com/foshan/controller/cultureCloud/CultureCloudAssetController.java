package com.foshan.controller.cultureCloud;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.cultureCloud.request.CultureCloudAssetReq;
import com.foshan.form.cultureCloud.response.asset.AddCultureCloudAssetRes;
import com.foshan.form.cultureCloud.response.asset.GetCultureCloudAssetInfoRes;
import com.foshan.form.cultureCloud.response.asset.GetCultureCloudAssetListRes;
import com.foshan.form.cultureCloud.response.asset.ModifyCultureCloudAssetRes;
import com.foshan.form.request.AssetReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.asset.AddAssetRes;
import com.foshan.form.response.asset.GetAssetListRes;
import com.foshan.form.response.asset.ModifyAssetRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "图文模块")
@RestController
public class CultureCloudAssetController extends BaseCultureCloudController {

	// 获取列表
	@ApiOperation(value = "获取列表(getCultureCloudAssetList)", httpMethod = "POST", notes = "获取列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCultureCloudAssetList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCultureCloudAssetListRes getCommunityEventCategoryItemsList(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCultureCloudAssetListRes res = (GetCultureCloudAssetListRes) cultureCloudAssetService.getCultureCloudAssetList(req);
		return res;
	}
	
	// 新增
	@ApiOperation(value = "新增(addCultureCloudAsset)", httpMethod = "POST", notes = "新增")
	@ResponseBody
	@RequestMapping(value = "/addCultureCloudAsset", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCultureCloudAssetRes addCultureCloudAsset(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCultureCloudAssetRes res = (AddCultureCloudAssetRes) cultureCloudAssetService.addCultureCloudAsset(req);
		return res;
	}
	
	// 修改
	@ApiOperation(value = "修改(modifyCultureCloudAsset)", httpMethod = "POST", notes = "修改，assetId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCultureCloudAsset", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCultureCloudAssetRes modifyCultureCloudAsset(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCultureCloudAssetRes res = (ModifyCultureCloudAssetRes) cultureCloudAssetService.modifyCultureCloudAsset(req);
		return res;
	}
	
	// 修改发布时间
	@ApiOperation(value = "修改发布时间(modifyPublishedTime)", httpMethod = "POST", notes = "修改发布时间，assetId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyPublishedTime", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCultureCloudAssetRes modifyPublishedTime(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCultureCloudAssetRes res = (ModifyCultureCloudAssetRes) cultureCloudAssetService.modifyPublishedTime(req);
		return res;
	}
	
	// 删除
	@ApiOperation(value = "删除(deleteCultureCloudAsset)", httpMethod = "POST", notes = "删除，assetId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCultureCloudAsset", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCultureCloudAsset(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) cultureCloudAssetService.deleteCultureCloudAsset(req);
		return res;
	}
	
	// 获取详情
	@ApiOperation(value = "获取详情(getCultureCloudAssetInfo)", httpMethod = "POST", notes = "获取详情，assetId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCultureCloudAssetInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCultureCloudAssetInfoRes getCultureCloudAssetInfo(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCultureCloudAssetInfoRes res = (GetCultureCloudAssetInfoRes) cultureCloudAssetService.getCultureCloudAssetInfo(req);
		return res;
	}
	
	// 审核媒资
	@ApiOperation(value = "审核媒资(auditCultureCloudAsset)", httpMethod = "POST", notes = "审核媒资，assetId、AuditState不能为空；")
	@ResponseBody
	@RequestMapping(value = "/auditCultureCloudAsset", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse auditCultureCloudAsset(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) cultureCloudAssetService.auditCultureCloudAsset(req);
		return res;
	}
	
	// 上下架
	@ApiOperation(value = "上下架(setAssetState)", httpMethod = "POST", notes = "上下架，assetId、assetState不能为空；")
	@ResponseBody
	@RequestMapping(value = "/setAssetState", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse setAssetState(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) cultureCloudAssetService.setAssetState(req);
		return res;
	}
	
	
	// 新增资源
	@ApiOperation(value = "新增资源(addResources)", httpMethod = "POST", notes = "新增资源；")
	@ResponseBody
	@RequestMapping(value = "/addResources", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddAssetRes addResources(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddAssetRes res = (AddAssetRes) cultureCloudAssetService.addResources(req);
		return res;
	}
	
	// 修改资源
	@ApiOperation(value = "修改资源(modifyResources)", httpMethod = "POST", notes = "修改资源；")
	@ResponseBody
	@RequestMapping(value = "/modifyResources", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyAssetRes modifyResources(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyAssetRes res = (ModifyAssetRes) cultureCloudAssetService.modifyResources(req);
		return res;
	}
	
	// 获取资源列表
	@ApiOperation(value = "获取资源列表(getResourcesList)", httpMethod = "POST", notes = "获取资源列表；parentAssetId不能为空")
	@ResponseBody
	@RequestMapping(value = "/getResourcesList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetAssetListRes getResourcesList(@RequestBody AssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetAssetListRes res = (GetAssetListRes) cultureCloudAssetService.getResourcesList(req);
		return res;
	}
	
	// 修改访问量
	@ApiOperation(value = "修改访问量(setBroadcastCount)", httpMethod = "POST", notes = "修改访问量，assetId，viewNumber不能为空；")
	@ResponseBody
	@RequestMapping(value = "/setBroadcastCount", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse setBroadcastCount(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) cultureCloudAssetService.setBroadcastCount(req);
		return res;
	}
	
	
	// 获取评论列表
	@ApiOperation(value = "获取评论列表(getCultureCloudAssetCommentList)", httpMethod = "POST", notes = "获取评论列表")
	@ResponseBody
	@RequestMapping(value = "/getCultureCloudAssetCommentList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCultureCloudAssetListRes getCultureCloudAssetCommentList(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCultureCloudAssetListRes res = (GetCultureCloudAssetListRes) cultureCloudAssetService.getCultureCloudAssetCommentList(req);
		return res;
	}
	
	// 新增评论
	@ApiOperation(value = "新增评论(addCultureCloudAssetComment)", httpMethod = "POST", notes = "新增评论")
	@ResponseBody
	@RequestMapping(value = "/addCultureCloudAssetComment", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addCultureCloudAssetComment(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) cultureCloudAssetService.addCultureCloudAssetComment(req);
		return res;
	}
   
	// 修改评论
	@ApiOperation(value = "修改评论(modifyCultureCloudAssetComment)", httpMethod = "POST", notes = "修改评论")
	@ResponseBody
	@RequestMapping(value = "/modifyCultureCloudAssetComment", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyCultureCloudAssetComment(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) cultureCloudAssetService.modifyCultureCloudAssetComment(req);
		return res;
	}
	
	// 审核评论
	@ApiOperation(value = "审核评论(auditCultureCloudAssetComment)", httpMethod = "POST", notes = "审核评论")
	@ResponseBody
	@RequestMapping(value = "/auditCultureCloudAssetComment", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse auditCultureCloudAssetComment(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) cultureCloudAssetService.auditCultureCloudAssetComment(req);
		return res;
	}
	
	// 删除评论
	@ApiOperation(value = "删除评论(deleteCultureCloudAssetComment)", httpMethod = "POST", notes = "删除评论")
	@ResponseBody
	@RequestMapping(value = "/deleteCultureCloudAssetComment", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCultureCloudAssetComment(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) cultureCloudAssetService.deleteCultureCloudAssetComment(req);
		return res;
	}
	// 获取评论详情
	@ApiOperation(value = "获取评论详情(getCultureCloudAssetCommentInfo)", httpMethod = "POST", notes = "获取评论详情，assetId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCultureCloudAssetCommentInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCultureCloudAssetInfoRes getCultureCloudAssetCommentInfo(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCultureCloudAssetInfoRes res = (GetCultureCloudAssetInfoRes) cultureCloudAssetService.getCultureCloudAssetCommentInfo(req);
		return res;
	}


	// 删除评论
	@ApiOperation(value = "删除评论(modifySyncAssetWhgdStatus)", httpMethod = "POST", notes = "删除评论")
	@ResponseBody
	@RequestMapping(value = "/modifySyncAssetWhgdStatus", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifySyncAssetWhgdStatus(@RequestBody CultureCloudAssetReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) cultureCloudAssetService.modifySyncAssetWhgdStatus(req);
		return res;
	}
}
