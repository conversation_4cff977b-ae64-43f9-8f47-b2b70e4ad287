package com.foshan.controller.cultureCloud;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.cultureCloud.request.CultureCloudRoomBookReq;
import com.foshan.form.cultureCloud.response.cultureCloudRoomBook.PreEditCultureCloudRoomBookRes;
import com.foshan.form.cultureCloud.response.cultureCloudRoomBook.QueryCultureCloudRoomBookListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.context.ResponseContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "活动室预订模块")
@RestController
public class CultureCloudRoomBookController extends BaseCultureCloudController{


    // 获取活动室预订列表-管理后台用
    @ApiOperation(value = "获取活动室预订列表-管理后台用(queryRoomBookList)", httpMethod = "POST", notes = "获取活动室预订列表-管理后台用")
    @ResponseBody
    @RequestMapping(value = "/queryRoomBookList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public QueryCultureCloudRoomBookListRes bookCultureCloudRoom(@RequestBody CultureCloudRoomBookReq req, HttpServletRequest request)
            throws JsonProcessingException {
        QueryCultureCloudRoomBookListRes res = (QueryCultureCloudRoomBookListRes) cultureCloudRoomBookService.queryRoomBookList(req);
        return res;
    }

    // 修改活动室场次前置接口-管理后台用
    @ApiOperation(value = "修改活动室场次前置接口-管理后台用(preEditRoomBook)", httpMethod = "POST", notes = "修改活动室场次前置接口-管理后台用")
    @ResponseBody
    @RequestMapping(value = "/preEditRoomBook", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public PreEditCultureCloudRoomBookRes preEditRoomBook(@RequestBody CultureCloudRoomBookReq req, HttpServletRequest request)
            throws JsonProcessingException {
        PreEditCultureCloudRoomBookRes res = (PreEditCultureCloudRoomBookRes) cultureCloudRoomBookService.preEditRoomBook(req);
        return res;
    }

    // 修改活动室场次-管理后台用
    @ApiOperation(value = "修改活动室场次-管理后台用(editRoomBook)", httpMethod = "POST", notes = "修改活动室场次-管理后台用")
    @ResponseBody
    @RequestMapping(value = "/editRoomBook", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse editRoomBook(@RequestBody CultureCloudRoomBookReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GenericResponse res = (GenericResponse) cultureCloudRoomBookService.editRoomBook(req);
        return res;
    }

    // 生成七日内活动室场次
    @ApiOperation(value = "生成七日内活动室场次(generateOneDayRoomBook)", httpMethod = "POST", notes = "生成七日内活动室场次")
    @ResponseBody
    @RequestMapping(value = "/generateOneDayRoomBook", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse generateOneDayRoomBook(@RequestBody CultureCloudRoomBookReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GenericResponse res = new GenericResponse();
        int result =  cultureCloudRoomBookService.generateOneDayRoomBook(req);
        if (result==1){
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        }else {
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
        }
        return res;
    }

}
