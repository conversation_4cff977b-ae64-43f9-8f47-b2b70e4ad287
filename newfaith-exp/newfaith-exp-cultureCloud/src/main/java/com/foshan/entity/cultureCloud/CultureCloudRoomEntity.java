package com.foshan.entity.cultureCloud;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import com.foshan.entity.UserEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_culture_cloud_room")
@org.hibernate.annotations.Table(appliesTo = "t_culture_cloud_room",comment="活动室") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CultureCloudRoomEntity extends CultureCloud {
	private static final long serialVersionUID = 7602842954956018861L;
	//活动室
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '活动室UUID'")
	private String roomUUID;
	@Column(columnDefinition = "varchar(64) comment '场馆UUID'")
	private String venueUUID;
	@Column(columnDefinition = "varchar(128) comment '活动名称'")
	private String roomName;
	@Column(columnDefinition = "varchar(256) comment '活动室图片URL'")
	private String roomPicUrl;
	@Column(columnDefinition = "text comment '活动室描述'")
	private String roomIntro;
	@Column(columnDefinition = "int(11) comment '容纳人数'")
	private Integer roomCapacity;
	@Column(columnDefinition = "int(1) comment '[星期一]1没有选中，2选中'")
	private Integer roomDayMonday;
	@Column(columnDefinition = "int(1) comment '[星期二]1没有选中，2选中'")
	private Integer roomDayTuesday;
	@Column(columnDefinition = "int(1) comment '[星期三]1没有选中，2选中'")
	private Integer roomDayWednesday;
	@Column(columnDefinition = "int(1) comment '[星期四]1没有选中，2选中'")
	private Integer roomDayThursday;
	@Column(columnDefinition = "int(1) comment '[星期五]1没有选中，2选中'")
	private Integer roomDayFriday;
	@Column(columnDefinition = "int(1) comment '[星期六]1没有选中，2选中'")
	private Integer roomDaySaturday;
	@Column(columnDefinition = "int(1) comment '[星期日]1没有选中，2选中'")
	private Integer roomDaySunday;
	@Column(columnDefinition = "varchar(256) comment '具体楼层门牌编号'")
	private String roomNO;
	@Column(columnDefinition = "varchar(64) comment '面积大小说明'")
	private String roomArea;
	@Column(columnDefinition = "int(1) comment '否正常开放'")
	private Integer roomIsClosed;
	@Column(columnDefinition = "varchar(256) comment '活动室类型'")
	private String roomType;
	@Column(columnDefinition = "varchar(64) comment '活动室联系人'")
	private String roomLiaison;
	@Column(columnDefinition = "varchar(128) comment '活动室联系邮箱'")
	private String roomMail;
	@Column(columnDefinition = "varchar(32) comment '活动室联系电话'")
	private String roomTel;
	@Column(columnDefinition = "int(1) comment '是否免费1：免费 2：收费 3：支付'")
	private Integer roomIsFree;
    //对应原来的ACTIVITY_PRICE
	@Column(columnDefinition = "varchar(256) comment '收费标准'")  
	private String roomFee;
	@Column(columnDefinition = "text comment '使用须知'")
	private String roomReleaseNotice;
	@Column(columnDefinition = "text comment '配套设施说明'")
	private String roomFacility;
	@Column(columnDefinition = "text comment '配套设施说明备注'")
	private String roomFacilityInfo;
	@Column(columnDefinition = "text comment '备注'")
	private String roomRemark;
	@Column(columnDefinition = "int(1) comment '活动室状态 1-草稿 2-审核通过 3-待审核 4- 审核不通过 5-回收站 6-发布 7-待提交'")
	private Integer roomState;
	@Column(columnDefinition = "varchar(128) comment '标签'")
	private String roomTag;
	@Column(columnDefinition = "int(11) comment '活动室排序'")
	private Integer roomSort;
	@Column(columnDefinition = "int(1) comment '是否置顶(1:是;2:否)'")
	private Integer roomIsTop;
	@Column(columnDefinition = "Timestamp comment '置顶时间'")
	private Timestamp roomTopTime;
	@Column(columnDefinition = "Timestamp comment '更新时间'")
	private Timestamp roomUpdateTime;
	@Column(columnDefinition = "int(11) comment '活动室是否可预订数目'")
	private Integer roomCount;

	@Column(columnDefinition = "int(1) comment '1-核销码，2-开锁码'")
	private Integer useType;
	/**
	 * 需要审核：1-是 0-否
	 */
	private Integer auditEnable;

	private Integer roomIsDel;
	private Integer ifNeedTuser;



	private Date roomCreateTime;
	private Date cancelEndTime;
	private Boolean overCancelTime;
	private Integer allowCancel;
	private Integer cancelEndHour;
	private String roomVenueId;
	private String roomConsultTel;
	private String roomFacilityDict;
	private String sysId;
	private String sysNo;
	private String reservationMethod;
	private Integer ifUploadNeeded;
	private String applicationTypeNeeded;
	private String applicationUrl;
	private String orderRemark;

	private Integer statisticCount;
	private String venueTown;
	private String venueProvince;
	private String venueCity;
	private String venueVillage;

	private String venueArea;
	private String dictName;
	private String venueName;
	private String venueAddress;
	private Integer venueIsDel;
	private Double venueLon;
	private Double venueLat;
	private String SearchKey;
	private Integer availableCount;
	private String roomTagName;
	private Integer roomIsIdentityCard;
	private String roomDesc;
	private String contactPerson;
	private String contactTel;


	/**
	 * 1-开启，0-关闭 预约设置开启长期预约时，活动室可针对发布的活动室选择关闭或者开启，默认为关闭 开启后显示最长期限字段，可输入1-12
	 */
	private Integer longOrderEnable;

	/**
	 * 预约期限（月）
	 */
	private Integer longOrderTime;


	/**
	 * 支持预下单 1-支持 0-不支持
	 */
	private Integer supportPreOrder;
	/**
	 * 个人可预约资质
	 */
	private String personTag;

	/**
	 * 社团可预约资质
	 */
	private String communityTag;

	/**
	 * 单位可预约资质
	 */
	private String unitTag;

	private Integer reservationType;
	
	/**
	 * 场次模板列表
	 */
	@OneToMany(targetEntity = CultureCloudRoomTimeEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "roomTimeId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CultureCloudRoomTimeEntity> roomTimeList = new ArrayList<>();



	/**
	 * 场馆信息创建人
	 */
	@OneToOne(targetEntity = UserEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "userId", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
	private UserEntity roomCreateUser;

	/**
	 * 场馆信息更新人
	 */
	@OneToOne(targetEntity = UserEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "updateUserId", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
	private UserEntity roomUpdateUser;

	@ManyToOne(targetEntity = CultureCloudVenueEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "venueId", referencedColumnName = "id", nullable = true)
	private CultureCloudVenueEntity venue;
	
	@ManyToMany(targetEntity = CultureCloudTagEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_culture_cloud_room_tag", joinColumns = @JoinColumn(name = "roomId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "tagId", referencedColumnName = "id"))
	@JsonIgnore
	private List<CultureCloudTagEntity> tagList = new ArrayList<CultureCloudTagEntity>();
	
}
