package com.foshan.entity.cultureCloud;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_culture_cloud_offline_training")
@org.hibernate.annotations.Table(appliesTo = "t_culture_cloud_offline_training", comment="线下培训")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CultureCloudOfflineTrainingEntity extends CultureCloud {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(columnDefinition = "int(11) comment '主键ID'")
    private Integer id;

    @Column(columnDefinition = "varchar(200) comment '培训标题'")
    private String trainTitle;

    @Column(columnDefinition = "varchar(256) comment '培训图片URL'")
    private String trainImgUrl;

    @Column(columnDefinition = "text comment '培训介绍'")
    private String trainIntroduce;

    @Column(columnDefinition = "int(1) comment '培训状态 0-下架 1-上架' default 0")
    private Integer trainStatus;

    @Column(columnDefinition = "int(1) comment '是否删除 0-删除 1-正常' default 1")
    private Integer isDelete;

    @Column(columnDefinition = "varchar(64) comment '创建人'")
    private String createUser;

    @Column(columnDefinition = "varchar(64) comment '省份'")
    private String trainProvince;

    @Column(columnDefinition = "varchar(64) comment '城市'")
    private String trainCity;

    @Column(columnDefinition = "varchar(64) comment '区域'")
    private String trainArea;

    @Column(columnDefinition = "varchar(256) comment '位置'")
    private String trainLocation;

    @Column(columnDefinition = "varchar(64) comment '镇'")
    private String trainTown;

    @Column(columnDefinition = "varchar(64) comment '村'")
    private String trainVillage;

    @Column(columnDefinition = "varchar(256) comment '详细地址'")
    private String trainAddress;

    @Column(columnDefinition = "varchar(64) comment '场馆类型'")
    private String venueType;

    @Column(columnDefinition = "varchar(64) comment '场馆ID'")
    private String venueId;

    @Column(columnDefinition = "varchar(64) comment '培训类型'")
    private String trainType;

    @Column(columnDefinition = "varchar(128) comment '培训标签'")
    private String trainTag;


    @Column(columnDefinition = "int(1) comment '录取方式 1-先到先得，2人工录取，3随机录取，4面试后录取'")
    private Integer admissionType;

    @Column(columnDefinition = "int(11) comment '最大人数'")
    private Integer maxPeople;

    @Column(columnDefinition = "int(11) comment '培训场次 1-单场 2-多场'")
    private Integer trainField;

    @Column(columnDefinition = "decimal(10,6) comment '经度'")
    private Double lon;

    @Column(columnDefinition = "decimal(10,6) comment '纬度'")
    private Double lat;

    @Column(columnDefinition = "varchar(128) comment '面试时间' default CURRENT_TIMESTAMP")
    private String interviewTime;

    @Column(columnDefinition = "varchar(256) comment '面试地址'")
    private String interviewAddress;

    @Column(columnDefinition = "text comment '温馨提示'")
    private String reminder;

    @Column(columnDefinition = "varchar(32) comment '咨询电话'")
    private String consultingPhone;

    @Column(columnDefinition = "varchar(256) comment '联系方式'")
    private String contactInformation;

    @Column(columnDefinition = "text comment '报名要求'")
    private String registrationRequirements;

    @Column(columnDefinition = "text comment '课程介绍'")
    private String courseIntroduction;

    @Column(columnDefinition = "text comment '教师介绍'")
    private String teachersIntroduction;

    @Column(columnDefinition = "varchar(64) comment '报名开始时间' default CURRENT_TIMESTAMP")
    private String registrationStartTime;

    @Column(columnDefinition = "varchar(64) comment '报名结束时间' default CURRENT_TIMESTAMP")
    private String registrationEndTime;

    @Column(columnDefinition = "varchar(64) comment '培训开始时间' default CURRENT_TIMESTAMP")
    private String trainStartTime;

    @Column(columnDefinition = "varchar(64) comment '培训结束时间' default CURRENT_TIMESTAMP")
    private String trainEndTime;

    @Column(columnDefinition = "varchar(64) comment '更新人'")
    private String updateUser;

    @Column(columnDefinition = "datetime comment '更新时间' default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
    private Date updateTime;

    @Column(columnDefinition = "varchar(32) comment '课程类型(新春班/春季班/暑期班/秋季班)'")
    private String courseType;

    @Column(columnDefinition = "int(11) comment '报名次数'")
    private Integer registrationCount;

    @Column(columnDefinition = "int(1) comment '是否需要签到 0：否 1：是'")
    private Integer sign;

    @Column(columnDefinition = "int(11) comment '所需积分'")
    private Integer integralNeeded;

    @Column(columnDefinition = "int(1) comment '审核状态 0：待审核 1：审核不通过 2：审核通过' default 0")
    private Integer checkStatus;

    @Column(columnDefinition = "int(3) comment '最小年龄'")
    private Integer minAge;

    @Column(columnDefinition = "int(3) comment '最大年龄'")
    private Integer maxAge;

    @Column(columnDefinition = "int(3) comment '女性最小年龄'")
    private Integer femaleMinAge;

    @Column(columnDefinition = "int(3) comment '女性最大年龄'")
    private Integer femaleMaxAge;

    @Column(columnDefinition = "int(1) comment '附件'")
    private Integer enclosure;

    @Column(columnDefinition = "varchar(32) comment '培训周期(1,2,3,4,5,6,7)'")
    private String weekStr;

    @Column(columnDefinition = "int(1) comment '性别限制 0-不限 1-仅男 2-仅女'")
    private Integer genderRestriction;

    @Column(columnDefinition = "int(1) comment '是否发放证书 0-否 1-是'")
    private Integer hasIssuingCertificate;

    @Column(columnDefinition = "int(1) comment '是否可以请假 0-否 1-是'")
    private Integer isAskLeave;

    @Column(columnDefinition = "int(1) comment '是否需要填写身份证号 0-否 1-是'")
    private Integer isCardNo;

    @Column(columnDefinition = "varchar(256) comment '关键词'")
    private String keywords;

    @Column(columnDefinition = "varchar(64) comment '所属平台'")
    private String website;

    @Column(columnDefinition = "varchar(256) comment 'h5链接'")
    private String mobileUrl;

    @Column(columnDefinition = "varchar(256) comment 'pc链接'")
    private String pcUrl;

    @Column(columnDefinition = "varchar(64) comment '发布来源'")
    private String publishSource;

    @Column(columnDefinition = "int(1) comment '是否实名报名 0-否 1-是'")
    private Integer realNameSystem;

    @Column(columnDefinition = "datetime comment '退订截止时间'")
    private Date refundTime;

    @Column(columnDefinition = "varchar(256) comment '视频'")
    private String resourceVideoUrl;

    @Column(columnDefinition = "int(1) comment '课程表添加方式 1-手动添加 2-批量导入 3-固定场次'")
    private Integer scheduleImportType;

    @Column(columnDefinition = "int(1) comment '是否自定义退订截止时间 1-默认 2-自定义'")
    private Integer setRefundWay;

    @Column(columnDefinition = "varchar(128) comment '主办方单位'")
    private String sponsorUnit;

    @Column(columnDefinition = "varchar(64) comment '培训周期'")
    private String trainPeriod;

    @Column(columnDefinition = "varchar(64) comment '活动室ID'")
    private String venueRoomId;

    @Column(columnDefinition = "varchar(256) comment '资源图片URL'")
    private String resourceImgUrl;

    @Column(columnDefinition = "varchar(10) comment '联系人'")
    private String linkman;

    // 添加场次列表字段
    @OneToMany(targetEntity = CultureCloudTrainFieldEntity.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "trainId", referencedColumnName = "id", nullable = true)
    @JsonIgnore
    private List<CultureCloudTrainFieldEntity> fieldList = new ArrayList<>();

    @ManyToMany(targetEntity = CultureCloudTagEntity.class, fetch = FetchType.LAZY)
    @JoinTable(name = "t_culture_cloud_training_tag",
            joinColumns = @JoinColumn(name = "trainingId", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "tagId", referencedColumnName = "id"))
    @JsonIgnore
    private List<CultureCloudTagEntity> tagList = new ArrayList<CultureCloudTagEntity>();
}
