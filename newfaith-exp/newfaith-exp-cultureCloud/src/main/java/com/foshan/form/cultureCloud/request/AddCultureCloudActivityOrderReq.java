package com.foshan.form.cultureCloud.request;

import com.foshan.form.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="(AddCultureCloudActivityOrderReq)")
public  class AddCultureCloudActivityOrderReq extends BaseRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = -5627099663555276317L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer activityOrderId;

	String detailList;
	String activityId;
	String userId;
	String activityEventIds;
	String bookCount;
	String orderMobileNum;
	String orderPrice;
	String activityEventimes;
	String orderName;
	String orderIdentityCard;
	String costTotalCredit;
	String exchangeTimePlaceId;
	String shopPath;
	String machineCode;
	String seatIds;
	String seatValues;
	String sysPlatform;
	Boolean isWhgdOrder;
	@ApiModelProperty(value = "座位ID")
	private String venueSeatIdList;
	@ApiModelProperty(value = "座位状态 1-正常 2-待修 3-不存在 4-vip 5-普通已预订 6-vip已预订 7-预定 8-取消预定'",example="1")
	private Integer seatStatus;

}
