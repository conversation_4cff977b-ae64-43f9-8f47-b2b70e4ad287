package com.foshan.form.cultureCloud.response.cultureCloudSeatAccount;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.cultureCloud.SeatAccountForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取订座列表(GetSeatAccountListRes)")
public class GetSeatAccountListRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6806348654992492503L;
	@ApiModelProperty(value = "订座列表")
	private List<SeatAccountForm> seatAccountList = new ArrayList<SeatAccountForm>(); 

}
