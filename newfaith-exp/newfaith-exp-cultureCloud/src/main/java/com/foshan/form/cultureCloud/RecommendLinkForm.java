package com.foshan.form.cultureCloud;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 推荐链接(RecommendLinkForm)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RecommendLinkForm {

	@ApiModelProperty(value = "推荐链接ID",example="1")
    private Integer recommendLinkId;
	@ApiModelProperty(value = "标题")
    private String title;
	@ApiModelProperty(value = "链接地址")
    private String linkUrl;
	@ApiModelProperty(value = "文件")
	private List<FileForm> fileList = new ArrayList<>();
	
}
