package com.foshan.form.cultureCloud.response.cultureCloudVenueTicket;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取场馆预约和模板(GetCultureCloudVenueTicketAndTemplateRes)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetCultureCloudVenueTicketAndTemplateRes extends BaseResponse {

    private static final long serialVersionUID = -6734208006059511165L;

    Map rtnMap = new HashMap();
}
