package com.foshan.form.cultureCloud;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 文化活动(CultureCloudTagForm)")
@JsonInclude(Include.NON_NULL)
public  class CultureCloudTagForm implements IForm {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer cultureCloudTagId;
    @ApiModelProperty(value = "类别：0--标签 1--关键字",example="1")
    private Integer category;
    @ApiModelProperty(value = "名称")
    private String tagName;
    @ApiModelProperty(value = "显示类别：0--通用; 1--新闻动态;2--活动;3--场管;4--活动室;5--旅游景点;6--景点资讯;",example="1")
    private Integer displayType;
  
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
