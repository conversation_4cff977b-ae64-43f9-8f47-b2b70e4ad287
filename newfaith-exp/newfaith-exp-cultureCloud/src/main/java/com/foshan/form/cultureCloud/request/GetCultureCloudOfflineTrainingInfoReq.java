package com.foshan.form.cultureCloud.request;

import com.foshan.form.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取线下培训详情请求参数")
public class GetCultureCloudOfflineTrainingInfoReq extends BaseRequest {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "培训ID")
    private Integer id;
}