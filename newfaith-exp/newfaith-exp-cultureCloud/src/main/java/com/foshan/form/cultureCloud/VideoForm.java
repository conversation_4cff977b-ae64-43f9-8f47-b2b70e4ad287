package com.foshan.form.cultureCloud;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="视频对象(VideoForm)")
@JsonInclude(Include.NON_NULL)
public  class VideoForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = -6985112195181390939L;
	@ApiModelProperty(value = "视频名称")
	private String videoName;
	@ApiModelProperty(value = "开始播放期限")
    private String startTime;
	@ApiModelProperty(value = "结束播放期限")
    private String endTime;
	@ApiModelProperty(value = "视频备案号")
    private String recordNumber;
	@ApiModelProperty(value = "视频链接")
    private String videoUrl;
	@ApiModelProperty(value = "视频时长")
    private String timeLength;



	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
