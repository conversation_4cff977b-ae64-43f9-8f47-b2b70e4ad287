package com.foshan.form.cultureCloud.response.cultureCloudTrain;

import com.foshan.form.cultureCloud.CultureCloudOfflineTrainingForm;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取文化云培训详情响应
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "获取培训详情响应(GetCultureCloudTrainInfoRes)")
public class GetCultureCloudTrainInfoRes extends BaseResponse {
    @ApiModelProperty(value = "培训信息")
    private CultureCloudOfflineTrainingForm train;
}