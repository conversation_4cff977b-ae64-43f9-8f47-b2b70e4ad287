package com.foshan.form.cultureCloud.response.cultureCloudActivity;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.cultureCloud.CultureCloudActivityForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取活动列表对象(GetCultureCloudActivityListRes)")
@JsonInclude(Include.NON_NULL)
public class GetCultureCloudActivityListRes extends BasePageResponse {
	private static final long serialVersionUID = -2838783796746963282L;
	/**
	 * 
	 */

	@ApiModelProperty(value = "活动列表对象")
	private List<CultureCloudActivityForm> activityList = new ArrayList<>();

}
