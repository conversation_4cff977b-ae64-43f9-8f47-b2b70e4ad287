package com.foshan.form.cultureCloud.response.cultureCloudUser;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.cultureCloud.CultureCloudUserForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取用户列表返回对象(GetCultureCloudUserListRes)")
@JsonInclude(Include.NON_NULL)
public class GetCultureCloudUserListRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8009653692464960529L;
	@ApiModelProperty(value = "用户列表")
	private List<CultureCloudUserForm> users = new ArrayList<CultureCloudUserForm>();
		
}
