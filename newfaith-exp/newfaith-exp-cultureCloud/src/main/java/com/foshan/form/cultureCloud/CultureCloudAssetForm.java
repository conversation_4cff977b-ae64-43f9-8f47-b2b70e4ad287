package com.foshan.form.cultureCloud;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.Column;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.entity.AssetEntity;
import com.foshan.form.AssetForm;
import com.foshan.form.DepartmentForm;
import com.foshan.form.IForm;
import com.foshan.form.RegionForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" (CultureCloudAssetForm)")
@JsonInclude(Include.NON_NULL)
public  class CultureCloudAssetForm implements IForm {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer assetId;
  @ApiModelProperty(value = "时间")
    private String createTime;
    @ApiModelProperty(value = "最后修改时间")
    private String lastModifyTime;
    @ApiModelProperty(value = "状态 0--无效数据  1--有效数据",example="1")
    private Integer state;
    @ApiModelProperty(value = "主演")
    private String actorsDisplay;
    @ApiModelProperty(value = "媒资编号")
    private String assetCode;
    @ApiModelProperty(value = "媒资名称")
    private String assetName;
    @ApiModelProperty(value = "媒资排序值",example="1")
    private Integer assetOrders;
    @ApiModelProperty(value = "媒资状态：0--待审核  1--下架  2--上架",example="1")
    private Integer assetState;
    @ApiModelProperty(value = "媒资类型：0--图片 1--声音 2--电影 3--连续剧 4--系列剧 5--文章",example="1")
    private Integer assetType;
    @ApiModelProperty(value = "审核时间")
    private String auditTime;
    @ApiModelProperty(value = "点播数",example="1")
    private Integer broadcastCount;
    @ApiModelProperty(value = "内容类型：0文章、1点播媒资、2、直播、3外部视频、4外部链接链接、5内部导航",example="1")
    private Integer contentType;
    @ApiModelProperty(value = "当contentType=0时，忽略该字段; 当contentType=1时，BO:providerId/assetId或IP:providerId/assetId/programId;当contentType=2时，DVB:timestamp/channelId直播 timestamp为开始播入时间;当contentType=4时,外部视频时，该字段 为外视频链接的URL,视频格式为HLS、MP4; 当contentType=5外部链接，该字段为URL地址;当contentType=6内部导航")
    private String contentUrl;
    @ApiModelProperty(value = "导演")
    private String director;
    @ApiModelProperty(value = "审核意见")
    private String idea;
    @ApiModelProperty(value = "媒资海报地址")
    private String imageFile;
    @ApiModelProperty(value = "是否为媒资包封面 0--否 1--是",example="1")
    private Integer isCover;
    @ApiModelProperty(value = "中图海报地址")
    private String middleImageFile;
    @ApiModelProperty(value = "媒资包内容数量",example="1")
    private Integer packageCount;
    @ApiModelProperty(value = "媒资包标识：0--单独媒资 1--媒资包",example="1")
    private Integer packageFlag;
    @ApiModelProperty(value = "媒资包内容排序值",example="1")
    private Integer packageOrders;
    @ApiModelProperty(value = "参数信息，以JSON方式保存")
    private String parameterInfo;
    @ApiModelProperty(value = "试看媒资id")
    private String previewAssetId;
    @ApiModelProperty(value = "试看内容提供商")
    private String previewProviderId;
    @ApiModelProperty(value = "发布时间")
    private String publishedTime;
    @ApiModelProperty(value = "点赞数",example="1")
    private Integer recommendCount;
    @ApiModelProperty(value = "所关联内容资源的名称")
    private String resourceName;
    @ApiModelProperty(value = "互动商品编号")
    private String serviceCode;
    @ApiModelProperty(value = "服务ID")
    private String serviceId;
    @ApiModelProperty(value = "小图海报地址")
    private String smallImageFile;
    @ApiModelProperty(value = "内容来源或出处")
    private String source;
    @ApiModelProperty(value = "副标题")
    private String subTitle;
    @ApiModelProperty(value = "简介")
    private String summaryShort;
    @ApiModelProperty(value = "媒资时长",example="1")
    private Integer timeLength;
    @ApiModelProperty(value = "文章发布者")
    private String userName;
    @ApiModelProperty(value = "过期时间")
    private String validTime;
    @ApiModelProperty(value = "",example="1")
    private Integer parentAssetId;
    @ApiModelProperty(value = "")
    private String dTYPE;
    @ApiModelProperty(value = "审核状态  0:初始化；1:初审通过；2：初审不通过；3：终审通过；4：终审不通过",example="1")
    private Integer auditState;
    @ApiModelProperty(value = "所属部门ID",example="1")
    private Integer departmentId;
    @ApiModelProperty(value = "主办方")
    private String sponsor;
    @ApiModelProperty(value = "所属单位")
    private String affiliatedUnit;
    @ApiModelProperty(value = "",example="1")
    private Integer memberId;
    @ApiModelProperty(value = "",example="1")
    private Integer userId;
    @ApiModelProperty(value = "",example="1")
    private Integer parentTypeId;
    @ApiModelProperty(value = "标签")
    private String tags;
    @ApiModelProperty(value = "内容")
    private String assetContent;
    @ApiModelProperty(value = "PC外链地址")
    private String pcUrl;
    @ApiModelProperty(value = "H5外链地址")
    private String h5Url;
    @ApiModelProperty(value = "视频名称")
    private String videoName;
    @ApiModelProperty(value = "开始播放期限")
    private String startTime;
    @ApiModelProperty(value = "结束播放期限")
    private String endTime;
    @ApiModelProperty(value = "视频链接")
    private String videoUrl;
    @ApiModelProperty(value = "关键字")
    private String keywords;
    @ApiModelProperty(value = "视频链接")
    private OutsideChainForm outsideChain;
    @ApiModelProperty(value = "视频")
    private VideoForm videoForm;
    @ApiModelProperty(value = "部门")
    private DepartmentForm department;
    @ApiModelProperty(value = "区域")
    private RegionForm regiom;
    @ApiModelProperty(value = "上架排序",example="1")
    private Integer orderNumber;
    @ApiModelProperty(value = "上架时间")
    private String upshelfTime;
    @ApiModelProperty(value = "上架ID",example="1")
    private Integer upshelfColumnId;
    @ApiModelProperty(value = "推荐连接")
    private String recommendTitle;
    @ApiModelProperty(value = "返回个人的评论 0：否；1：是；",example="1")
    private Integer individualComment;
    @ApiModelProperty(value = "头像")
    private String headImage;
    @ApiModelProperty(value = "类型")
    private List<CultureCloudTypeForm> typeList = new ArrayList<>();
    @ApiModelProperty(value = "附件")
    private List<AssetForm> attachmentAssetList = new ArrayList<>();
    @ApiModelProperty(value = "文件")
    private List<AssetForm> fileAssetList = new ArrayList<>();
    @ApiModelProperty(value = "视频图片")
    private List<AssetForm> videoImageList = new ArrayList<>();
    @ApiModelProperty(value = "图片")
    private List<AssetForm> imageList = new ArrayList<>();
    @ApiModelProperty(value = "资源")
    private List<AssetForm> resourcesList = new ArrayList<>();
    @ApiModelProperty(value = "资源")
    private List<CultureCloudUpshelfColumnForm> upshelfColumnList = new ArrayList<>();
    @ApiModelProperty(value = "推荐链接模块")
    private List<RecommendLinkForm> recommendLinkList = new ArrayList<>();
    @ApiModelProperty(value = "子媒资")
    private List<CultureCloudAssetForm> subAssetList = new ArrayList<>();
    @ApiModelProperty(value = "是否已同步到文化广东 0-未同步，1-已同步 2-已修改未同步 3-已修改",example="0")
    private Integer hasSynced;
    @ApiModelProperty(value = "父")
    private CultureCloudAssetForm parentCultureCloudAsset;
  
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
