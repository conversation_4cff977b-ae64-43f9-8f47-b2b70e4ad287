package com.foshan.form.cultureCloud.request;

import com.foshan.entity.cultureCloud.CultureCloudVenueTicketOrderDetailEntity;
import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="活动场次请求(CultureCloudVenueTicketOrderReq)")
public class CultureCloudVenueTicketOrderReq extends BasePageRequest {

    private static final long serialVersionUID = -8595370557845603829L;
    String venueTicketOrderId;
    Integer venueOrderId;
    Integer venueTicketEventId;
    String userId;
    String orderSerialNumber;
    String orderPersonName;
    String orderPersonLinknum;
    String orderCode;
    Integer orderVotes;
    Integer orderStatus;
    Integer isDeleted;
    Integer allowCancel;
    Integer cancelEndHour;
    Integer cancelEndBeforeHour;
    Integer cancelBeforeOrEnd;
    Integer reorderNum;
    Date createTime;
    Date updateTime;
    String updateBackUser;
    String shopPath;
    String shopProvince;
    Integer page;
    Integer rows;
    List<CultureCloudVenueTicketOrderDetailEntity> orderDetailList;
    Integer selectType;
    Integer fromBack;
    String venueId;
    String orderDetailPersonLinknum;
    String orderDetailPersonCardno;
    String orderDetailCode;
    Integer memberId;
    String verifyCodes;
    Integer verifyMode;

    Integer userType;
    String curDateStart;
    String curDateEnd;
    String orderCreateTimeStart;
    String orderCreateTimeEnd;
    String venueName;
    Integer bookStatus;
    Boolean isWhgdOrder;
}
