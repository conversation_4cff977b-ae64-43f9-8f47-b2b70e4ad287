package com.foshan.form.cultureCloud.response.cultureCloudAddress;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.cultureCloud.CultureCloudAddressForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取地址详情返回对象(GetCultureCloudAddressInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetCultureCloudAddressInfoRes extends BaseResponse {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 3516388083536747485L;
	private CultureCloudAddressForm addressForm;

}
