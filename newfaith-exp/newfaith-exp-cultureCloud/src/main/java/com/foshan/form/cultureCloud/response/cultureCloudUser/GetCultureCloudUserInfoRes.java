package com.foshan.form.cultureCloud.response.cultureCloudUser;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.cultureCloud.CultureCloudUserForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取角色信息返回对象(GetCultureCloudUserInfo)")
@JsonInclude(Include.NON_NULL)
public class GetCultureCloudUserInfoRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1949200982821780904L;
	@ApiModelProperty(value = "角色对象")
	private CultureCloudUserForm user;

}
