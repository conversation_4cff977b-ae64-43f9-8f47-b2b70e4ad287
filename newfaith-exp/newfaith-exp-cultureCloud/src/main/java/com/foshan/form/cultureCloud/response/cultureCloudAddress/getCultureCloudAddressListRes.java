package com.foshan.form.cultureCloud.response.cultureCloudAddress;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.cultureCloud.CultureCloudActivityForm;
import com.foshan.form.cultureCloud.CultureCloudAddressForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

	
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取活动列表对象(getCultureCloudAddressListRes)")
@JsonInclude(Include.NON_NULL)
public class getCultureCloudAddressListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 1889136215627588835L;
	@ApiModelProperty(value = "地址列表对象")
	private List<CultureCloudAddressForm> addressList = new ArrayList<>();
	

}
