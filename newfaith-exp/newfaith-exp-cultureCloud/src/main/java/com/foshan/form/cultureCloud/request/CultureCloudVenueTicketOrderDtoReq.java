package com.foshan.form.cultureCloud.request;

import com.foshan.entity.cultureCloud.dto.CultureCloudVenueTicketOrderDto;
import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="活动请求参数(CultureCloudVenueTicketOrderDtoReq)")
public class CultureCloudVenueTicketOrderDtoReq extends CultureCloudVenueTicketOrderReq {

	private static final long serialVersionUID = -3184210186388736594L;
	/**
	 *
	 */

	private String venueId;
	private String createTimeStr;

	private String userName;
	private Date eventDate;
	private Integer orderNum;
	private String venueName;
	private String timePeriod;
	private String updateBackUserName;
	private Integer canCancel;
	private String curDateStart;
	private String curDateEnd;
	private String realInformation;
	private String orderStatusList;
}
