package com.foshan.service.cultureCloud.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.cache.Cache;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.jdbc.Work;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.foshan.dao.generic.Page;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.RoleEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.cultureCloud.CultureCloudMemberEntity;
import com.foshan.form.cultureCloud.CultureCloudMemberForm;
import com.foshan.form.cultureCloud.request.CultureCloudMemberReq;
import com.foshan.form.cultureCloud.response.cultureCloudMember.GetCultureCloudMemberInfoRes;
import com.foshan.form.cultureCloud.response.cultureCloudMember.GetCultureCloudMemberListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.cultureCloud.ICultureCloudMemberService;
import com.foshan.util.CodeUtil;
import com.foshan.util.DateUtil;
import com.foshan.util.DigestUtil;
import com.foshan.util.SpringHandler;
import com.hazelcast.spring.cache.HazelcastCacheManager;


@Transactional
@Service("cultureCloudMemberService")
public class CultureCloudMemberServiceImpl extends GenericCultureCloudService implements ICultureCloudMemberService{
    @Resource
    private HazelcastCacheManager cacheManager;
	@Override
	public IResponse getCultureCloudMemberList(CultureCloudMemberReq req) {
		GetCultureCloudMemberListRes res = new GetCultureCloudMemberListRes();
		Page<CultureCloudMemberEntity> page = new Page<CultureCloudMemberEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 0) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		
		StringBuilder hql = new StringBuilder("select distinct a from CultureCloudMemberEntity a where a.userState=1");
		hql.append(null!=req.getCloseComments() ? " and a.closeComments="+req.getCloseComments():"")
			.append(null!=req.getPassAuthentication() ? " and a.passAuthentication="+req.getPassAuthentication() : "")
			.append(StringUtils.isNotEmpty(req.getUserName()) ? " and a.userName like'%"+req.getUserName()+"%'" : "")
			.append(StringUtils.isNotEmpty(req.getLoginName()) ? " and a.loginName like'%"+req.getLoginName()+"%'" : "");


		page = cultureCloudMemberDao.queryPage(page, hql.toString());
		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		page.getResultList().forEach(o -> {
			CultureCloudMemberForm memberForm = new CultureCloudMemberForm();
			memberForm.setMemberId(o.getId());
			memberForm.setEmail(o.getEmail());
			memberForm.setHomePhone(o.getHomePhone());
			memberForm.setNickName(o.getNickName());
			memberForm.setOfficePhone(o.getOfficePhone());
			memberForm.setPhone(o.getPhone());
			memberForm.setRegistName(o.getRegistName());
			memberForm.setSex(o.getSex());
			memberForm.setSmartcardId(o.getSmartcardId());
			memberForm.setHeadImage(StringUtils.isNotEmpty(o.getHeadImage()) ? o.getHeadImage() : "");
			memberForm.setUserName(StringUtils.isNotEmpty(o.getUserName()) ? o.getUserName() : "");
			memberForm.setState(null != o.getUserState()?o.getUserState():null);
			memberForm.setRegistTime(null != o.getRegistTime()?sdf.format(o.getRegistTime()):null);
			memberForm.setRegionCode(StringUtils.isNotEmpty(o.getRegionCode()) ? o.getRegionCode() : "");
			memberForm.setIdCard(StringUtils.isNotEmpty(o.getIdCard()) ? o.getIdCard() : "");
			memberForm.setCloseComments(o.getCloseComments());
			memberForm.setPassAuthentication(o.getPassAuthentication());

			res.getMemberList().add(memberForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		
		return res;
	}
	
	@Audit(operate = "修改会员")
	public IResponse modifyCultureCloudMember(CultureCloudMemberReq req) {
		GenericResponse res = new GenericResponse();
		Object userObj = getPrincipal(true);
		CultureCloudMemberEntity member = null;
		if (null != userObj && userObj instanceof CultureCloudMemberEntity) {
			member= (CultureCloudMemberEntity) userObj;
		}else if(null!=req.getMemberId() ) {
			member= cultureCloudMemberDao.getUniqueBySql(
					"SELECT * FROM t_account WHERE id="+req.getMemberId()+"","");
			if (null == member) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
		}else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("用户未登录或有参数为空！");
			return res;
		}
//		if(StringUtils.isNotEmpty(req.getRoleIdList())) {
//			member.setRoleList(null);
//			String[] rolerId = req.getRoleIdList().split(",");
//			List<RoleEntity> roleList = new ArrayList<RoleEntity>();
//			for (String id : rolerId) {
//				RoleEntity role = roleDao.get(Integer.valueOf(id));
//				if (null != role) {
//					roleList.add(role);
//				}
//			}
//			member.setRoleList(roleList);
//		}
		member.setLastModifyTime(new Timestamp(new Date().getTime()));
		member.setEmail(StringUtils.isNotEmpty(req.getEmail()) ? req.getEmail() : member.getEmail());
		member.setPhone(StringUtils.isNotEmpty(req.getPhone()) ? req.getPhone() : member.getPhone());
		member.setSex(null != req.getSex() ? req.getSex() : member.getSex());
		member.setSmartcardId(
				StringUtils.isNotEmpty(req.getSmartcardId()) ? req.getSmartcardId() : member.getSmartcardId());
		member.setHeadImage(
				StringUtils.isNotEmpty(req.getHeadImage()) ? req.getHeadImage() : member.getHeadImage());
		member.setRegionCode(
				StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : member.getRegionCode());
		member.setUserName(
				StringUtils.isNotEmpty(req.getUserName()) ? req.getUserName() : member.getUserName());
		member.setComment(StringUtils.isNotEmpty(req.getComment()) ? req.getComment():member.getComment());
		member.setTvNo(StringUtils.isNotEmpty(req.getTvNo()) ? req.getTvNo():member.getTvNo());
		member.setMac(StringUtils.isNotEmpty(req.getMac()) ?req.getMac():member.getMac());
		member.setIp(StringUtils.isNotEmpty(req.getIp()) ? req.getIp():member.getIp());
		member.setWeixinOpenId(StringUtils.isNotEmpty(req.getWeixinOpenId()) ? req.getWeixinOpenId():member.getWeixinOpenId());
		member.setWeixinAvatar(StringUtils.isNotEmpty(req.getWeixinAvatar()) ? req.getWeixinAvatar():member.getWeixinAvatar());
		member.setHomeAddress(StringUtils.isNotEmpty(req.getHomeAddress()) ? req.getHomeAddress():member.getHomeAddress());
		member.setCompany(StringUtils.isNotEmpty(req.getCompany()) ? req.getCompany():member.getCompany());
		member.setPostalCode(StringUtils.isNotEmpty(req.getPostalCode()) ? req.getPostalCode():member.getPostalCode());
		member.setNation(StringUtils.isNotEmpty(req.getNation()) ? req.getNation():member.getNation());
		member.setPoliceStation(StringUtils.isNotEmpty(req.getPoliceStation()) ? req.getPoliceStation():member.getPoliceStation());
		member.setNativePlace(StringUtils.isNotEmpty(req.getNativePlace()) ? req.getNativePlace():member.getNativePlace());
		member.setOfficeAddress(StringUtils.isNotEmpty(req.getOfficeAddress()) ? req.getOfficeAddress():member.getOfficeAddress());
		member.setExchangeRegion(StringUtils.isNotEmpty(req.getExchangeRegion()) ? req.getExchangeRegion():member.getExchangeRegion());
		member.setExchangeHall(StringUtils.isNotEmpty(req.getExchangeHall()) ? req.getExchangeHall():member.getExchangeHall());
		member.setMiniProgramOpenId(StringUtils.isNotEmpty(req.getMiniProgramOpenId()) ? req.getMiniProgramOpenId():member.getMiniProgramOpenId());
		member.setIsSmartcardMaster(null!=req.getIsSmartcardMaster() ? req.getIsSmartcardMaster():member.getIsSmartcardMaster());
		member.setHomePhone(StringUtils.isNotEmpty(req.getHomePhone()) ? req.getHomePhone():member.getHomePhone());
		member.setBirthday(StringUtils.isNotEmpty(req.getBirthday()) ? req.getBirthday():member.getBirthday());
		member.setPhoneVerifyState(null!=req.getPhoneVerifyState() ? req.getPhoneVerifyState():member.getPhoneVerifyState());
		member.setQq(StringUtils.isNotEmpty(req.getQq()) ? req.getQq():member.getQq());
		member.setWeixin(StringUtils.isNotEmpty(req.getWeixin()) ? req.getWeixin():member.getWeixin());
		member.setPhoto(StringUtils.isNotEmpty(req.getPhoto()) ? req.getPhoto():member.getPhoto());
		member.setIdType(StringUtils.isNotEmpty(req.getIdType()) ? req.getIdType():member.getIdType());
		member.setIdCard(StringUtils.isNotEmpty(req.getIdCard()) ? req.getIdCard():member.getIdCard());
		member.setNickName(StringUtils.isNotEmpty(req.getNickName()) ? req.getNickName():member.getNickName());
		
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		return res;
	}

	@Audit(operate = "会员注册")
	public IResponse memberRegist(CultureCloudMemberReq req, HttpServletRequest request) {
		GenericResponse res = new GenericResponse();
		String phone = (StringUtils.isNotEmpty(req.getPhone()) ? req.getPhone() : "");
		if (StringUtils.isEmpty(phone)) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "注册信息空！");
			return res;
		}
		JsonNode json = null;
//		try {
//			if (null != request.getSession().getAttribute("verifyCode")) {
//				json = new ObjectMapper().readTree((String) request.getSession().getAttribute("verifyCode"));
//			}
//		} catch (IOException e1) {
//			e1.printStackTrace();
//		}
	//	if (json == null) {
			Cache cache = cacheManager.getCache("smsCache");
			Map<String, Object> smsCache = cache.get(phone, Map.class);
			if(null==smsCache) {
				res.setRet(ResponseContext.RES_VERIF_CODE_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_VERIF_CODE_ERROR_INFO);
				return res;
			}
			String verifyCode = smsCache.get("verifyCode").toString();
			if (StringUtils.isEmpty(verifyCode) ||(StringUtils.isNotEmpty(verifyCode)  
					&& !verifyCode.equals(req.getVerifyCode()))) {
				res.setRet(ResponseContext.RES_VERIF_CODE_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_VERIF_CODE_ERROR_INFO);
				return res;
			}
		//}
//		if ((System.currentTimeMillis() - json.get("createTime").longValue()) > 1000 * 60 * contextInfo.smsEffective) {
//			res.setRet(ResponseContext.RES_VERIF_CODE_OVERDUE_CODE);
//			res.setRetInfo(ResponseContext.RES_VERIF_CODE_OVERDUE_INFO);
//			return res;
//		}
		CultureCloudMemberEntity member = cultureCloudMemberDao.getUniqueBySql("SELECT * FROM t_account WHERE phone="+req.getPhone()+" and userState = 1",
				"");
		String wechatOpenid = (String) request.getSession().getAttribute("wechatOpenid");
		if (null != member) {
			if (StringUtils.isNotEmpty(wechatOpenid)) {
				// 微信快速注册，老用户
				member.setWeixinOpenId(wechatOpenid);
			} else {
				// 普通注册流程
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo("对不起，您注册的用户名或手机号已被注册！");
				return res;
			}
		} else {
			String[] rolerId = contextInfo.getMemberRoleId().split(",");
			List<RoleEntity> roleList = new ArrayList<RoleEntity>();
			for (String id : rolerId) {
				RoleEntity role = roleDao.get(Integer.valueOf(id));
				if (null != role) {
					roleList.add(role);
				}
			}
			member = new CultureCloudMemberEntity();
			member.setLoginName(req.getPhone());
			member.setNickName(StringUtils.isNotEmpty(req.getNickName()) ? req.getNickName() : req.getPhone());
			member.setUserName(StringUtils.isNotEmpty(req.getUserName()) ? req.getUserName() : "");
			// 密码选填
			String password = req.getPassword();
			if (StringUtils.isNotBlank(password)) {
				try {
					member.setPassword(DigestUtil.sm3Digest(req.getPassword()));
					member.setLastPwdModifyTime(new Timestamp(new Date().getTime()));
				} catch (UnsupportedEncodingException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}

			member.setRegistName(req.getPhone());
			member.setPhone(phone);
			member.setPhoneVerifyState(EntityContext.RECORD_STATE_VALID);
			member.setUserState(EntityContext.RECORD_STATE_VALID);
			member.setSex(null != req.getSex() ? req.getSex() : 2);
			member.setRegionCode(StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "");
			member.setRoleList(roleList);
			// String wechatOpenid = (String)
			// request.getSession().getAttribute("wechatOpenid");
			// 微信快速注册
			member.setWeixinOpenId(StringUtils.isEmpty(wechatOpenid) ? "" : wechatOpenid);
			member.setType(EntityContext.ACCOUNT_TYPE_PRIVATE_ACCOUNT);
			member.setPassAuthentication(0);

			cultureCloudMemberDao.save(member);
		}

		// 微信快速注册的情况，自动登录
		if (StringUtils.isNotEmpty(wechatOpenid)) {
//			PhoneToken token = new PhoneToken(wechatOpenid, wechatOpenid, "3");
//			token.setRememberMe(true);
//			Subject curUser = SecurityUtils.getSubject();
//			curUser.login(token);
//			curUser.getSession().setTimeout(1800000);// 180000毫秒/30分钟超时
		}

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	@Audit(operate = "禁止评论/取消禁止")
	public IResponse setMemberCloseComments(CultureCloudMemberReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getMemberId() && null!=req.getCloseComments()) {
			CultureCloudMemberEntity memeber = cultureCloudMemberDao.get(req.getMemberId());
			if(null!=memeber) {
				memeber.setCloseComments(req.getCloseComments() );

				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}			
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	@Audit(operate = "会员身份认证")
	public IResponse memberAuthentication(CultureCloudMemberReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getMemberId() && null!=req.getPassAuthentication()) {
			//(req.getPassAuthentication()==3 ||req.getPassAuthentication()==1)
			CultureCloudMemberEntity memeber = cultureCloudMemberDao.get(req.getMemberId());
			if(null!=memeber) {
				memeber.setPassAuthentication(req.getPassAuthentication());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}			
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	@Audit(operate = "会员提交身份认证")
	public IResponse  submitAuthentication(CultureCloudMemberReq req) {
		GenericResponse res = new GenericResponse();
		Object userObj = getPrincipal(true);
		CultureCloudMemberEntity member = null;
		if (null != userObj && userObj instanceof CultureCloudMemberEntity) {
			member= (CultureCloudMemberEntity) userObj;
			req.setMemberId(member.getId());
		}
		if (null != req.getMemberId() ) {
			CultureCloudMemberEntity memeber = cultureCloudMemberDao.get(req.getMemberId());
			if(null!=memeber &&(memeber.getPassAuthentication()==0 ||memeber.getPassAuthentication()==3)) {
				memeber.setPassAuthentication(2);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else if(null!=memeber &&memeber.getPassAuthentication()==1){
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo("对不起，已经实名认证，无需再提交！");
			}else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}			
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Audit(operate = "修改会员")
	public IResponse getCultureCloudMemberInfo(CultureCloudMemberReq req) {
		GetCultureCloudMemberInfoRes res = new GetCultureCloudMemberInfoRes();
		Object userObj = getPrincipal(true);
		CultureCloudMemberEntity member = null;
		if (null != userObj && userObj instanceof CultureCloudMemberEntity) {
			member= (CultureCloudMemberEntity) userObj;
		}else if(null!=req.getMemberId() ) {
			member= cultureCloudMemberDao.getUniqueBySql(
					"SELECT * FROM t_account WHERE id="+req.getMemberId()+"","");
			if (null == member) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
		}else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("用户未登录或有参数为空！");
			return res;
		}
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		CultureCloudMemberForm memberForm = new CultureCloudMemberForm();
		memberForm.setMemberId(member.getId());
		memberForm.setEmail(member.getEmail());
		memberForm.setHomePhone(member.getHomePhone());
		memberForm.setNickName(member.getNickName());
		memberForm.setOfficePhone(member.getOfficePhone());
		memberForm.setPhone(member.getPhone());
		memberForm.setRegistName(member.getRegistName());
		memberForm.setSex(member.getSex());
		memberForm.setSmartcardId(member.getSmartcardId());
		memberForm.setHeadImage(StringUtils.isNotEmpty(member.getHeadImage()) ? member.getHeadImage() : "");
		memberForm.setUserName(StringUtils.isNotEmpty(member.getUserName()) ? member.getUserName() : "");
		memberForm.setState(null != member.getUserState()?member.getUserState():null);
		memberForm.setRegistTime(null != member.getRegistTime()?sdf.format(member.getRegistTime()):null);
		memberForm.setRegionCode(StringUtils.isNotEmpty(member.getRegionCode()) ? member.getRegionCode() : "");
		memberForm.setIdCard(StringUtils.isNotEmpty(member.getIdCard()) ? member.getIdCard() : "");
		memberForm.setPassAuthentication(member.getPassAuthentication());
		
		res.setMember(memberForm);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		return res;
	}
	
	@Override
	public IResponse modifyMemberPassword(CultureCloudMemberReq req) {
		GenericResponse res = new GenericResponse();
		if (StringUtils.isEmpty(req.getPassword())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}
		Object userObj = getPrincipal(true);
		if (null != userObj && userObj instanceof CultureCloudMemberEntity) {
			CultureCloudMemberEntity m = (CultureCloudMemberEntity) userObj;
			if (null != m) {
				try {
					m.setPassword(DigestUtil.sm3Digest(req.getPassword()));
					m.setLastPwdModifyTime(new Timestamp(new Date().getTime()));
					m.setSalt(null);
				} catch (UnsupportedEncodingException e) {
					e.printStackTrace();
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} 

		}else if(StringUtils.isNotEmpty(req.getPhone())&&StringUtils.isNotEmpty(req.getMessageCode())) {
            String messageCode = null;
            Cache cache = cacheManager.getCache("smsCache");
            Map<String, Object> smsCache = cache.get(req.getPhone(), Map.class);
            if(null == smsCache) {
				res.setRet(ResponseContext.RES_SEND_SMS_CODE);
				res.setRetInfo("对不起，请先获取短信验证码！");
				return res;
            }
            messageCode = smsCache.get("verifyCode").toString();
			if (messageCode.equals(req.getMessageCode())) {
				try {
					StringBuilder hql = new StringBuilder("select distinct a from CultureCloudMemberEntity a where"
							+ " a.userState=1 and a.phone='"+req.getPhone()+"'");
					List<CultureCloudMemberEntity> list = cultureCloudMemberDao.getListByHql(hql.toString(), "");
					if(null!=list && list.size()>0) {
						CultureCloudMemberEntity m = list.get(0);
								m.setPassword(DigestUtil.sm3Digest(req.getPassword()));
								m.setLastPwdModifyTime(new Timestamp(new Date().getTime()));
								m.setSalt(null);
					}else {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					}
				} catch (UnsupportedEncodingException e) {
					e.printStackTrace();
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo("对不起，验证码错误！");
			}
		} else {
			res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO);
		}
		return res;
	}

//	@Override
//	public CultureCloudMemberEntity checkUser(String userId) {
//		MultiValueMap<String, Object> paramMap = new LinkedMultiValueMap<>(16);
//		paramMap.add("userId", userId);
//		ResponseEntity<Result> response = restTemplate.postForEntity("http://userServer/back/terminalUser/queryTerminalUser", paramMap, Result.class);
//		Result userResult = response.getBody();
//		if (userResult.getStatus().equals(200)) {
//			Object data = userResult.getData();
//			if (data != null) {
//				String s = JSONObject.toJSONString(data);
//				CultureCloudMemberEntity cmsTerminalUser = JSON.parseObject(s, new TypeReference<CultureCloudMemberEntity>() {
//				});
//				return cmsTerminalUser;
//			} else {
//				return null;
//			}
//		} else {
//			return null;
//		}
//	}
	


	public IResponse importCloudMember(HttpServletRequest request) {
		try {
			GenericResponse res  = cloudMemberHandler(uploadFile(request));
			return res;
		} catch (IllegalStateException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	@SuppressWarnings({ "resource", "unused", "unchecked" })
	private GenericResponse cloudMemberHandler(File excelFile) throws IOException {
		GenericResponse res  = new GenericResponse();

		FileInputStream inputStream = new FileInputStream(excelFile);
		Workbook workBook = null;
		if (excelFile.getName().endsWith("xls")) {
			workBook = new HSSFWorkbook(inputStream);
		} else if (excelFile.getName().endsWith("xlsx")) {
			workBook = new XSSFWorkbook(inputStream);
		} else {
			//logger.info(excelFile + "不是excel类型文件！！！");
			return null;
		}
		Sheet sheet = workBook.getSheetAt(workBook.getActiveSheetIndex());
		StringBuilder info = new StringBuilder();
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		Transaction transaction = session.beginTransaction();
		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {
				StringBuilder sql = new StringBuilder(
						"insert into  t_account(accountType,phone,phoneVerifyState,userState,"
								+ "type,userName,closeComments,passAuthentication) "
								+ "values('CC',?,1,1,0,?,0,1) ");

				PreparedStatement stmt = conn.prepareStatement(sql.toString());
				conn.setAutoCommit(false);
				Integer transCount = 0;
				int i=0;
				for(Row o : sheet) {
					String phone = cell2Str(o.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
					List<CultureCloudMemberEntity> memberList = cultureCloudMemberDao.getListByHql("select a from CultureCloudMemberEntity a "
							+ " where a.phone='"
							+phone+"' ","");
					if(null==memberList || (null!=memberList && memberList.size()<=0)) {
						stmt.setString(1, phone);
						stmt.setString(2, phone);
						stmt.addBatch();
						transCount++;
						if (transCount % 500 == 0) {
							stmt.executeBatch();
							conn.commit();
						}
					}else {
						i++;
					}
				}
				stmt.executeBatch();
				conn.commit();
				String str = i!=0?ResponseContext.RES_SUCCESS_CODE_INFO+"有"+i+"个手机号重复！":ResponseContext.RES_SUCCESS_CODE_INFO;
				res.setRetInfo(str);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
			}
		});

		transaction.commit();

		if (session != null) {
			session.close();
		}


		return res;
	}
	public File uploadFile(HttpServletRequest request) throws IllegalStateException, IOException {
		MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
		MultipartFile file = multiRequest.getFile("file");
		String tmpFile = System.getProperty("java.io.tmpdir");
		//String tmpFile = "F:/temp";
		tmpFile = tmpFile + File.separator + System.currentTimeMillis() + "-"
				+ DigestUtil.getMD5Str(file.getOriginalFilename()) + "-" + CodeUtil.getId(10000)
				+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
		file.transferTo(new File(tmpFile));
		File excelFile = new File(tmpFile);
		
		return excelFile;
	}
	private String cell2Str(Cell cell) {

		String str = "";
		if (cell.getCellType() == CellType.STRING) {
			str = StringUtils.isNotEmpty(cell.getStringCellValue()) ? cell.getStringCellValue() :"";
		} else if (cell.getCellType() == CellType.NUMERIC) {
			str = String.valueOf(cell.getNumericCellValue());
			if(str.contains("E10")) {
				DecimalFormat decimalFormat=new DecimalFormat("#");
				str = decimalFormat.format(Double.valueOf(str));
			}
		}else if(cell.getCellType() == CellType.FORMULA){
			str = cell.getCellFormula();
		}else if(cell.getCellType() == CellType.BOOLEAN){
			str=cell.getBooleanCellValue()+"";
		}else if(cell.getCellType() == CellType.ERROR){
			str=cell.getErrorCellValue()+"";
		}else if(cell.getCellType() == CellType.BLANK || cell.getCellType() == CellType. _NONE){
			str="";
		}else {
			str="";
		}
		return str;
	}	
	
}
