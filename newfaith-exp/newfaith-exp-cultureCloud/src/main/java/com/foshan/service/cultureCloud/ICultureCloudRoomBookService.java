package com.foshan.service.cultureCloud;

import com.foshan.entity.cultureCloud.CultureCloudMemberEntity;
import com.foshan.entity.cultureCloud.CultureCloudRoomBookEntity;
import com.foshan.entity.cultureCloud.CultureCloudRoomEntity;
import com.foshan.entity.cultureCloud.CultureCloudVenueEntity;
import com.foshan.form.cultureCloud.request.CultureCloudRoomBookOrderReq;
import com.foshan.form.cultureCloud.request.CultureCloudRoomBookReq;
import com.foshan.form.cultureCloud.request.CultureCloudRoomReq;
import com.foshan.form.response.IResponse;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface ICultureCloudRoomBookService {

    IResponse queryRoomBookList(CultureCloudRoomBookReq req);

    IResponse preEditRoomBook(CultureCloudRoomBookReq req);

    IResponse editRoomBook(CultureCloudRoomBookReq req);

    IResponse roomAppDetail(CultureCloudRoomReq req);

    IResponse bookCultureCloudRoom(CultureCloudRoomBookOrderReq req);

    /* (non-Javadoc)
     * @see com.sun3d.why.webservice.service.RoomBookAppService#appRoomOrderByCondition(java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
     */
    @Transactional(isolation= Isolation.REPEATABLE_READ)
    IResponse appRoomOrderByCondition(String bookId, String teamUserId, String teamUserName,
                                      String userId, String orderName, String orderTel, String purpose,
                                      String reservationMethod, Integer orderAttendNum, String applicationUrl, String userIdNo,
                                      String urgentOrderReason, String roomAllocationId, String planId,Boolean isWhgdOrder);

    IResponse roomConfirm(CultureCloudRoomBookEntity cmsRoomBook, CultureCloudVenueEntity cmsVenue, CultureCloudRoomEntity cmsActivityRoom,
                          CultureCloudMemberEntity cmsTerminalUser,
                          String purpose, String reservationMethod, Integer orderAttendNum, String applicationUrl, String userIdNo,
                          String urgentOrderReason, String planId,Boolean isWhgdOrder);

    int initRoomBookInfo(List<CultureCloudRoomEntity> roomList);

    void generateRoomBookByDays(CultureCloudRoomEntity cmsActivityRoom, Date baseDate, int days);

    int generateOneDayRoomBook(CultureCloudRoomBookReq req);
}
