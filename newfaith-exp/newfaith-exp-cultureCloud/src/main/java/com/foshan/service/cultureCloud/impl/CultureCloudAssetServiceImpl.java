package com.foshan.service.cultureCloud.impl;

import com.foshan.entity.cultureCloud.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.foshan.entity.AccountEntity;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.ColumnEntity;
import com.foshan.entity.RegionEntity;
import com.foshan.entity.UpshelfColumnEntity;
import com.foshan.entity.UserEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.cultureCloud.vo.OrderNumberVo;
import com.foshan.entity.cultureCloud.vo.OutsideChainVo;
import com.foshan.entity.cultureCloud.vo.VideoVo;
import com.foshan.service.annotation.Audit;
import com.foshan.service.cultureCloud.ICultureCloudAssetService;
import com.foshan.service.cultureCloud.annotation.ChechSensitiveWords;

import org.springframework.transaction.annotation.Transactional;

import com.foshan.form.AssetForm;
import com.foshan.form.RegionForm;
import com.foshan.form.cultureCloud.CultureCloudAssetForm;
import com.foshan.form.cultureCloud.CultureCloudTypeForm;
import com.foshan.form.cultureCloud.CultureCloudUpshelfColumnForm;
import com.foshan.form.cultureCloud.FileForm;
import com.foshan.form.cultureCloud.OutsideChainForm;
import com.foshan.form.cultureCloud.RecommendLinkForm;
import com.foshan.form.cultureCloud.VideoForm;
import com.foshan.form.cultureCloud.request.CultureCloudAssetReq;
import com.foshan.form.cultureCloud.response.asset.AddCultureCloudAssetRes;
import com.foshan.form.cultureCloud.response.asset.GetCultureCloudAssetInfoRes;
import com.foshan.form.cultureCloud.response.asset.GetCultureCloudAssetListRes;
import com.foshan.form.cultureCloud.response.asset.ModifyCultureCloudAssetRes;
import com.foshan.form.request.AssetReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.model.permssion.AccountPrincipalModel;
import com.foshan.model.permssion.PrincipalModel;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.asset.AddAssetRes;
import com.foshan.form.response.asset.GetAssetListRes;
import com.foshan.form.response.asset.ModifyAssetRes;
import com.foshan.util.DateUtil;
import com.foshan.util.DesensitizedUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.foshan.dao.generic.Page;
import org.apache.commons.lang3.StringUtils;

import static java.util.stream.Collectors.groupingBy;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Transactional
@Service("cultureCloudAssetService")
public class CultureCloudAssetServiceImpl extends GenericCultureCloudService implements ICultureCloudAssetService{

	private final static Logger logger = LoggerFactory.getLogger(CultureCloudAssetServiceImpl.class);

	@Override
	public IResponse getCultureCloudAssetList(CultureCloudAssetReq req) {
		GetCultureCloudAssetListRes res = new GetCultureCloudAssetListRes();
//		if(StringUtils.isNoneEmpty(req.getColumnIdList())) {
			 Page<CultureCloudUpshelfColumnEntity> page = new Page<>();
	            page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
	            page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 0) * page.getPageSize());
	            page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
	            
	    		Object userObj = getPrincipal(true);
	    		StringBuilder hql = new StringBuilder("select distinct a from CultureCloudUpshelfColumnEntity a inner "
	    				+ "join a.column c   ");
	    		
	    		hql.append(StringUtils.isNoneEmpty(req.getColumnIdList()) ? "where a.column.id in("+req.getColumnIdList()+
	    				")": " where a.column.id is not null ");
	    		if(null == userObj  || (null != userObj && userObj instanceof CultureCloudMemberEntity)) {
	    			hql.append(" and c.isGlobal in(0,1)  ")
	    				.append(" and a.resourceId "
	    						+ "in(select b.id from CultureCloudAssetEntity b  ")
	    				.append(StringUtils.isNoneEmpty(req.getTypeIdList()) ? " INNER JOIN  b.tagList d ":"")
	    				.append("where")
	    				.append(null!=req.getAuditState() ?"  b.auditState="+req.getAuditState():" b.auditState=3")
	    				.append(null!=req.getAssetState() ?" and b.assetState="+req.getAssetState():" and assetState=2")
	    				.append(StringUtils.isNoneEmpty(req.getUserName()) ? " and  b.userName like'%"+req.getUserName()+"%'" : "")		
	    				.append(StringUtils.isNoneEmpty(req.getAssetName()) ? " and  b.assetName like'%"+req.getAssetName()+"%'" : "")	
	    	    		.append(StringUtils.isNoneEmpty(req.getStartTime()) ? " and b.publishedTime>='"+req.getStartTime()+"'":"")
		    			.append(StringUtils.isNoneEmpty(req.getEndTime()) ? " and b.publishedTime<='"+req.getEndTime()+"'":"")
		    			.append(StringUtils.isNoneEmpty(req.getTypeIdList()) ? " and d.id in("+req.getTypeIdList()+")":"")
	    				.append(")");
	    		}else {
	    			String depIds = getDepartment(userObj);
	    			hql.append(" and a.resourceId "
    						+ "in(select b.id from CultureCloudAssetEntity b ")
	    			.append(StringUtils.isNoneEmpty(req.getTypeIdList()) ? "  INNER JOIN b.tagList d ":"")
	    			.append(" where b.state=1 ")
    				.append(StringUtils.isNoneEmpty(req.getUserName()) ? " and  b.userName like'%"+req.getUserName()+"%'" : "")	
    				.append(StringUtils.isNoneEmpty(req.getAssetName()) ? " and  b.assetName like'%"+req.getAssetName()+"%'" : "")	
    				.append(null!=req.getAuditState() ? " and b.auditState="+req.getAuditState():"")
    	    		.append(StringUtils.isNoneEmpty(req.getStartTime()) ? " and b.publishedTime>='"+req.getStartTime()+"'":"")
	    			.append(StringUtils.isNoneEmpty(req.getEndTime()) ? " and b.publishedTime<='"+req.getEndTime()+"'":"")
	    			.append(StringUtils.isNoneEmpty(req.getTypeIdList()) ? " and d.id in("+req.getTypeIdList()+")":"")
	    			.append(null!=req.getAssetState() ?" and b.assetState="+req.getAssetState():"")
	    			.append(StringUtils.isNoneEmpty(depIds) ? " and b.departmentId in("+depIds+")":"")
							.append(null != req.getNeedSync()? " and b.needSync = " + req.getNeedSync() : "")
							.append(")");
	    			
	    		}

	            hql.append(" ORDER BY a.orderNumber DESC, a.orderTime DESC ");

	            page = cultureCloudUpshelfColumnDao.queryPage(page, hql.toString());
	            res.setTotalResult(page.getTotalCount());
	            res.setPageSize(page.getPageSize());
	            res.setCurrentPage(page.getCurrentPage());
	            res.setTotal(page.getTotalPage());

	            page.getResultList().forEach(o -> {
	            	CultureCloudAssetEntity asset = cultureCloudAssetDao.get(o.getResourceId());//(CultureCloudAssetEntity) o.getAsset();
	            	CultureCloudAssetForm assetForm = getCultureCloudAssetForm(asset,req,o);
	            	assetForm.setUpshelfColumnId(o.getId());
	            	assetForm.setOrderNumber(o.getOrderNumber());
	            	assetForm.setUpshelfTime(null != o.getUpshelfTime() ? 
                        		o.getUpshelfTime().toString().substring(0, o.getUpshelfTime().toString().indexOf(".")) : "");
					assetForm.setHasSynced(asset.getHasSynced());
	            	res.getAssetList().add(assetForm);
	              });
//		}else {
//			Page<CultureCloudAssetEntity> page = new Page<CultureCloudAssetEntity>();
//			page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
//			page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
//			page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
//			StringBuilder hql = new StringBuilder("select distinct a from CultureCloudAssetEntity a where a.state="
//					+EntityContext.RECORD_STATE_VALID);
//    		hql.append(StringUtils.isNoneEmpty(req.getStartTime()) ? " and a.upshelfTime>='"+req.getStartTime()+"'":"")
//				.append(StringUtils.isNoneEmpty(req.getEndTime()) ? " and a.upshelfTime<='"+req.getEndTime()+"'":"")
//				.append(StringUtils.isNoneEmpty(req.getUserName()) ? " and  a.userName like'%"+req.getUserName()+"%'" : "")
//				.append(null!=req.getAuditState() ? " and a.auditState="+req.getAuditState():"");
//			//hql.append(" ORDER BY a.id desc");
//			page = cultureCloudAssetDao.queryPage(page, hql.toString());
//
//			res.setTotalResult(page.getTotalCount());
//			res.setPageSize(page.getPageSize());
//			res.setCurrentPage(page.getCurrentPage());
//			res.setTotal(page.getTotalPage());
//			page.getResultList().forEach(o -> {
//				res.getAssetList().add(getCultureCloudAssetForm(o,req,null));
//			});
//		}

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	public CultureCloudAssetForm getCultureCloudAssetForm(CultureCloudAssetEntity o,CultureCloudAssetReq req, 
			CultureCloudUpshelfColumnEntity upshelfColumn) {
		CultureCloudAssetForm cultureCloudAsset = new CultureCloudAssetForm();
		if(o.getAssetType()==20) {
			cultureCloudAsset.setAssetContent(StringUtils.isNoneEmpty(o.getAssetContent()) ? o.getAssetContent() : "");
		}else if(o.getAssetType()==21) {
			
		}else if(o.getAssetType()==22 || o.getAssetType()==25) {
			VideoVo videoVo= null;
			VideoForm video = new VideoForm();
			if(StringUtils.isNotEmpty(o.getReservedField())) {
				try {
					videoVo =  mapper.readValue(o.getReservedField(), VideoVo.class);
					video.setEndTime(videoVo.getEndTime());
					video.setRecordNumber(videoVo.getRecordNumber());
					video.setStartTime(videoVo.getStartTime());
					video.setVideoName(videoVo.getVideoName());
					video.setVideoUrl(videoVo.getVideoUrl());
					video.setTimeLength(videoVo.getTimeLength());
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			}else {
				video.setEndTime("");
				video.setRecordNumber("");
				video.setStartTime("");
				video.setVideoName("");
				video.setVideoUrl("");
				video.setTimeLength("");
			}
			cultureCloudAsset.setVideoForm(video);
			cultureCloudAsset.setAssetContent(StringUtils.isNoneEmpty(o.getAssetContent()) ? o.getAssetContent() : "");
			if(null != o.getSubFileAssetSet()) {
				for (AssetEntity asset : o.getSubFileAssetSet()) {
					AssetForm assetForm = new AssetForm();
					BeanUtils.copyProperties(asset, assetForm);
					assetForm.setAssetId(asset.getId());
					if(asset.getAssetType()==EntityContext.ASSET_TYPE_VIDEO || asset.getAssetType()==EntityContext.ASSET_TYPE_SOUND) {
						cultureCloudAsset.getFileAssetList().add(assetForm);
					}else {
						cultureCloudAsset.getVideoImageList().add(assetForm);
					}
				}
			}
			
		}else if(o.getAssetType()==23) {
			OutsideChainVo outsideChainVo= null;
			OutsideChainForm outsideChain = new OutsideChainForm();
			if(StringUtils.isNotEmpty(o.getReservedField())) {
				try {
					outsideChainVo =  mapper.readValue(o.getReservedField(), OutsideChainVo.class);
					outsideChain.setH5Url(outsideChainVo.getH5Url());
					outsideChain.setPcUrl(outsideChainVo.getPcUrl());
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			}else {
				outsideChain.setH5Url("");
				outsideChain.setPcUrl("");
			}
			cultureCloudAsset.setOutsideChain(outsideChain);
		}else if(o.getAssetType()==24) {
			cultureCloudAsset.setAssetContent(StringUtils.isNoneEmpty(o.getAssetContent()) ? o.getAssetContent() : "");
			if(null != o.getSubFileAssetSet()) {
				for (AssetEntity asset : o.getSubFileAssetSet()) {
					AssetForm assetForm = new AssetForm();
					BeanUtils.copyProperties(asset, assetForm);
					assetForm.setAssetId(asset.getId());
					cultureCloudAsset.getFileAssetList().add(assetForm);
				}
			}
		}
		if(null!=o.getRegion()) {
//			RegionForm region = new RegionForm();
//			BeanUtils.copyProperties(o.getRegion(), region);
//			region.setRegionId(o.getRegion().getId());
//			cultureCloudAsset.setRegiom(region);
			cultureCloudAsset.setRegiom(getParentRegionForm(o.getRegion(),null));
		}
		
		Map<Integer, List<CultureCloudTagEntity>> collect1 = (Map<Integer, List<CultureCloudTagEntity>>) o.getTagList()
				.parallelStream().collect(groupingBy(CultureCloudTagEntity::getCategory));
		for(Integer key : collect1.keySet()) {
			StringBuilder tags = new StringBuilder();
			if(key == 0) {
				for(CultureCloudTagEntity t : collect1.get(key)) {
					tags.append(t.getTagName()+",");
				}
				cultureCloudAsset.setTags(tags.substring(0, tags.length()-1).toString());
			}else if(key ==1) {
				for(CultureCloudTagEntity t : collect1.get(key)) {
					tags.append(t.getTagName()+",");
				}
				cultureCloudAsset.setKeywords(tags.substring(0, tags.length()-1).toString());
			}else if(key ==2) {
				for(CultureCloudTagEntity t : collect1.get(key)) {
					CultureCloudTypeForm cultureCloudTypeForm = new CultureCloudTypeForm();
					cultureCloudTypeForm.setCultureCloudTypeId(t.getId());
		            cultureCloudTypeForm.setCategory(t.getCategory());
		            cultureCloudTypeForm.setTypeName(t.getTagName());
		            cultureCloudAsset.getTypeList().add(cultureCloudTypeForm);
				}

			}
			
		}
		cultureCloudAsset.setContentType(o.getContentType());
		cultureCloudAsset.setAssetId(o.getId());
		cultureCloudAsset.setAssetName(StringUtils.isNoneEmpty(o.getAssetName()) ? o.getAssetName() : "");
		cultureCloudAsset.setUserName(StringUtils.isNoneEmpty(o.getUserName()) ? o.getUserName() : "");
		cultureCloudAsset.setAuditState(o.getAuditState());
		cultureCloudAsset.setSource(StringUtils.isNoneEmpty(o.getSource()) ? o.getSource() : "");
		cultureCloudAsset.setAffiliatedUnit(StringUtils.isNoneEmpty(o.getAffiliatedUnit()) ? o.getAffiliatedUnit() : "");
		cultureCloudAsset.setDepartmentId(o.getDepartmentId());
		cultureCloudAsset.setAssetState(o.getAssetState());
		cultureCloudAsset.setAuditState(o.getAuditState());
		//cultureCloudAsset.setTags(o.getTags());
//		if(null!=o.getTypeList()) {
//			for (CultureCloudTypeEntity type : o.getTypeList()) {
//				CultureCloudTypeForm cultureCloudTypeForm = new CultureCloudTypeForm();
//				cultureCloudTypeForm.setCultureCloudTypeId(type.getId());
//	            cultureCloudTypeForm.setCategory(type.getCategory());
//	            cultureCloudTypeForm.setTypeName(type.getTypeName());
//	            cultureCloudAsset.getTypeList().add(cultureCloudTypeForm);
//			}
//		}
		if(null != o.getSubAttachmentAssetSet()) {
			for (AssetEntity asset : o.getSubAttachmentAssetSet()) {
				AssetForm assetForm = new AssetForm();
				BeanUtils.copyProperties(asset, assetForm);
				assetForm.setAssetId(asset.getId());
				cultureCloudAsset.getAttachmentAssetList().add(assetForm);
			}
		}
		
		if(null!=o.getSubAssetSet()) {
			for (AssetEntity asset : o.getSubAssetSet()) {
				AssetForm assetForm = new AssetForm();
				BeanUtils.copyProperties(asset, assetForm);
				assetForm.setAssetId(asset.getId());
				cultureCloudAsset.getResourcesList().add(assetForm);
			}
		}
		if(null!=o.getSubImageSet()) {
			for (AssetEntity asset : o.getSubImageSet()) {
				AssetForm assetForm = new AssetForm();
				BeanUtils.copyProperties(asset, assetForm);
				assetForm.setAssetId(asset.getId());
				cultureCloudAsset.getImageList().add(assetForm);
			}
		}
		//cultureCloudAsset.setTags(StringUtils.isNoneEmpty(o.getTags()) ? o.getTags() : "");
		cultureCloudAsset.setSummaryShort(StringUtils.isNoneEmpty(o.getSummaryShort()) ? o.getSummaryShort() : "");
		cultureCloudAsset.setSponsor(StringUtils.isNoneEmpty(o.getSponsor()) ? o.getSponsor() : "");
    	cultureCloudAsset.setPublishedTime(null!=o.getPublishedTime() ? 
            DateUtil.format(o.getPublishedTime(),1) : null);
		cultureCloudAsset.setLastModifyTime(DateUtil.format(o.getLastModifyTime(),1));
		cultureCloudAsset.setState(EntityContext.RECORD_STATE_VALID);
		cultureCloudAsset.setAssetCode(StringUtils.isNoneEmpty(o.getAssetCode()) ? o.getAssetCode() : 
			String.valueOf(System.currentTimeMillis()));
		cultureCloudAsset.setAssetState(o.getAssetState());
		cultureCloudAsset.setAssetType(o.getAssetType());
		cultureCloudAsset.setAuditState(o.getAuditState());
		if(null != upshelfColumn) {
			cultureCloudAsset.getUpshelfColumnList().add(getUpshelfColumn(upshelfColumn));
		}else {
			StringBuilder hql = new StringBuilder("select a from CultureCloudUpshelfColumnEntity a where a.resourceId="+o.getId());
			hql.append(StringUtils.isNoneEmpty(req.getColumnIdList()) ? " and a.column.id in("+req.getColumnIdList()+")":"");
			List<CultureCloudUpshelfColumnEntity> list = cultureCloudUpshelfColumnDao.getListByHql(hql.toString(), "");
			list.forEach(p->{
					cultureCloudAsset.getUpshelfColumnList().add(getUpshelfColumn(p));
			});
		}
		cultureCloudAsset.setRecommendTitle(o.getRecommendTitle());
		o.getRecommendLinkSet().forEach(p->{
			RecommendLinkForm recommendLink = new RecommendLinkForm();
			recommendLink.setRecommendLinkId(p.getId());
			recommendLink.setTitle(p.getAssetName());
			recommendLink.setLinkUrl(p.getContentUrl());
			p.getSubAssetSet().forEach(f->{
				FileForm file = new FileForm();
				file.setFileId(f.getId());
				file.setFileName(f.getAssetName());
				file.setFileUrl(f.getImageFile());
				recommendLink.getFileList().add(file);
			});
			cultureCloudAsset.getRecommendLinkList().add(recommendLink);
		});
		cultureCloudAsset.setContentUrl(o.getContentUrl());
		return cultureCloudAsset;
	}
	
	public RegionForm getParentRegionForm(RegionEntity region ,RegionForm subRegion) {
		RegionForm regionForm = getRegionForm(region,subRegion);
		if(null!=region.getParentRegion() && region.getParentRegion().getRegionLevel()!=0) {
			//RegionForm parentRegionForm = getRegionForm(region.getParentRegion(),regionForm);
			return getParentRegionForm(region.getParentRegion(),regionForm);
		}else {
			return regionForm;
		}
	}
	
	
	
	public RegionForm getRegionForm(RegionEntity region,RegionForm subRegion) {
		RegionForm regionForm = new RegionForm();
		BeanUtils.copyProperties(region, regionForm);
		regionForm.setRegionId(region.getId());
		if(null != subRegion) {
			regionForm.getSubRegionList().add(subRegion);
		}
		return regionForm;
	}
	
	

	
	
	public CultureCloudUpshelfColumnForm getUpshelfColumn(CultureCloudUpshelfColumnEntity p) {
		CultureCloudUpshelfColumnForm upshelfColumnForm = new CultureCloudUpshelfColumnForm(null,p.getId(),p.getIsRecommend(),
				p.getOrderNumber(),p.getResourceId(),new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").
				format(p.getUpshelfTime()),
				p.getTerminalType(),p.getType()) ;
		upshelfColumnForm.setRemarks(p.getRemarks());
		upshelfColumnForm.setTargetUrl(p.getTargetUrl());
		upshelfColumnForm.setTitle(p.getTitle());
		upshelfColumnForm.setOrderTime(null !=p.getOrderTime() ?  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").
				format(p.getOrderTime()) :"");
		return upshelfColumnForm;
	}

	@Override
	@ChechSensitiveWords
	public IResponse addCultureCloudAsset(CultureCloudAssetReq req) {
		AddCultureCloudAssetRes res = new AddCultureCloudAssetRes();
		Object userObj = getPrincipal(true);
		String depIds = getDepartment(userObj);
		if(null!=req.getDepartmentId() && StringUtils.isNoneEmpty(depIds) ) {
			 List<String> depIdList = Stream.of(depIds.split(","))  
		                .collect(Collectors.toList());
			 if(!depIdList.contains(req.getDepartmentId().toString())) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("对不起，不能操作除本部门及子部门以外的数据！");
					return res;
			 }
		}
		
		if (null!=req.getAssetType()) {
			CultureCloudAssetEntity cultureCloudAsset = new CultureCloudAssetEntity();
			if(req.getAssetType()==20) {
				cultureCloudAsset.setAssetContent(StringUtils.isNoneEmpty(req.getAssetContent()) ? req.getAssetContent() : "");
			}else if(req.getAssetType()==21) {
				
			}else if(req.getAssetType()==22 || req.getAssetType()==25) {
				cultureCloudAsset.setAssetContent(StringUtils.isNoneEmpty(req.getAssetContent()) ? req.getAssetContent() : "");
				VideoVo video = new VideoVo();
				cultureCloudAsset.setContentType(req.getContentType());
				if(req.getContentType()==1) {
					video.setVideoName(StringUtils.isNotEmpty(req.getVideoName()) ? 
							req.getVideoName():"");
					video.setStartTime(StringUtils.isNotEmpty(req.getStartTime()) ? 
							req.getStartTime():"");
					video.setEndTime(StringUtils.isNotEmpty(req.getEndTime()) ? 
							req.getEndTime() : "");
					if(null!=req.getSmallImageId()) {
						AssetEntity smallImage = assetDao.get(req.getSmallImageId());
						cultureCloudAsset.getSubFileAssetSet().add(smallImage);
					}
					if(StringUtils.isNoneEmpty(req.getFileIdList())) {
						String[] fileIds = req.getFileIdList().split(",");
						for (String fileId : fileIds) {
							AssetEntity file = assetDao.get(Integer.parseInt(fileId));
							cultureCloudAsset.getSubFileAssetSet().add(file);
						}
					}

					video.setVideoUrl("");
				}else if(req.getContentType()==3) {
					video.setVideoUrl(StringUtils.isNotEmpty(req.getVideoUrl()) ? req.getVideoUrl() :"");
				}
				video.setRecordNumber(StringUtils.isNotEmpty(req.getRecordNumber()) ? 
						req.getRecordNumber():"");
				video.setTimeLength(StringUtils.isNotEmpty(req.getTimeLength()) ? req.getTimeLength() :"");
				String json="";
				try {
					json = mapper.writeValueAsString(video);
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
				cultureCloudAsset.setReservedField(json);
				
			}else if(req.getAssetType()==23) {
				OutsideChainVo outsideChainVo = new OutsideChainVo();
				outsideChainVo.setH5Url(StringUtils.isNoneEmpty(req.getH5Url()) ? req.getH5Url() : "");
				outsideChainVo.setPcUrl(StringUtils.isNoneEmpty(req.getPcUrl()) ? req.getPcUrl() : "");
				String json="";
				try {
					json = mapper.writeValueAsString(outsideChainVo);
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
				cultureCloudAsset.setReservedField(json);
			}else if(req.getAssetType()==24) {
				cultureCloudAsset.setAssetContent(StringUtils.isNoneEmpty(req.getAssetContent()) ? req.getAssetContent() : "");
				if(StringUtils.isNoneEmpty(req.getFileIdList())) {
					String[] fileIds = req.getFileIdList().split(",");
					for (String fileId : fileIds) {
						AssetEntity file = assetDao.get(Integer.parseInt(fileId));
						cultureCloudAsset.getSubFileAssetSet().add(file);
					}
				}
			}else {
				cultureCloudAsset.setContentUrl(StringUtils.isNoneEmpty(req.getContentUrl()) ? req.getContentUrl():"");
			}
//			else {
//				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
//				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
//				return res;
//			}
			cultureCloudAsset.setRegion(null!=req.getRegionId() ? regionDao.get(req.getRegionId()) : null);
			cultureCloudAsset.setAssetName(StringUtils.isNoneEmpty(req.getAssetName()) ? req.getAssetName() : "");
			cultureCloudAsset.setUserName(StringUtils.isNoneEmpty(req.getUserName()) ? req.getUserName() : "");
			cultureCloudAsset.setAuditState(0);
			cultureCloudAsset.setSource(StringUtils.isNoneEmpty(req.getSource()) ? req.getSource() : "");
			cultureCloudAsset.setAffiliatedUnit(StringUtils.isNoneEmpty(req.getAffiliatedUnit()) ? req.getAffiliatedUnit() : "");
			
			cultureCloudAsset.setBroadcastCount(null!=req.getBroadcastCount() ? 
					req.getBroadcastCount() :0);
			cultureCloudAsset.setState(EntityContext.RECORD_STATE_VALID);

			if(StringUtils.isNoneEmpty(req.getAttachmentIdList())) {
				String[] attachmentIds = req.getAttachmentIdList().split(",");
				for (String attachmentId : attachmentIds) {
					AssetEntity attachment = assetDao.get(Integer.parseInt(attachmentId));
					cultureCloudAsset.getSubAttachmentAssetSet().add(attachment);
				}
			}
			
			if(null!=req.getImageId()) {
				AssetEntity image = assetDao.get(req.getImageId());
				if(null!=image) {
					cultureCloudAsset.getSubImageSet().add(image);
				}
			}
			String displayTypeHql = "";
			if(null!=req.getColumnType() && 0==req.getColumnType()) {
				displayTypeHql = " and displayType=1";
				cultureCloudAsset.setTags("新闻动态");
			}else if(null!=req.getColumnType() && 1==req.getColumnType()) {
				displayTypeHql = " and displayType=5";
				cultureCloudAsset.setTags("旅游景点");
			} else if(null!=req.getColumnType() && 2==req.getColumnType()) {
				displayTypeHql = " and displayType=6";
				cultureCloudAsset.setTags("景点资讯");
			}else {
				displayTypeHql = " and displayType=1";
				cultureCloudAsset.setTags("新闻动态");
			}
			if(StringUtils.isNoneEmpty(req.getTags())) {
				String[] tags = req.getTags().split(",");
				for (String tag : tags) {
					CultureCloudTagEntity cultureCloudTag = cultureCloudTagDao.getUniqueByHql(
							"from CultureCloudTagEntity a where a.category=0 and a.tagName='"+tag+"'"+displayTypeHql, "");
					if(null == cultureCloudTag) {
						cultureCloudTag = new CultureCloudTagEntity();
						cultureCloudTag.setTagName(tag);
						cultureCloudTag.setCategory(0);
						cultureCloudTag.setDisplayType(1);
						cultureCloudTagDao.save(cultureCloudTag);
					}
					cultureCloudAsset.getTagList().add(cultureCloudTag);
				}
			}
			if(StringUtils.isNoneEmpty(req.getKeywords())) {
				String[] keywords = req.getKeywords().split(",");
				for (String keyword : keywords) {
					CultureCloudTagEntity cultureCloudTag = cultureCloudTagDao.getUniqueByHql(
							"from CultureCloudTagEntity a where a.category=1 and a.tagName='"+keyword+"'"+displayTypeHql, "");
					if(null == cultureCloudTag) {
						cultureCloudTag = new CultureCloudTagEntity();
						cultureCloudTag.setTagName(keyword);
						cultureCloudTag.setCategory(1);
						cultureCloudTag.setDisplayType(1);
						cultureCloudTagDao.save(cultureCloudTag);
					}
					cultureCloudAsset.getTagList().add(cultureCloudTag);
				}
			}
			
			if(StringUtils.isNoneEmpty(req.getTypeIdList())) {
				String[] typeIds = req.getTypeIdList().split(",");
				//List<CultureCloudTypeEntity> typeList = new ArrayList<CultureCloudTypeEntity>();
				for (String typeId : typeIds) {
					cultureCloudAsset.getTagList().add(cultureCloudTagDao.get(Integer.parseInt(typeId)));
				}
				//cultureCloudAsset.setTypeList(typeList);
			}
			
			cultureCloudAsset.setSummaryShort(StringUtils.isNoneEmpty(req.getSummaryShort()) ? req.getSummaryShort() : "");
			cultureCloudAsset.setSponsor(StringUtils.isNoneEmpty(req.getSponsor()) ? req.getSponsor() : "");
			
            try {
            	cultureCloudAsset.setPublishedTime(StringUtils.isNotEmpty(req.getPublishedTime()) ? 
                    new Timestamp(DateUtil.parseLongFormat(req.getPublishedTime()).getTime()) : null);
            } catch (ParseException e) {
                e.printStackTrace();
            }
			cultureCloudAsset.setLastModifyTime(new Timestamp(new Date().getTime()));
			cultureCloudAsset.setState(EntityContext.RECORD_STATE_VALID);
			cultureCloudAsset.setAssetCode(StringUtils.isNoneEmpty(req.getAssetCode()) ? req.getAssetCode() : 
				String.valueOf(System.currentTimeMillis()));
			cultureCloudAsset.setAssetState(0);
			cultureCloudAsset.setAssetType(req.getAssetType());
			cultureCloudAsset.setAuditState(0);
			cultureCloudAsset.setRecommendTitle(StringUtils.isNoneEmpty(req.getRecommendTitle()) ? 
					req.getRecommendTitle() : "");
			if(null!=req.getRecommendLinkList() && req.getRecommendLinkList().size()>0) {
				for(RecommendLinkForm recommendLinkForm : req.getRecommendLinkList()) {
					CultureCloudAssetEntity recommendLink = new CultureCloudAssetEntity();
					recommendLinkForm.getFileList().forEach(o->{
						AssetEntity image = assetDao.get(o.getFileId());
						if(null!= image) {
							recommendLink.getSubAssetSet().add(image);
						}
					});
					
					recommendLink.setAssetName(StringUtils.isNoneEmpty(recommendLinkForm.getTitle()) ? 
							recommendLinkForm.getTitle() : "");
					recommendLink.setContentUrl(StringUtils.isNoneEmpty(recommendLinkForm.getLinkUrl()) ? 
							recommendLinkForm.getLinkUrl() : "");
					recommendLink.setContentType(4);
					cultureCloudAsset.getRecommendLinkSet().add(recommendLink);
					recommendLink.setAssetCode(String.valueOf(System.currentTimeMillis()));
					cultureCloudAssetDao.save(recommendLink);
				}
			}
			if(null!=req.getDepartmentId()){
				cultureCloudAsset.setDepartmentId(req.getDepartmentId());
			}else {

				if(null!=userObj && !(userObj instanceof AccountPrincipalModel) && !(userObj instanceof AccountEntity)) {
					CultureCloudUserEntity user = (CultureCloudUserEntity) userObj;
					cultureCloudAsset.setDepartmentId(user.getDepartment().getId());
				}
			}
            cultureCloudAssetDao.save(cultureCloudAsset);
            
			if(StringUtils.isNoneEmpty(req.getColumnIdList())) {
				String[] columnIds = req.getColumnIdList().split(",");
				for (String columnId : columnIds) {
					ColumnEntity column = columnDao.get(Integer.valueOf(columnId));
		            if (null != column) {
		            	CultureCloudUpshelfColumnEntity upshelfColumnEntity = new CultureCloudUpshelfColumnEntity();
		                upshelfColumnEntity.setColumn(column);
		                upshelfColumnEntity.setResourceId(cultureCloudAsset.getId());
		                upshelfColumnEntity.setType(12);
		                upshelfColumnEntity.setIsTop(0);
		                upshelfColumnEntity.setAsset(cultureCloudAsset);
		                upshelfColumnEntity.setTerminalType(7);
		                cultureCloudUpshelfColumnDao.save(upshelfColumnEntity);
//		        		if (null == req.getOrderNumber()) {//如果排序不传默认值，默认此栏目下最大排序+1；
		        			try {
		        				OrderNumberVo orderNumberVo = orderNumberVoDao.getUniqueByHql("SELECT new com.foshan.entity."
		        					+ "cultureCloud.vo.OrderNumberVo(MAX(a.orderNumber) "
		        					+ "AS orderNumber) FROM UpshelfColumnEntity a WHERE a.column.id="+column.getId()+" AND "
		        					+ "a.id!="+upshelfColumnEntity.getId()+
		        					" ORDER BY a.orderNumber DESC, a.upshelfTime DESC ", "");
		        				upshelfColumnDao.executeUpdate(
		        					"UPDATE UpshelfColumnEntity b SET b.orderNumber="+(null!=orderNumberVo.getOrderNumber() 
		        					? orderNumberVo.getOrderNumber():0)+"+1 WHERE b.id =" + 
		        					upshelfColumnEntity.getId());
		        			} catch (Exception e) {
		        				e.printStackTrace();
		        				upshelfColumnEntity.setOrderNumber(1);
		        			} 
//		        		}
		            }
				}
			}	
			
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@ChechSensitiveWords
	public IResponse modifyCultureCloudAsset(CultureCloudAssetReq req) {
		ModifyCultureCloudAssetRes res = new ModifyCultureCloudAssetRes();
		if (null!=req.getAssetId() ) {
			CultureCloudAssetEntity cultureCloudAsset = cultureCloudAssetDao.get(req.getAssetId()) ;
			Object userObj = getPrincipal(true);
			String depIds = getDepartment(userObj);
			if(StringUtils.isNoneEmpty(depIds) ) {
				 List<String> depIdList = Stream.of(depIds.split(","))  
			                .collect(Collectors.toList());
				 if((null!=req.getDepartmentId()&&!depIdList.contains(req.getDepartmentId().toString())) ||
						 (!depIdList.contains(cultureCloudAsset.getDepartmentId().toString()))) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo("对不起，不能操作除本部门及子部门以外的数据！");
						return res;
				 }
			}
			if(null != cultureCloudAsset){
				if(cultureCloudAsset.getAuditState()==1 && cultureCloudAsset.getAuditState()==3) {
					res.setRet(ResponseContext.RES_IS_SHELVES_CODE);
					res.setRetInfo("提交审核或上架的数据不能修改！");
					return res;
				}
				if(req.getAssetType()==20) {
					cultureCloudAsset.setAssetContent(StringUtils.isNoneEmpty(req.getAssetContent()) ? req.getAssetContent() : 
						cultureCloudAsset.getAssetContent());
				}else if(req.getAssetType()==21) {
					
				}else if(req.getAssetType()==22 || req.getAssetType()==25) {
					cultureCloudAsset.setAssetContent(StringUtils.isNoneEmpty(req.getAssetContent()) ? req.getAssetContent() : 
						cultureCloudAsset.getAssetContent());
					VideoVo video = new VideoVo();
					cultureCloudAsset.setContentType(req.getContentType());
					if(req.getContentType()==1) {
						video.setVideoName(req.getVideoName());
						video.setStartTime(StringUtils.isNotEmpty(req.getStartTime()) ? 
								req.getStartTime():"");
						video.setEndTime(StringUtils.isNotEmpty(req.getEndTime()) ? 
								req.getEndTime() : "");
						cultureCloudAsset.getSubFileAssetSet().clear();
						if(null!=req.getSmallImageId()) {
							AssetEntity smallImage = assetDao.get(req.getSmallImageId());
							cultureCloudAsset.getSubFileAssetSet().add(smallImage);
						}
						if(StringUtils.isNoneEmpty(req.getFileIdList())) {
							String[] fileIds = req.getFileIdList().split(",");
							for (String fileId : fileIds) {
								AssetEntity file = assetDao.get(Integer.parseInt(fileId));
								cultureCloudAsset.getSubFileAssetSet().add(file);
							}
						}
						video.setVideoUrl("");
					}else if(req.getContentType()==3) {
						video.setVideoUrl(StringUtils.isNotEmpty(req.getVideoUrl()) ? req.getVideoUrl() :"");
					}
					video.setRecordNumber(StringUtils.isNotEmpty(req.getRecordNumber()) ? req.getRecordNumber() :"");
					video.setTimeLength(StringUtils.isNotEmpty(req.getTimeLength()) ? req.getTimeLength() :"");
					String json="";
					try {
						json = mapper.writeValueAsString(video);
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
					cultureCloudAsset.setReservedField(json);
					
				}else if(req.getAssetType()==23) {
					OutsideChainVo outsideChainVo = new OutsideChainVo();
					outsideChainVo.setH5Url(StringUtils.isNoneEmpty(req.getH5Url()) ? req.getH5Url() : "");
					outsideChainVo.setPcUrl(StringUtils.isNoneEmpty(req.getPcUrl()) ? req.getPcUrl() : "");
					String json="";
					try {
						json = mapper.writeValueAsString(outsideChainVo);
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
					cultureCloudAsset.setReservedField(json);
				}else if(req.getAssetType()==24) {
					cultureCloudAsset.setAssetContent(StringUtils.isNoneEmpty(req.getAssetContent()) ? req.getAssetContent() : 
						cultureCloudAsset.getAssetContent());
					if(StringUtils.isNoneEmpty(req.getFileIdList())) {
						cultureCloudAsset.getSubFileAssetSet().clear();
						String[] fileIds = req.getFileIdList().split(",");
						for (String fileId : fileIds) {
							AssetEntity file = assetDao.get(Integer.parseInt(fileId));
							cultureCloudAsset.getSubFileAssetSet().add(file);
						}
					}
				}else {
					cultureCloudAsset.setContentUrl(StringUtils.isNoneEmpty(req.getContentUrl()) ? 
							req.getContentUrl():cultureCloudAsset.getContentUrl());
				}
//				else {
//					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
//					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
//					return res;
//				}
				cultureCloudAsset.setRegion(null!=req.getRegionId() ? regionDao.get(req.getRegionId()) : 
					cultureCloudAsset.getRegion());
				cultureCloudAsset.setAssetName(StringUtils.isNoneEmpty(req.getAssetName()) ? req.getAssetName() : 
					cultureCloudAsset.getAssetName());
				cultureCloudAsset.setUserName(StringUtils.isNoneEmpty(req.getUserName()) ? req.getUserName() : 
					cultureCloudAsset.getUserName());
				cultureCloudAsset.setAuditState(0);
				cultureCloudAsset.setSource(StringUtils.isNoneEmpty(req.getSource()) ? req.getSource() : 
					cultureCloudAsset.getSource());
				cultureCloudAsset.setAffiliatedUnit(StringUtils.isNoneEmpty(req.getAffiliatedUnit()) ? req.getAffiliatedUnit() : 
					cultureCloudAsset.getAffiliatedUnit());
				cultureCloudAsset.setBroadcastCount(null!=req.getBroadcastCount() ? 
						req.getBroadcastCount() :cultureCloudAsset.getBroadcastCount());
				cultureCloudAsset.setDepartmentId(null!=req.getDepartmentId() ? req.getDepartmentId():cultureCloudAsset.getDepartmentId());
				if(StringUtils.isNoneEmpty(req.getTypeIdList())) {
					cultureCloudAsset.getTypeList().clear();
					String[] typeIds = req.getTypeIdList().split(",");
					List<CultureCloudTypeEntity> typeList = new ArrayList<CultureCloudTypeEntity>();
					for (String typeId : typeIds) {
						typeList.add(cultureCloudTypeDao.get(Integer.parseInt(typeId)));
					}
					cultureCloudAsset.setTypeList(typeList);
				}
				if(StringUtils.isNoneEmpty(req.getAttachmentIdList())) {
					cultureCloudAsset.getSubAttachmentAssetSet().clear();
					String[] attachmentIds = req.getAttachmentIdList().split(",");
					for (String attachmentId : attachmentIds) {
						AssetEntity attachment = assetDao.get(Integer.parseInt(attachmentId));
						cultureCloudAsset.getSubAttachmentAssetSet().add(attachment);
					}
				}
				String displayTypeHql = "";
				if(null!=req.getColumnType() && 0==req.getColumnType()) {
					displayTypeHql = " and a.displayType=1";
					cultureCloudAsset.setTags("新闻动态");
				}else if(null!=req.getColumnType() && 1==req.getColumnType()) {
					displayTypeHql = " and a.displayType=5";
					cultureCloudAsset.setTags("旅游景点");
				} else if(null!=req.getColumnType() && 2==req.getColumnType()) {
					displayTypeHql = " and a.displayType=6";
					cultureCloudAsset.setTags("景点资讯");
				}else {
					displayTypeHql = " and a.displayType=1";
					cultureCloudAsset.setTags("新闻动态");
				}
				
				List<CultureCloudTagEntity> tagList = new ArrayList<CultureCloudTagEntity>();
				if(StringUtils.isNoneEmpty(req.getTags())) {
					String[] tags = req.getTags().split(",");
					for (String tag : tags) {
						CultureCloudTagEntity cultureCloudTag = cultureCloudTagDao.getUniqueByHql(
								"from CultureCloudTagEntity a where  a.category=0 and a.tagName='"+tag+"'"+displayTypeHql, "");
						if(null == cultureCloudTag) {
							cultureCloudTag = new CultureCloudTagEntity();
							cultureCloudTag.setTagName(tag);
							cultureCloudTag.setCategory(0);
							cultureCloudTag.setDisplayType(1);
							cultureCloudTagDao.save(cultureCloudTag);
						}
						tagList.add(cultureCloudTag);
					}
				}else {
					List<CultureCloudTagEntity> cultureCloudTagList = cultureCloudTagDao.getListByHql(
							"from CultureCloudTagEntity a inner join a.assetList b  where  a.category=0 and b.id='"
									+cultureCloudAsset.getId()+"'"+displayTypeHql, "");
					for(CultureCloudTagEntity o : cultureCloudTagList) {
						tagList.add(o);
					}
				}
				if(StringUtils.isNoneEmpty(req.getKeywords())) {
					String[] keywords = req.getKeywords().split(",");
					for (String keyword : keywords) {
						CultureCloudTagEntity cultureCloudTag = cultureCloudTagDao.getUniqueByHql(
								"from CultureCloudTagEntity a where  a.category=1 and a.tagName='"+keyword+"'"+displayTypeHql, "");
						if(null == cultureCloudTag) {
							cultureCloudTag = new CultureCloudTagEntity();
							cultureCloudTag.setTagName(keyword);
							cultureCloudTag.setCategory(0);
							cultureCloudTag.setDisplayType(1);
							cultureCloudTagDao.save(cultureCloudTag);
						}
						tagList.add(cultureCloudTag);
					}
				}else {
					List<CultureCloudTagEntity> cultureCloudTagList = cultureCloudTagDao.getListByHql(
							"from CultureCloudTagEntity a inner join a.assetList b  where a.category=1 and b.id='"+
					cultureCloudAsset.getId()+"'"+displayTypeHql, "");
					for(CultureCloudTagEntity o : cultureCloudTagList) {
						tagList.add(o);
					}
				}
				if(StringUtils.isNoneEmpty(req.getTypeIdList())) {
					String[] typeIds = req.getTypeIdList().split(",");
					for (String typeId : typeIds) {
						tagList.add(cultureCloudTagDao.get(Integer.parseInt(typeId)));
					}
				}else {
					List<CultureCloudTagEntity> cultureCloudTypeList = cultureCloudTagDao.getListByHql(
							"from CultureCloudTagEntity a inner join a.assetList b  where  a.category=2 and b.id='"+
					cultureCloudAsset.getId()+"'"+displayTypeHql, "");
					for(CultureCloudTagEntity o : cultureCloudTypeList) {
						tagList.add(o);
					}
				}
				cultureCloudAsset.getTagList().clear();
				cultureCloudAsset.setTagList(tagList);
				if(null!=req.getImageId()) {
					AssetEntity image = assetDao.get(req.getImageId());
					cultureCloudAsset.getSubImageSet().clear();
					if(null!=image) {
						cultureCloudAsset.getSubImageSet().add(image);
					}
				}
				cultureCloudAsset.setTags(StringUtils.isNoneEmpty(req.getTags()) ? req.getTags() : 
					cultureCloudAsset.getTags());
				cultureCloudAsset.setSummaryShort(StringUtils.isNoneEmpty(req.getSummaryShort()) ? req.getSummaryShort() : 
					cultureCloudAsset.getSummaryShort());
				cultureCloudAsset.setSponsor(StringUtils.isNoneEmpty(req.getSponsor()) ? req.getSponsor() : 
					cultureCloudAsset.getSponsor());
				
	            try {
	            	cultureCloudAsset.setPublishedTime(StringUtils.isNotEmpty(req.getPublishedTime()) ? 
	                    new Timestamp(DateUtil.parseLongFormat(req.getPublishedTime()).getTime()) : 
	                    	cultureCloudAsset.getPublishedTime());
	            } catch (ParseException e) {
	                e.printStackTrace();
	            }
				cultureCloudAsset.setRecommendTitle(StringUtils.isNoneEmpty(req.getRecommendTitle()) ? 
						req.getRecommendTitle() : cultureCloudAsset.getRecommendTitle());
				if(null!=req.getRecommendLinkList() && req.getRecommendLinkList().size()>0) {
					cultureCloudAsset.getRecommendLinkSet().clear();
					for(RecommendLinkForm recommendLinkForm : req.getRecommendLinkList()) {
						CultureCloudAssetEntity recommendLink = null; 
						if(null != recommendLinkForm.getRecommendLinkId()) {
							recommendLink = cultureCloudAssetDao.get(recommendLinkForm.getRecommendLinkId());
						}else {
							recommendLink = new CultureCloudAssetEntity();
							recommendLink.setAssetCode(String.valueOf(System.currentTimeMillis()));
						}
						for(FileForm o :recommendLinkForm.getFileList()){
							AssetEntity image = assetDao.get(o.getFileId());
							if(null!= image) {
								recommendLink.getSubAssetSet().add(image);
							}
						}
						recommendLink.setAssetName(StringUtils.isNoneEmpty(recommendLinkForm.getTitle()) ? 
								recommendLinkForm.getTitle() : "");
						recommendLink.setContentUrl(StringUtils.isNoneEmpty(recommendLinkForm.getLinkUrl()) ? 
								recommendLinkForm.getLinkUrl() : "");
						recommendLink.setContentType(4);
						cultureCloudAsset.getRecommendLinkSet().add(recommendLink);
						if(null == recommendLinkForm.getRecommendLinkId()) {
							cultureCloudAssetDao.save(recommendLink);
						}
					}
				}
				
				
				cultureCloudAsset.setLastModifyTime(new Timestamp(new Date().getTime()));
				List<CultureCloudUpshelfColumnEntity> upshelfColumnList = cultureCloudUpshelfColumnDao.getListByHql("select a from UpshelfColumnEntity"
						+ " a where a.resourceId="+cultureCloudAsset.getId(), "");
				if(StringUtils.isNoneEmpty(req.getColumnIdList())) {
					String[] columnIds = req.getColumnIdList().split(",");
					List<String> newIdList = Arrays.asList(columnIds);
					List<String> oldIdlist = new ArrayList<>();
					for(Iterator<CultureCloudUpshelfColumnEntity> iterator = upshelfColumnList.iterator(); iterator.hasNext(); ){ //把不关联上架栏目的删除，并且所有之前上架栏目ID查出
						UpshelfColumnEntity o = iterator.next();
					//for(UpshelfColumnEntity o : upshelfColumnList) {
						String columnId = o.getColumn().getId().toString();
						if(!oldIdlist.contains(columnId)) {
							oldIdlist.add(columnId);
						}
						if(!newIdList.contains(columnId)) {
							upshelfColumnDao.delete(o);
						}
					}
					for(String columnId : newIdList) {
						if(!oldIdlist.contains(columnId)) {//保存上架不包含的栏目
							ColumnEntity column = columnDao.get(Integer.valueOf(columnId));
				            if (null != column) {
				            	CultureCloudUpshelfColumnEntity upshelfColumnEntity = new CultureCloudUpshelfColumnEntity();
				                upshelfColumnEntity.setColumn(column);
				                upshelfColumnEntity.setResourceId(cultureCloudAsset.getId());
				                upshelfColumnEntity.setType(12);
				                upshelfColumnEntity.setIsTop(0);
				                upshelfColumnEntity.setAsset(cultureCloudAsset);
				                upshelfColumnEntity.setTerminalType(7);
				                cultureCloudUpshelfColumnDao.save(upshelfColumnEntity);
//				        		if (null == req.getOrderNumber()) {//如果排序不传默认值，默认此栏目下最大排序+1；
				        			try {
				        				OrderNumberVo orderNumberVo = orderNumberVoDao.getUniqueByHql("SELECT new com.foshan.entity."
					        					+ "cultureCloud.vo.OrderNumberVo(MAX(a.orderNumber) "
					        					+ "AS orderNumber) FROM UpshelfColumnEntity a WHERE a.column.id="+column.getId()+" AND "
					        					+ "a.id!="+upshelfColumnEntity.getId()+
					        					" ORDER BY a.orderNumber DESC, a.upshelfTime DESC ", "");
					        				upshelfColumnDao.executeUpdate(
					        					"UPDATE UpshelfColumnEntity b SET b.orderNumber="+(null!=orderNumberVo.getOrderNumber() 
					        					? orderNumberVo.getOrderNumber():0)+"+1 WHERE b.id =" + 
					        					upshelfColumnEntity.getId());
				        			} catch (Exception e) {
				        				e.printStackTrace();
				        				upshelfColumnEntity.setOrderNumber(1);
				        			} 
//				        		}
				            }
						}
					}
				}else {
					for(UpshelfColumnEntity o : upshelfColumnList) {
						upshelfColumnDao.delete(o);
					}
				}	
				
//                cultureCloudAsset.setMemberId(req.getMemberId());
//                cultureCloudAsset.setUserId(req.getUserId());
//                cultureCloudAsset.setParentTypeId(req.getParentTypeId());
				res.setAssetId(cultureCloudAsset.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	@Override
	public IResponse modifyPublishedTime(CultureCloudAssetReq req) {
		ModifyCultureCloudAssetRes res = new ModifyCultureCloudAssetRes();
		if (null!=req.getAssetId() ) {
			CultureCloudAssetEntity cultureCloudAsset = cultureCloudAssetDao.get(req.getAssetId()) ;
            try {
            	cultureCloudAsset.setPublishedTime(StringUtils.isNotEmpty(req.getPublishedTime()) ? 
                    new Timestamp(DateUtil.parseLongFormat(req.getPublishedTime()).getTime()) : cultureCloudAsset.getPublishedTime());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } catch (ParseException e) {
                e.printStackTrace();
            }
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse deleteCultureCloudAsset(CultureCloudAssetReq req) {
		GenericResponse res = new GenericResponse();
		
		if (null != req.getAssetId()) {
		CultureCloudAssetEntity cultureCloudAsset = cultureCloudAssetDao.get(req.getAssetId());
			if (null != cultureCloudAsset) {
				Object userObj = getPrincipal(true);
				String depIds = getDepartment(userObj);
				if(StringUtils.isNoneEmpty(depIds) ) {
					 List<String> depIdList = Stream.of(depIds.split(","))  
				                .collect(Collectors.toList());
					 if(!depIdList.contains(cultureCloudAsset.getDepartmentId().toString())) {
							res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
							res.setRetInfo("对不起，不能操作除本部门及子部门以外的数据！");
							return res;
					 }
				}
				cultureCloudAsset.setSubAssetSet(null);
				cultureCloudAsset.setParentAsset(null);
				cultureCloudAsset.setRegion(null);
				cultureCloudAsset.setSubAttachmentAssetSet(null);
				cultureCloudAsset.setSubFileAssetSet(null);
				cultureCloudAsset.setSubImageSet(null);
				cultureCloudAsset.setMember(null);
				cultureCloudAsset.setUser(null);
				cultureCloudAsset.setRecommendLinkSet(null);
				cultureCloudAsset.setTagList(null);
				List<CultureCloudUpshelfColumnEntity> list = cultureCloudUpshelfColumnDao
						.getListByHql("from CultureCloudUpshelfColumnEntity a where a.asset.id= "
				 +cultureCloudAsset.getId(), "");
				if(null != list) {
					for(CultureCloudUpshelfColumnEntity upshelfColumn : list) {
						upshelfColumn.setAsset(null);
						upshelfColumn.setColumn(null);
						cultureCloudUpshelfColumnDao.delete(upshelfColumn);
					}
				}
				cultureCloudAssetDao.deleteById(req.getAssetId());
				//CultureCloudAsset.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	@Override
	public IResponse setBroadcastCount(CultureCloudAssetReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getAssetId() && null!=req.getBroadcastCount()) {
		CultureCloudAssetEntity cultureCloudAsset = cultureCloudAssetDao.get(req.getAssetId());
			if (null != cultureCloudAsset) {
				cultureCloudAsset.setBroadcastCount(req.getBroadcastCount());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCultureCloudAssetInfo(CultureCloudAssetReq req) {
		GetCultureCloudAssetInfoRes res = new GetCultureCloudAssetInfoRes();
		if (null != req.getAssetId()) {
			CultureCloudAssetEntity cultureCloudAsset = cultureCloudAssetDao.get(req.getAssetId());
			if (null != cultureCloudAsset) {
				res.setAssetForm(getCultureCloudAssetForm(cultureCloudAsset,req,null));
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	
	
    @Audit(operate = "文化云媒资上下架")
    @Override
    public IResponse setAssetState(CultureCloudAssetReq req) {
        GenericResponse res = new GenericResponse();
        if (null != req.getAssetId() && null != req.getAssetState()&& 0!= req.getAssetState()) {
        	CultureCloudAssetEntity cultureCloudAsset = cultureCloudAssetDao.get(req.getAssetId());
            if (null != cultureCloudAsset) {
            	
            	cultureCloudAsset.setAssetState(req.getAssetState());
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }

        return res;
    }
	
	
    @Audit(operate = "文化云审核媒资")
    @Override
    public IResponse auditCultureCloudAsset(CultureCloudAssetReq req) {
        GenericResponse res = new GenericResponse();
        if (null != req.getAssetId() && null != req.getAuditState()) {
        	CultureCloudAssetEntity cultureCloudAsset = cultureCloudAssetDao.get(req.getAssetId());
            if (null != cultureCloudAsset) {
            	
            	cultureCloudAsset.setAuditState(req.getAuditState());
            	if(null!=req.getAssetState()) {
            		cultureCloudAsset.setAssetState(req.getAssetState());
            	}else {
            		if(req.getAuditState()==2 || req.getAuditState()==3) {
            			cultureCloudAsset.setAssetState(1);
            		}
            	}
            	
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }

        return res;
    }
    
    
    
	@Override
	@Audit(operate = "新增资源")
	public IResponse addResources(CultureCloudAssetReq req) {
		// TODO Auto-generated method stub
		AddAssetRes res = new AddAssetRes();
		//if (StringUtils.isNotEmpty(req.getAssetCode())) {
			AssetEntity asset = new AssetEntity();
			asset.setAssetCode(StringUtils.isNoneEmpty(req.getAssetCode()) ? req.getAssetCode() : 
				String.valueOf(System.currentTimeMillis()));
			asset.setAssetName(StringUtils.isNoneEmpty(req.getAssetName()) ? req.getAssetName() : 
				asset.getAssetCode());
			AssetForm assetForm = new AssetForm();
			BeanUtils.copyProperties(asset,req);
			BeanUtils.copyProperties(res,req);
			asset.setPackageCount(0);
			asset.setAssetOrders(null!=req.getAssetOrders() ? req.getAssetOrders() : 1);
			asset.setPackageFlag(EntityContext.ASSET_PACKAGE_FLAG_VALID);
			if (null != req.getParentAssetId()) {
				AssetEntity parentAsset = assetDao.get(req.getParentAssetId());
				if (null != parentAsset) {
					asset.setParentAsset(parentAsset);
					parentAsset.setPackageFlag(EntityContext.ASSET_PACKAGE_FLAG_VALID);
					parentAsset.setPackageCount(
							null != parentAsset.getPackageCount() ? parentAsset.getPackageCount() + 1 : 1);
					assetForm.setParentAssetId(parentAsset.getId());
				} else {
					res.setRet("0001");
					res.setRetInfo("要增加媒资的媒资包不存在！！！");
				}
			}
			if(StringUtils.isNoneEmpty(req.getResourcesIdList())) {
				String[] resourcesIds = req.getResourcesIdList().split(",");
				int orders = resourcesIds.length;
				for (String resourcesId : resourcesIds) {
					AssetEntity resources = assetDao.get(Integer.parseInt(resourcesId));
					resources.setAssetOrders(orders);
					orders--;
					asset.getSubAssetSet().add(resources);
				}
			}
			try {
				Integer assetId = (Integer) assetDao.save(asset);
				assetForm.setAssetId(assetId);
			} catch (Exception ex) {
				res.setRet("0001");
				res.setRetInfo("要增加的媒资已存在！！！");
				return res;
			}
			res.setAssetForm(assetForm);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}
		return res;
	}

	@Override
	@Audit(operate = "修改媒资")
	public IResponse modifyResources(CultureCloudAssetReq req) {
		// TODO Auto-generated method stub
		ModifyAssetRes res = new ModifyAssetRes();
		AssetForm assetForm = new AssetForm();
		if (null != req.getAssetId()) {
			AssetEntity asset = assetDao.get(req.getAssetId());
			if (null != asset) {
				asset.setAssetCode(
						StringUtils.isNotEmpty(req.getAssetCode()) ? req.getAssetCode() : asset.getAssetCode());
				asset.setAssetName(
						StringUtils.isNotEmpty(req.getAssetName()) ? req.getAssetName() : asset.getAssetName());
				asset.setAssetState(null != req.getAssetState() ? req.getAssetState() : asset.getAssetState());
				asset.setAssetType(null != req.getAssetType() ? req.getAssetType() : asset.getAssetType());
				asset.setServiceCode(
						StringUtils.isNotEmpty(req.getServiceCode()) ? req.getServiceCode() : asset.getServiceCode());
				asset.setImageFile(
						StringUtils.isNotEmpty(req.getImageFile()) ? req.getImageFile() : asset.getImageFile());
				asset.setSmallImageFile(StringUtils.isNotEmpty(req.getSmallImageFile()) ? req.getSmallImageFile()
						: asset.getSmallImageFile());
				//asset.setTimeLength(null != req.getTimeLength() ? req.getTimeLength() : asset.getTimeLength());
				asset.setAssetOrders(null!=req.getAssetOrders()?req.getAssetOrders():asset.getAssetOrders());
				/*
				 * 判断媒资包的绑定情况： 1、如果请求父媒资为空，若请求媒资的父媒资不为空则变更媒资包关系；
				 * 2、如过请求父媒资不为空，则不论请求媒资的父媒资是否为空均进行媒资包关系调整
				 */
				AssetEntity oldParentAsset = asset.getParentAsset();
				if (null != req.getParentAssetId()) {
					AssetEntity newParentAsset = assetDao.get(req.getParentAssetId());
					if (null != newParentAsset) {
						asset.setParentAsset(newParentAsset);
						newParentAsset.setPackageCount(
								null != newParentAsset.getPackageCount() ? newParentAsset.getPackageCount() + 1 : 1);
						newParentAsset.setPackageFlag(EntityContext.ASSET_PACKAGE_FLAG_VALID);
						assetForm.setParentAssetId(newParentAsset.getId());
						if (null != oldParentAsset) {
							oldParentAsset.setPackageCount(oldParentAsset.getPackageCount() - 1);
							oldParentAsset.setPackageFlag(
									oldParentAsset.getPackageCount() == 0 ? EntityContext.ASSET_PACKAGE_FLAG_INVALID
											: EntityContext.ASSET_PACKAGE_FLAG_VALID);
						}
						// 如果修改的媒资绑定了投票选手，则因该媒资变更为媒资包的子媒资，则删除相应的投票选手数据

					} else {
						assetForm.setParentAssetId(req.getParentAssetId());
						res.setRet("0001");
						res.setRetInfo("要变更的媒资包不存在！！！");
						return res;
					}

				} else {
					if (null != oldParentAsset) {
						oldParentAsset.setPackageCount(oldParentAsset.getPackageCount() - 1);
						oldParentAsset.setPackageFlag(
								oldParentAsset.getPackageCount() == 0 ? EntityContext.ASSET_PACKAGE_FLAG_INVALID
										: EntityContext.ASSET_PACKAGE_FLAG_VALID);
						asset.setParentAsset(null);
					}
				}
				if(StringUtils.isNoneEmpty(req.getResourcesIdList())) {
					asset.getSubAssetSet().clear();
					String[] resourcesIds = req.getResourcesIdList().split(",");
					int orders = resourcesIds.length;
					for (String resourcesId : resourcesIds) {
						AssetEntity resources = assetDao.get(Integer.parseInt(resourcesId));
						resources.setAssetOrders(orders);
						orders--;
						asset.getSubAssetSet().add(resources);
					}
				}
				BeanUtils.copyProperties(asset,assetForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				assetForm.setAssetId(req.getAssetId());
				res.setRet("0001");
				res.setRetInfo("要修改的媒资不存在！！！");
			}
			res.setAssetForm(assetForm);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse getResourcesList(AssetReq req) {
		// TODO Auto-generated method stub
		GetAssetListRes response = new GetAssetListRes();
		if (null == req.getParentAssetId()) {
			response.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			response.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return response;
		}
		Page<AssetEntity> page = new Page<AssetEntity>();
		try {
			String assetName = req.getAssetName();
			Integer type = req.getAssetType();
			//Object userObj = getPrincipal(true);
			page.setPageSize(req.getPageSize());
			page.setCurrentPage(req.getRequestPage());
			StringBuilder hql =  new StringBuilder("from AssetEntity a where 1=1");
			if(StringUtils.isNoneEmpty(assetName)) {
				hql.append(" and assetName like '%"+ assetName + "%'");
			}
			if(StringUtils.isNoneEmpty(req.getAssetCode())) {
				hql.append(" and assetCode like '%"+ req.getAssetCode() + "%'");
			}
			if(type != null) {
				hql.append(" and assetType = " + type);
			}
			hql.append(null!=req.getParentAssetId() ? " and parentAsset.id="+req.getParentAssetId() :"");
		    
			Page<AssetEntity> queryPage = assetDao.queryPage(page,hql.toString());
			
			for(AssetEntity entity : queryPage.getResultList()) {
				AssetForm form = new AssetForm();
				BeanUtils.copyProperties(entity,form);
				form.setAssetId(entity.getId());
				for(AssetEntity a :entity.getSubAssetSet()) {
					AssetForm subForm = new AssetForm();
					BeanUtils.copyProperties(a,subForm);
					subForm.setAssetId(a.getId());
					form.getSubAssetList().add(subForm);
				}
				response.getAssetFormList().add(form);
			}
			
			response.setTotalResult(page.getTotalCount());
			response.setPageSize(page.getPageSize());
			response.setCurrentPage(page.getCurrentPage());
			response.setTotal(page.getTotalPage());
			response.setRet(ResponseContext.RES_SUCCESS_CODE);
			response.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			
		
		} catch (Exception e) {
			response.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
			response.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
		}

		return response;

	}
	
	
    @Audit(operate = "新增内容评论")
    @Override
    @ChechSensitiveWords
    public IResponse addCultureCloudAssetComment(CultureCloudAssetReq req) {
        GenericResponse res = new GenericResponse();
        Object userObj = getPrincipal(true);
        if(null == userObj) {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
            return res;
        }
        if (StringUtils.isNoneEmpty(req.getPreviewAssetId()) && null !=req.getContentType()) {
            
            CultureCloudAssetEntity asset = new CultureCloudAssetEntity();
            if (null != req.getParentAssetId()) {
            	CultureCloudAssetEntity parentAsset = cultureCloudAssetDao.get(req.getParentAssetId());
            	
            	if(null!=parentAsset) {
            		asset.setParentAsset(parentAsset);
            	}else {
                    res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                    res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "主贴不存在");
                    return res;
            	}
            } 
			asset.setPublishedTime(new Timestamp(new Date().getTime()));
	
            //asset.setSummaryShort(req.getSummaryShort());
            asset.setAssetContent(req.getAssetContent());
            asset.setAssetCode(String.valueOf(System.currentTimeMillis()));
            //asset.setAuditState(0);
            asset.setAssetType(5);
            asset.setPreviewAssetId(req.getPreviewAssetId());
            asset.setBroadcastCount(null!=req.getBroadcastCount() ? req.getBroadcastCount() :0);
            if(null!=req.getImageId()) {
            	AssetEntity image = assetDao.get(req.getImageId());
            	if(null!=image) {
            		image.setParentAsset(asset);
            		asset.getSubImageSet().add(image);
            	}
            }
            asset.setContentType(req.getContentType());
//            if (null != req.getMemberId()) {
//            	CultureCloudMemberEntity member = cultureCloudMemberDao.get(req.getMemberId());
//                asset.setMember(member);
//            }
//            if (null != req.getUserId()) {
//                UserEntity user = userDao.get(req.getUserId());
//                asset.setUser(user);
//            }
        	
        	if(userObj instanceof AccountEntity) {
        		CultureCloudMemberEntity account = (CultureCloudMemberEntity) userObj;
        		asset.setMember(account);
        		asset.setAuditState(1);
        		asset.setUserName(account.getNickName());
        	}else if(userObj instanceof PrincipalModel) {
        		UserEntity user = (UserEntity) userObj;
        		asset.setUser(user);
        		asset.setAuditState(3);
        		asset.setUserName(DesensitizedUtil.desensitizedName(user.getUserName()));
        	}
            cultureCloudAssetDao.save(asset);
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);


        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }

    @Audit(operate = "修改内容评论")
    @Override
    @ChechSensitiveWords
    public IResponse modifyCultureCloudAssetComment(CultureCloudAssetReq req) {
        GenericResponse res = new GenericResponse();
        Object userObj = getPrincipal(true);
        if(null == userObj) {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
            return res;
        }
        if (null != req.getAssetId()){
            CultureCloudAssetEntity asset = cultureCloudAssetDao.get(req.getAssetId());
            if (null != asset){
                
//                if (null != req.getMemberId()) {
//                    CultureCloudMemberEntity member = cultureCloudMemberDao.get(req.getMemberId());
//                    asset.setMember(member);
//                }
//                if (null != req.getUserId()) {
//                    UserEntity user = cultureCloudUserDao.get(req.getUserId());
//                    asset.setUser(user);
//                }
            	if(null!=userObj && userObj instanceof AccountEntity) {
            		CultureCloudMemberEntity account = (CultureCloudMemberEntity) userObj;
            		if(null!=asset.getMember()&& account.getId().toString().equals(asset.getMember().getId().toString())) {
            			asset.setAuditState(1);
            			asset.setUserName(account.getNickName());
            		}else {
                        res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
                        res.setRetInfo("对不起，只能修改本人的评论！");
                        return res;
            		}
            		
            	}else if(null!=userObj && userObj instanceof PrincipalModel) {
            		UserEntity user = (UserEntity) userObj;
            		asset.setUserName(DesensitizedUtil.desensitizedName(user.getUserName()));
            		if(!(null!=asset.getUser()&& user.getId().toString().equals(asset.getUser().getId().toString()))) {
                        res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
                        res.setRetInfo("对不起，只能修改本人的评论！");
                        return res;
            		}
            	}else {
                    res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
                    res.setRetInfo("对不起，只能修改本人的评论！");
                    return res;
            	}
                asset.setBroadcastCount(null!=req.getBroadcastCount() ? req.getBroadcastCount() :asset.getBroadcastCount());
                if(null!=req.getImageId()) {
                	AssetEntity image = assetDao.get(req.getImageId());
                	if(null!=image) {
                		asset.getSubImageSet().clear();
                		image.setParentAsset(asset);
                		asset.getSubImageSet().add(image);
                	}
                }

            	asset.setAssetContent(StringUtils.isNotEmpty(req.getAssetContent())?req.getAssetContent():asset.getAssetContent());
                cultureCloudAssetDao.saveOrUpdate(asset);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            }else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "回帖不存在");
            }
        }else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }

    @Audit(operate = "审核邻里内容评论")
    @Override
    public IResponse auditCultureCloudAssetComment(CultureCloudAssetReq req) {
    	GenericResponse res = new GenericResponse();
        if (null != req.getAssetId() && null != req.getAuditState()){
            CultureCloudAssetEntity asset = cultureCloudAssetDao.get(req.getAssetId());
            if (null != asset){
                asset.setAuditState(req.getAuditState());
                asset.setIdea(null != req.getIdea()?req.getIdea():"");
                cultureCloudAssetDao.saveOrUpdate(asset);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            }else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "回帖不存在");
            }
        }else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }

    @Audit(operate = "删除邻里内容评论")
    @Override
    public IResponse deleteCultureCloudAssetComment(CultureCloudAssetReq req) {
    	GenericResponse res = new GenericResponse();
        Object userObj = getPrincipal(true);
        if(null == userObj) {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
            return res;
        }
        if (null != req.getAssetId()){
            CultureCloudAssetEntity asset = cultureCloudAssetDao.get(req.getAssetId());
            if (null != asset){
				if(null != userObj && userObj instanceof CultureCloudUserEntity){

				}
            	else if(null!=userObj && userObj instanceof AccountEntity) {
            		CultureCloudMemberEntity account = (CultureCloudMemberEntity) userObj;
            		if(null!=asset.getMember()&& account.getId().toString().equals(asset.getMember().getId().toString())) {
            			asset.setAuditState(1);
            		}else {
                        res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
                        res.setRetInfo("对不起，只能删除本人的评论！");
                        return res;
            		}
            		
            	}else if(null!=userObj && userObj instanceof PrincipalModel) {
            		UserEntity user = (UserEntity) userObj;
            		if(!(null!=asset.getUser()&& user.getId().toString().equals(asset.getUser().getId().toString()))) {
                        res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
                        res.setRetInfo("对不起，只能删除本人的评论！");
                        return res;
            		}
            	}else {
                    res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
                    res.setRetInfo("对不起，只能删除本人的评论！");
                    return res;
            	}
            	asset.getSubAssetSet().forEach(o->{
            		o.setParentAsset(null);
            	});
            	asset.setSubAssetSet(null);
				asset.setMember(null);
                cultureCloudAssetDao.delete(asset);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            }else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "回帖不存在");
            }
        }else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;    
    }

    @Override
    public IResponse getCultureCloudAssetCommentList(CultureCloudAssetReq req) {
        //GetCultureCloudAssetCommentListRes res = new GetCultureCloudAssetCommentListRes();
    	GetCultureCloudAssetListRes res = new GetCultureCloudAssetListRes();
//	    if (null != req.getAssetId()){
	        Page<CultureCloudAssetEntity> page = new Page<>();
	        page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
	        page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 0) * page.getPageSize());
	        page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
	        StringBuilder hql = new StringBuilder("select distinct a from CultureCloudAssetEntity a where a.id is not null ");
	        hql.append(StringUtils.isNoneEmpty(req.getPreviewAssetId()) ? " and a.previewAssetId="+req.getPreviewAssetId():"")
	        	.append(StringUtils.isNoneEmpty(req.getAssetContent()) ? " and a.assetContent like(%"+req.getAssetContent()+"%)":"")
	        	.append(null!=req.getParentAssetId() ? "  and a.parentAsset.id ="+req.getParentAssetId():
	        		(null!=req.getCommentTree()&&req.getCommentTree()==1 ? "   and a.parentAsset is null " :""));
	        Object userObj = getPrincipal(true);
	        boolean isAccount = false;
	        boolean isUser = false;
	        String accountId=null;
	        String userId=null;
        	if(null!=userObj && userObj instanceof AccountEntity) {
        		AccountEntity account = (AccountEntity) userObj;
        		isAccount = true;
        		accountId = account.getId().toString();
        	}else if(null!=userObj && (userObj instanceof PrincipalModel || 
        			userObj instanceof CultureCloudUserEntity)) {
        		isUser = true;
        		if(userObj instanceof PrincipalModel) {
        			PrincipalModel user = (PrincipalModel) userObj;
        			userId = user.getId().toString();
        		}else {
        			CultureCloudUserEntity user = (CultureCloudUserEntity) userObj;
        			userId = user.getId().toString();
        		}
        	}
	        
	        if(null!=req.getIndividualComment() && req.getIndividualComment()==1) {
	        	
	        	if(isAccount) {
	        		hql.append(" and a.member.id="+accountId);
	        	}else if(isUser) {
	        		
	        		hql.append(" and a.user.id="+userId);
	        	}
	        }else if(isAccount){
	        	hql.append(" and (a.member.id="+accountId+" or ( a.member.id !='"+accountId+"' and  a.auditState=3))");
	        }
//	        else if(isUser) {
//	        	hql.append(" and (a.user.id="+accountId+" or ( a.user.id !='"+userId+"' and a.auditState=3))");
//	        }
	        else{
	        	hql.append(StringUtils.isNoneEmpty(req.getAuditStateList()) ? " and a.auditState in("+req.getAuditStateList()+")":
	        			null!=req.getAuditState() ? " and a.auditState='"+req.getAuditState()+"'":" and  a.auditState=3");
	        }
        	hql.append(null !=req.getContentType() ? " and a.contentType="+req.getContentType():"")
        		.append(" ORDER BY a.createTime desc");
	        page = cultureCloudAssetDao.queryPage(page,hql.toString());
	        res.setTotalResult(page.getTotalCount());
	        res.setPageSize(page.getPageSize());
	        res.setCurrentPage(page.getCurrentPage());
	        res.setTotal(page.getTotalPage());

	        
	        for(CultureCloudAssetEntity o : page.getResultList()){
	        	//if((null!=req.getAuditState()&&o.getAuditState()==req.getAuditState()) ||
	        	//		(null==req.getAuditState()&&o.getAuditState()==3)) {
		        	CultureCloudAssetForm commentForm = new CultureCloudAssetForm();
		        	commentForm.setPreviewAssetId(o.getPreviewAssetId());
		        	commentForm.setContentType(o.getContentType());
		            commentForm.setAssetId(o.getId());
		            commentForm.setAuditState(o.getAuditState());
		            commentForm.setSummaryShort(o.getSummaryShort());
		            commentForm.setMemberId(null != o.getMember()?o.getMember().getId():null);
		            commentForm.setUserId(null != o.getUser()?o.getUser().getId():null);
		            commentForm.setCreateTime(null!=o.getCreateTime() ? 
		                        DateUtil.format(o.getCreateTime(),1) : "");
		            commentForm.setAssetContent(o.getAssetContent());
		            commentForm.setBroadcastCount(null!=o.getBroadcastCount() ? o.getBroadcastCount() :0);
		    		CultureCloudAssetForm parentCommentForm = new CultureCloudAssetForm();
		    		if(null!=o.getParentAsset()) {
		    			//CultureCloudAssetEntity parentComment =  (CultureCloudAssetEntity)o.getParentAsset();
		    			CultureCloudAssetEntity parentComment = cultureCloudAssetDao.get(o.getParentAsset().getId());
		    			parentCommentForm.setAssetId(parentComment.getId());
		    			parentCommentForm.setAssetContent(parentComment.getAssetContent());
		                if(null!=parentComment.getMember()) {
		                	if(parentComment.getMember().getId().toString().equals(accountId)) {
		                		parentCommentForm.setIndividualComment(1);
		                	}
		                	parentCommentForm.setHeadImage(o.getMember().getHeadImage());
		                	parentCommentForm.setUserName(StringUtils.isNoneEmpty(o.getUserName()) ? o.getUserName() :
		                		o.getMember().getNickName());
		                }else if(null!=parentComment.getUser()) {
		                	parentCommentForm.setUserName(StringUtils.isNoneEmpty(parentComment.getUserName()) ? o.getUserName() :
		                		DesensitizedUtil.desensitizedName(parentComment.getUser().getUserName()));
		                	if(parentComment.getUser().getId().toString().equals(userId)) {
		                		parentCommentForm.setIndividualComment(1);
		                	}
		                	parentCommentForm.setIndividualComment(1);
		                	parentCommentForm.setHeadImage("");
		                }else {
		                	parentCommentForm.setUserName("");
		                	parentCommentForm.setIndividualComment(0);
		                	parentCommentForm.setHeadImage("");
		                }
		    		}
		    		commentForm.setParentCultureCloudAsset(parentCommentForm);
		            if(null!=o.getMember()) {
		            	if(o.getMember().getId().toString().equals(accountId)) {
		            		commentForm.setIndividualComment(1);
		            	}
		            	commentForm.setHeadImage(o.getMember().getHeadImage());
		            	commentForm.setUserName(StringUtils.isNoneEmpty(o.getMember().getNickName()) ? o.getMember().getNickName() :
		            		"");
		            }else if(null!=o.getUser()) {
		            	if(o.getUser().getId().toString().equals(userId)) {
		            		commentForm.setIndividualComment(1);
		            	}
		            	
		            	commentForm.setHeadImage("");
		            	commentForm.setUserName(StringUtils.isNoneEmpty(o.getUserName()) ? o.getUserName() :
		            		DesensitizedUtil.desensitizedName(o.getUser().getUserName()));
		            }else {
		            	commentForm.setIndividualComment(0);
		            	commentForm.setHeadImage("");
		            }
		            if(null!=req.getCommentTree()&&req.getCommentTree()==1) {
		            	//getSubAsset(o.getSubAssetSet(),req,accountId,userId);
		            	commentForm.getSubAssetList().addAll(getSubAsset(o.getSubAssetSet(),req,accountId,userId));
		            }
		    		if(null!=o.getSubImageSet()) {
		    			for (AssetEntity asset : o.getSubImageSet()) {
		    				AssetForm assetForm = new AssetForm();
		    				BeanUtils.copyProperties(asset, assetForm);
		    				assetForm.setAssetId(asset.getId());
		    				commentForm.getImageList().add(assetForm);
		    			}
		    		}
		            res.getAssetList().add(commentForm);
	        	//}
	        }
	        res.setRet(ResponseContext.RES_SUCCESS_CODE);
	        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

//	    }else {
//	        res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//	        res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//	    }
	    return res;
    }

    public List<CultureCloudAssetForm> getSubAsset(Set<AssetEntity> list ,
    		CultureCloudAssetReq req,String accountId,String userId) {
    	List<CultureCloudAssetForm> formList = new ArrayList<>();
    	list.forEach(p->{
			CultureCloudAssetEntity o = new CultureCloudAssetEntity();
			copyProperties(o,p);
			o.setSubAssetSet(p.getSubAssetSet());
			o.setOutsideColumnEntityList(p.getOutsideColumnEntityList());
			o.setAssetInteractionList(p.getAssetInteractionList());
			o.setParentAsset(p.getParentAsset());
    		CultureCloudAssetForm commentForm = new CultureCloudAssetForm();
    		CultureCloudAssetForm parentCommentForm = new CultureCloudAssetForm();
    		if(null!=p.getParentAsset()) {
    			//CultureCloudAssetEntity parentComment =  (CultureCloudAssetEntity)p.getParentAsset();
    			CultureCloudAssetEntity parentComment = cultureCloudAssetDao.get(p.getParentAsset().getId());
    			parentCommentForm.setAssetId(parentComment.getId());
    			parentCommentForm.setAssetContent(parentComment.getAssetContent());
                if(null!=parentComment.getMember()) {
                	if(parentComment.getMember().getId().toString().equals(accountId)) {
                		parentCommentForm.setIndividualComment(1);
                	}
                	parentCommentForm.setHeadImage(parentComment.getMember().getHeadImage());
                	parentCommentForm.setUserName(StringUtils.isNoneEmpty(parentComment.getUserName()) ? parentComment.getUserName() :
							parentComment.getMember().getNickName());
                }else if(null!=parentComment.getUser()) {
                	parentCommentForm.setUserName(StringUtils.isNoneEmpty(parentComment.getUserName()) ? o.getUserName() :
                		DesensitizedUtil.desensitizedName(parentComment.getUser().getUserName()));
                	if(parentComment.getUser().getId().toString().equals(userId)) {
                		parentCommentForm.setIndividualComment(1);
                	}
                	parentCommentForm.setIndividualComment(1);
                	parentCommentForm.setHeadImage("");
                }else {
                	parentCommentForm.setUserName("");
                	parentCommentForm.setIndividualComment(0);
                	parentCommentForm.setHeadImage("");
                }
    		}
    		commentForm.setParentCultureCloudAsset(parentCommentForm);
    		
        	commentForm.setPreviewAssetId(o.getPreviewAssetId());
        	commentForm.setContentType(o.getContentType());
            commentForm.setAssetId(o.getId());
            commentForm.setAuditState(o.getAuditState());
            commentForm.setSummaryShort(o.getSummaryShort());
            commentForm.setMemberId(null != o.getMember()?o.getMember().getId():null);
            commentForm.setUserId(null != o.getUser()?o.getUser().getId():null);
            commentForm.setAssetContent(StringUtils.isNoneEmpty(o.getAssetContent())? o.getAssetContent():"");
            commentForm.setBroadcastCount(null!=o.getBroadcastCount() ? o.getBroadcastCount() :0);
            commentForm.setCreateTime(null!=o.getCreateTime() ? 
                        DateUtil.format(o.getCreateTime(),1) : "");
            if(null!=o.getMember()) {
            	if(o.getMember().getId().toString().equals(accountId)) {
            		commentForm.setIndividualComment(1);
            	}
            	commentForm.setHeadImage(o.getMember().getHeadImage());
            	commentForm.setUserName(StringUtils.isNoneEmpty(o.getUserName()) ? o.getUserName() :
            		o.getMember().getNickName());
            }else if(null!=o.getUser()) {
            	commentForm.setUserName(StringUtils.isNoneEmpty(o.getUserName()) ? o.getUserName() :
            		DesensitizedUtil.desensitizedName(o.getUser().getUserName()));
            	if(o.getUser().getId().toString().equals(userId)) {
            		commentForm.setIndividualComment(1);
            	}
            	commentForm.setIndividualComment(1);
            	commentForm.setHeadImage("");
            }else {
            	commentForm.setUserName("");
            	commentForm.setIndividualComment(0);
            	commentForm.setHeadImage("");
            }
    		if(null!=o.getSubImageSet()) {
    			for (AssetEntity asset : o.getSubImageSet()) {
    				AssetForm assetForm = new AssetForm();
    				BeanUtils.copyProperties(asset, assetForm);
    				assetForm.setAssetId(asset.getId());
    				commentForm.getImageList().add(assetForm);
    			}
    		}
            formList.add(commentForm);
    	});
		Collections.sort(formList,new Comparator<CultureCloudAssetForm>(){
			public int compare(CultureCloudAssetForm o1, CultureCloudAssetForm o2) {
				if(null==o1.getAssetId() || null==o2.getAssetId()) {
					return 1;
				}
				if(o1.getAssetId()<o2.getAssetId()){
					return 1;
				}
				if(o1.getAssetId()==o2.getAssetId())
					return 0;
				return -1;
			}
		});
    	return formList;
    }

    @Override
    public IResponse getCultureCloudAssetCommentInfo(CultureCloudAssetReq req) {
    	GetCultureCloudAssetInfoRes res = new GetCultureCloudAssetInfoRes();
        if (null != req.getAssetId()){
	        Object userObj = getPrincipal(true);
	        //boolean isAccount = false;
	       // boolean isUser = false;
	        String accountId=null;
	        String userId=null;
        	if(null!=userObj && userObj instanceof AccountEntity) {
        		AccountEntity account = (AccountEntity) userObj;
        		//isAccount = true;
        		accountId = account.getId().toString();
        	}else if(null!=userObj && userObj instanceof PrincipalModel) {
        		//isUser = true;
        		PrincipalModel user = (PrincipalModel) userObj;
        		userId = user.getId().toString();
        	}
            CultureCloudAssetEntity o = cultureCloudAssetDao.get(req.getAssetId());
            if (null != o){
	        	CultureCloudAssetForm commentForm = new CultureCloudAssetForm();
	        	commentForm.setPreviewAssetId(o.getPreviewAssetId());
	        	commentForm.setContentType(o.getContentType());
	            commentForm.setAssetId(o.getId());
	            commentForm.setAuditState(o.getAuditState());
	            commentForm.setSummaryShort(o.getSummaryShort());
	            commentForm.setMemberId(null != o.getMember()?o.getMember().getId():null);
	            commentForm.setUserId(null != o.getUser()?o.getUser().getId():null);
	            commentForm.setCreateTime(null!=o.getCreateTime() ? 
	                        DateUtil.format(o.getCreateTime(),1) : "");
	            commentForm.setAssetContent(o.getAssetContent());
	            commentForm.setBroadcastCount(null!=o.getBroadcastCount() ? o.getBroadcastCount() :0);
	            if(null!=o.getMember()) {
	            	if(o.getMember().getId().toString().equals(accountId)) {
	            		commentForm.setIndividualComment(1);
	            	}
	            	commentForm.setHeadImage(o.getMember().getHeadImage());
	            	commentForm.setUserName(StringUtils.isNoneEmpty(o.getMember().getNickName()) ? o.getMember().getNickName() :
	            		"");
	            }else if(null!=o.getUser()) {
	            	if(o.getUser().getId().toString().equals(userId)) {
	            		commentForm.setIndividualComment(1);
	            	}
	            	
	            	commentForm.setHeadImage("");
	            	commentForm.setUserName(StringUtils.isNoneEmpty(o.getUserName()) ? o.getUserName() :
	            		DesensitizedUtil.desensitizedName(o.getUser().getUserName()));
	            }else {
	            	commentForm.setIndividualComment(0);
	            	commentForm.setHeadImage("");
	            }
	            if(null!=req.getCommentTree()&&req.getCommentTree()==1) {
	            	//getSubAsset(o.getSubAssetSet(),req,accountId,userId);
	            	commentForm.getSubAssetList().addAll(getSubAsset(o.getSubAssetSet(),req,accountId,userId));
	            }
	    		if(null!=o.getSubImageSet()) {
	    			for (AssetEntity asset : o.getSubImageSet()) {
	    				AssetForm assetForm = new AssetForm();
	    				BeanUtils.copyProperties(asset, assetForm);
	    				assetForm.setAssetId(asset.getId());
	    				commentForm.getImageList().add(assetForm);
	    			}
	    		}
	    		res.setAssetForm(commentForm);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            }else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "回帖不存在");
            }
        }else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;    
    }


	@Audit(operate = "修改资讯同步文化广东状态")
	@Override
	public IResponse modifySyncAssetWhgdStatus(CultureCloudAssetReq req) {
		GenericResponse res = new GenericResponse();
		Object userObj = getPrincipal(true);
		UserEntity user = (UserEntity) userObj;

		if (user == null) {
			res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
			return res;
		}
		try {
			CultureCloudAssetEntity activity = cultureCloudAssetDao.get(req.getAssetId());
			activity.setNeedSync(req.getNeedSync());
			cultureCloudAssetDao.update(activity);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} catch (Exception e) {
			logger.error("修改活动同步文化广东状态失败：", e);
			res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
			res.setRetInfo("修改活动同步文化广东状态失败");
		}
		return res;

	}

}