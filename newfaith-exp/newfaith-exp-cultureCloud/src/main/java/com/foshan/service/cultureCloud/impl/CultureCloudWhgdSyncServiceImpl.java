package com.foshan.service.cultureCloud.impl;

import com.alibaba.fastjson.JSONObject;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.cultureCloud.*;

import com.foshan.form.cultureCloud.model.WhgdPersonInfo;
import com.foshan.form.cultureCloud.request.WhgdCancelOrderReq;
import com.foshan.form.cultureCloud.request.WhgdSyncReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.cultureCloud.ICultureCloudWhgdSyncService;
import com.foshan.util.DigestUtil;
import com.foshan.util.HttpClientUtil;

import com.foshan.util.cultureCloud.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * 文化广东同步服务实现类
 * 用于同步信息到文化广东平台
 */
@Service("cultureCloudWhgdSyncService")
@Transactional
public class CultureCloudWhgdSyncServiceImpl extends GenericCultureCloudService implements ICultureCloudWhgdSyncService {

    private final static Logger logger = LoggerFactory.getLogger(CultureCloudWhgdSyncServiceImpl.class);

    // 文化广东API接口地址
    private final static String WHGD_API_BASE_URL = "https://wlzn.prdculture.org.cn/zdataGateway/route/";

    // 文化广东API接口密钥
    private final static String WHGD_API_KEY = "foshan_culture_cloud";

    // 文化广东API接口密钥
    private final static String WHGD_API_SECRET = "foshan_secret_key";

    // 文化广东SM4KEY
    private final static String SM4KEY = "DEF76582D8D56625C4FC6A711E8703D7";

    @Override
    public IResponse syncActivityToWhgd(WhgdSyncReq req) {
        GenericResponse res = new GenericResponse();

        try {

            // 获取需要同步的活动信息
            StringBuilder hql = new StringBuilder("from CultureCloudActivityEntity a where a.needSync = 1 and (a.hasSynced = 0 or a.hasSynced = 2)");
            List<CultureCloudActivityEntity> activityList = cultureCloudActivityDao.getListByHql(hql.toString());
            if (activityList.size() == 0) {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo("没有需要同步的活动信息");
                return res;
            }

            logger.info("需要同步的活动总数: " + activityList.size());

            // 每批次处理19条数据
            int batchSize = 19;
            int totalProcessed = 0;
            int successCount = 0;

            for (int i = 0; i < activityList.size(); i += batchSize) {
                // 获取当前批次的活动列表
                int endIndex = Math.min(i + batchSize, activityList.size());
                List<CultureCloudActivityEntity> batchList = activityList.subList(i, endIndex);

                // 构建同步数据集合
                List<Map<String, Object>> infoList = new ArrayList<>();

                // 处理每个活动
                for (CultureCloudActivityEntity activity : batchList) {
                    Map<String, Object> activityInfo = new HashMap<>();

                    // 填充必填参数
                    activityInfo.put("tongId", activity.getId());
                    activityInfo.put("name", activity.getActivityName());

                    String activityType = "";
                    Map<Integer, List<CultureCloudTagEntity>> collect1 = (Map<Integer, List<CultureCloudTagEntity>>) activity.getTagList()
                            .parallelStream().collect(groupingBy(CultureCloudTagEntity::getCategory));
                    for(Integer key : collect1.keySet()) {
                        if(key==2){
                            for(CultureCloudTagEntity t : collect1.get(key)) {
                                activityType = t.getTagName();
                            }
                        }
                    }

                    activityInfo.put("type", getActivityType(activityType));
                    activityInfo.put("showed", activity.getActivityState() == 6 ? 1 : 0);
                    activityInfo.put("needEnroll", activity.getActivityIsReservation().equals(1) ? 0 : 1);

                    // 如果需要报名，填充报名相关信息
                    if (activity.getActivityIsReservation() == 2) {
                        List<CultureCloudActivityEventEntity> activityEventList = cultureCloudActivityEventDao.getListByHql("select a from " +
                                "CultureCloudActivityEventEntity a where a.state="+ EntityContext.RECORD_STATE_VALID+"  and a.activity.id = "+ activity.getId());
                        Integer aviilableCount = 0;
                        for(CultureCloudActivityEventEntity event:activityEventList){
                            if (null != event.getAvailableCount()){
                                aviilableCount += event.getAvailableCount();
                            }
                        }

                        activityInfo.put("regStartDay", DateUtils.convertToSS(activity.getSignStartTime()));
                        activityInfo.put("regEndDay", DateUtils.convertToSS(activity.getSignEndTime()));
                        activityInfo.put("maxNumber", aviilableCount);
                        activityInfo.put("singleMaxNumber", activity.getActivityTicketCount() != null ? activity.getActivityTicketCount() : 1);
                    }

                    activityInfo.put("linkman", null != activity.getActivitySpeaker()? activity.getActivitySpeaker(): "无");
                    activityInfo.put("way", 0); // 默认线下活动

                    // 线下活动地址信息
                    activityInfo.put("areaCode", getAreaCode(activity.getActivityProvince(), activity.getActivityCity(), activity.getActivityArea()));
                    activityInfo.put("address", activity.getActivityAddress());
                    activityInfo.put("longitude", activity.getActivityLon() != null ? activity.getActivityLon().toString() : "");
                    activityInfo.put("latitude", activity.getActivityLat() != null ? activity.getActivityLat().toString() : "");

                    // 活动时间
                    activityInfo.put("startDay",activity.getActivityStartTime()+" 00:00:00");
                    activityInfo.put("endDay",activity.getActivityEndTime()+" 23:59:59");

                    // 主办方信息
                    activityInfo.put("unitName", StringUtils.isNotEmpty(activity.getActivityHost())? activity.getActivityHost() : "无");
                    activityInfo.put("coUnitName", StringUtils.isNotEmpty(activity.getActivityCoorganizer())? activity.getActivityCoorganizer() :
                            null);
                    activityInfo.put("undertakeUnitName", StringUtils.isNotEmpty(activity.getActivityOrganizer())? activity.getActivityOrganizer()
                            : null);

                    // 联系方式和详情
                    activityInfo.put("phone", activity.getActivityTel() != null ? activity.getActivityTel() : activity.getFixedTel());
                    activityInfo.put("detail", activity.getActivityMemo() != null ? activity.getActivityMemo() : "");
                    activityInfo.put("picUrl", "https://www.foshan.gov.cn/fswenhua/cultureCloud"+activity.getActivityIconUrl());
                    activityInfo.put("remark", activity.getActivityProfile() != null ? activity.getActivityProfile() : "");

                    activityInfo.put("creatorUnitId",activity.getVenueList().get(0).getWhgdUnitId());
                    infoList.add(activityInfo);
                }

                // 构建API请求参数
                Map<String, Object> syncData = new HashMap<>();
                syncData.put("infoList", infoList);

                // 调用文化广东API
                String apiUrl = WHGD_API_BASE_URL + "/activityBatchSave";
                logger.info("同步活动信息到文化广东，API地址: " + apiUrl + ", 请求参数: " + syncData);
                String result = sendToWhgdApi(apiUrl, syncData);

                // 处理同步结果
                if (StringUtils.isNotEmpty(result)) {
                    logger.info("文化广东服务器返回："+ result);
                    JSONObject resultJson = JSONObject.parseObject(result);
                    if (resultJson.getInteger("code") == 2000) {
                        resultJson.getJSONObject("result").getJSONArray("data").forEach(item -> {
                            JSONObject activityInfo = (JSONObject) item;
                            Integer tongId = activityInfo.getInteger("tongId");
                            Integer whgdId = activityInfo.getInteger("activityId");
                            // 更新活动的文化广东ID
                            CultureCloudActivityEntity activity = cultureCloudActivityDao.get(tongId);
                            activity.setWhgdActivityId(whgdId);
                            // 更新已同步的活动状态
                            activity.setHasSynced(1); // 设置为已同步
                            cultureCloudActivityDao.update(activity);
                            logger.info("活动ID: " + tongId + ", 文化广东ID: " + whgdId);
                        });
                        successCount += batchList.size();
                        logger.info("成功同步活动批次: " + (i/batchSize + 1) + ", 数量: " + batchList.size());
                    } else {
                        logger.error("同步活动批次失败: " + (i/batchSize + 1) + ", 错误: " + resultJson.getString("message"));
                    }
                } else {
                    logger.error("同步活动批次失败: " + (i/batchSize + 1) + ", API返回为空");
                }

                totalProcessed += batchList.size();
                logger.info("已处理: " + totalProcessed + "/" + activityList.size() + " 活动");
            }

            // 设置返回结果
            if (successCount > 0) {
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo("成功同步 " + successCount + "/" + activityList.size() + " 个活动");
            } else {
                res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                res.setRetInfo("活动同步失败，请查看日志");
            }

        } catch (Exception e) {
            logger.error("同步活动信息到文化广东失败：", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("同步活动信息到文化广东失败：" + e.getMessage());
        }

        return res;
    }

    @Override
    public IResponse syncTrainingToWhgd(WhgdSyncReq req) {
        GenericResponse res = new GenericResponse();

        try {
            // 获取需要同步的培训信息
            StringBuilder hql = new StringBuilder("from CultureCloudOfflineTrainingEntity t where t.needSync = 1 and (t.hasSynced = 0 or t.hasSynced = 2)");
            List<CultureCloudOfflineTrainingEntity> trainingList = cultureCloudOfflineTrainingDao.getListByHql(hql.toString());
            if (trainingList.size() == 0) {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo("没有需要同步的培训信息");
                return res;
            }

            logger.info("需要同步的培训总数: " + trainingList.size());

            // 艺术类型映射
            Map<String, Integer> artTypeMap = new HashMap<>();
            artTypeMap.put("音乐", 1);
            artTypeMap.put("舞蹈", 2);
            artTypeMap.put("戏剧", 3);
            artTypeMap.put("美术", 4);
            artTypeMap.put("书法", 5);
            artTypeMap.put("摄影", 6);
            artTypeMap.put("其他", 7);

            // 培训类型映射
            Map<String, Integer> trainTypeMap = new HashMap<>();
            trainTypeMap.put("公益培训", 1);
            trainTypeMap.put("收费培训", 2);
            trainTypeMap.put("其他培训", 3);

            // 每批次处理19条数据
            int batchSize = 19;
            int totalProcessed = 0;
            int successCount = 0;

            for (int i = 0; i < trainingList.size(); i += batchSize) {
                // 获取当前批次的培训列表
                int endIndex = Math.min(i + batchSize, trainingList.size());
                List<CultureCloudOfflineTrainingEntity> batchList = trainingList.subList(i, endIndex);

                // 构建同步数据集合
                List<Map<String, Object>> infoList = new ArrayList<>();

                // 处理每个培训
                for (CultureCloudOfflineTrainingEntity training : batchList) {
                    Map<String, Object> trainingInfo = new HashMap<>();

                    // 填充必填参数
                    trainingInfo.put("tongId", training.getId());
                    trainingInfo.put("name", training.getTrainTitle());
                    trainingInfo.put("way", 0); // 固定传0
                    trainingInfo.put("picUrl", "https://www.foshan.gov.cn/fswenhua/cultureCloud" + training.getResourceImgUrl());
                    trainingInfo.put("personName", training.getLinkman() != null ? training.getLinkman() : "佛山市文化馆");
                    
                    // 获取艺术类型
                    String trainType = training.getTrainType();
                    Integer artType = 7; // 默认为其他
                    for (Map.Entry<String, Integer> entry : artTypeMap.entrySet()) {
                        if (trainType != null && trainType.contains(entry.getKey())) {
                            artType = entry.getValue();
                            break;
                        }
                    }
                    trainingInfo.put("artType", artType);
                    
                    // 获取培训类型
                    Integer offlineTrainType = 1; // 默认为公益培训
                    for (Map.Entry<String, Integer> entry : trainTypeMap.entrySet()) {
                        if (trainType != null && trainType.contains(entry.getKey())) {
                            offlineTrainType = entry.getValue();
                            break;
                        }
                    }
                    trainingInfo.put("trainType", offlineTrainType);
                    
                    // 培训简介
                    if (training.getTrainIntroduce() != null) {
                        trainingInfo.put("detail", training.getTrainIntroduce());
                    }
                    
                    // 报名时间
                    trainingInfo.put("regStartDay", training.getRegistrationStartTime());
                    trainingInfo.put("regEndDay", training.getRegistrationEndTime());
                    trainingInfo.put("maxNumber", training.getMaxPeople() != null ? training.getMaxPeople() : 30);
                    
                    // 年龄限制
                    boolean hasAgeLimit = training.getMinAge() != null && training.getMaxAge() != null;
                    trainingInfo.put("limitAge", hasAgeLimit ? 1 : 0);
                    if (hasAgeLimit) {
                        trainingInfo.put("minAge", training.getMinAge());
                        trainingInfo.put("maxAge", training.getMaxAge());
                    }
                    
                    // 培训时间
                    trainingInfo.put("startDay", training.getTrainStartTime());
                    trainingInfo.put("endDay", training.getTrainEndTime());
                    
                    // 地址信息
                    trainingInfo.put("areaCode", getAreaCode(training.getTrainProvince(), training.getTrainCity(), training.getTrainArea()));
                    trainingInfo.put("address", training.getTrainAddress());
                    trainingInfo.put("longitude", training.getLon() != null ? training.getLon().toString() : "113.121416");
                    trainingInfo.put("latitude", training.getLat() != null ? training.getLat().toString() : "23.021834");
                    
                    // 发布单位信息
                    trainingInfo.put("creatorUnitId", 64929L); // 默认机构ID，需要根据实际情况调整
                    trainingInfo.put("publishUnitId", 64929L); // 默认机构ID，需要根据实际情况调整
                    
                    // 上下架状态
                    trainingInfo.put("showed", training.getTrainStatus() != null && training.getTrainStatus() == 1 ? 1 : 0);
                    
                    // 获取培训课程列表
                    List<Map<String, Object>> classList = new ArrayList<>();
                    
                    // 查询培训场次
                    String fieldHql = "from CultureCloudOfflineTrainingFieldEntity f where f.training.id = " + training.getId() + 
                                      " and f.state = " + EntityContext.RECORD_STATE_VALID + " order by f.fieldDate asc";
                    List<CultureCloudTrainFieldEntity> fieldList = cultureCloudTrainFieldDao.getListByHql(fieldHql);
                    
                    if (fieldList.isEmpty()) {
                        // 如果没有具体场次，创建一个默认场次
                        Map<String, Object> defaultClass = new HashMap<>();
                        defaultClass.put("classDate", training.getTrainStartTime());
                        defaultClass.put("classStartTime", "09:00:00");
                        defaultClass.put("classEndTime", "11:00:00");
                        defaultClass.put("orderBy", 1);
                        classList.add(defaultClass);
                    } else {
                        // 处理每个场次
                        int orderBy = 1;
                        for (CultureCloudTrainFieldEntity field : fieldList) {
                            Map<String, Object> classInfo = new HashMap<>();
                            classInfo.put("classDate", field.getFieldTimeStr());
                            classInfo.put("classStartTime", field.getFieldStartTime());
                            classInfo.put("classEndTime", field.getFieldEndTime());
                            classInfo.put("orderBy", orderBy++);
                            classList.add(classInfo);
                        }
                    }
                    
                    trainingInfo.put("classList", classList);
                    infoList.add(trainingInfo);
                }

                // 构建API请求参数
                Map<String, Object> syncData = new HashMap<>();
                syncData.put("infoList", infoList);

                // 调用文化广东API
                String apiUrl = WHGD_API_BASE_URL + "/offlineArtTrainBatchSave";
                String result = sendToWhgdApi(apiUrl, syncData);

                // 处理同步结果
                if (StringUtils.isNotEmpty(result)) {
                    JSONObject resultJson = JSONObject.parseObject(result);
                    if (resultJson.getInteger("code") == 200) {
                        // 更新已同步的培训状态
                        for (CultureCloudOfflineTrainingEntity training : batchList) {
//                            training.setHasSynced(1); // 设置为已同步
                            cultureCloudOfflineTrainingDao.update(training);
                        }
                        successCount += batchList.size();
                        logger.info("成功同步培训批次: " + (i/batchSize + 1) + ", 数量: " + batchList.size());
                    } else {
                        logger.error("同步培训批次失败: " + (i/batchSize + 1) + ", 错误: " + resultJson.getString("message"));
                    }
                } else {
                    logger.error("同步培训批次失败: " + (i/batchSize + 1) + ", API返回为空");
                }

                totalProcessed += batchList.size();
                logger.info("已处理: " + totalProcessed + "/" + trainingList.size() + " 培训");
            }

            // 设置返回结果
            if (successCount > 0) {
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo("成功同步 " + successCount + "/" + trainingList.size() + " 个培训");
            } else {
                res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                res.setRetInfo("培训同步失败，请查看日志");
            }

        } catch (Exception e) {
            logger.error("同步培训信息到文化广东失败：", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("同步培训信息到文化广东失败：" + e.getMessage());
        }

        return res;
    }

    @Override
    public IResponse syncRoomToWhgd(WhgdSyncReq req) {
        GenericResponse res = new GenericResponse();

        try {
            // 场馆编码映射
            Map<String, String> venueCodeMap = new HashMap<>();
            venueCodeMap.put("佛山市图书馆", "1922547435771662336");
            venueCodeMap.put("佛山市文化馆", "1922213199915126784");
            venueCodeMap.put("佛山市石景宜刘紫英伉俪文化艺术馆", "1922547975033327616");
            venueCodeMap.put("佛山市图书馆祖庙路分馆", "1922545209565122560");
            //把venueCodeMap的key串成字符串，用'连接
            String venueNameStr = venueCodeMap.keySet().stream().collect(Collectors.joining("','"));
            // 获取需要同步的场馆信息
            StringBuilder hql = new StringBuilder("from CultureCloudVenueEntity v where v.venueName in ('"+venueNameStr+"')");
            List<CultureCloudVenueEntity> venueList = cultureCloudVenueDao.getListByHql(hql.toString());
            if (venueList.size() == 0) {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo("没有需要同步的场馆信息");
                return res;
            }

            logger.info("需要同步的场馆总数: " + venueList.size());


            // 场地类型映射
            Map<String, Integer> siteTypeMap = new HashMap<>();
            siteTypeMap.put("琴房", 1);
            siteTypeMap.put("舞蹈室", 2);
            siteTypeMap.put("戏剧室", 3);
            siteTypeMap.put("美术书法室", 4);
            siteTypeMap.put("古筝教室", 5);
            siteTypeMap.put("合唱排练厅", 6);

            // 每批次处理19条数据
            int batchSize = 19;
            int totalProcessed = 0;
            int successCount = 0;
            int roomSuccessCount = 0;

            for (int i = 0; i < venueList.size(); i += batchSize) {
                // 获取当前批次的场馆列表
                int endIndex = Math.min(i + batchSize, venueList.size());
                List<CultureCloudVenueEntity> batchList = venueList.subList(i, endIndex);

                        // 同步场馆下的场地
                        for (CultureCloudVenueEntity venue : batchList) {
                            // 获取场馆编码
                            String venueCode = venueCodeMap.get(venue.getVenueName());
                            if (StringUtils.isEmpty(venueCode)) {
                                continue; // 跳过没有编码的场馆
                            }
                            
                            // 获取场馆下的场地
                            StringBuilder roomHql = new StringBuilder("from CultureCloudRoomEntity r where r.venue.id = " + venue.getId() + " and r.state = 1 and r.roomState = 6 and r.roomIsDel != 2");
                            List<CultureCloudRoomEntity> roomList = cultureCloudRoomDao.getListByHql(roomHql.toString());
                            if (roomList.isEmpty()) {
                                logger.info("场馆 " + venue.getVenueName() + " 下没有场地需要同步");
                                continue; // 跳过没有场地的场馆
                            }
                            
                            logger.info("场馆 " + venue.getVenueName() + " 下有 " + roomList.size() + " 个场地需要同步");
                            
                            // 每批次处理19条场地数据
                            for (int j = 0; j < roomList.size(); j += batchSize) {
                                int roomEndIndex = Math.min(j + batchSize, roomList.size());
                                List<CultureCloudRoomEntity> roomBatchList = roomList.subList(j, roomEndIndex);
                                
                                // 构建场地同步数据
                                List<Map<String, Object>> roomInfoList = new ArrayList<>();
                                
                                for (CultureCloudRoomEntity room : roomBatchList) {
                                    Map<String, Object> roomInfo = new HashMap<>();
                                    
                                    // 根据场地名称判断场地类型，默认为琴房(1)
                                    Integer roomSiteType = 1;
                                    String roomName = room.getRoomName();
                                    for (Map.Entry<String, Integer> entry : siteTypeMap.entrySet()) {
                                        if (roomName != null && roomName.contains(entry.getKey())) {
                                            roomSiteType = entry.getValue();
                                            break;
                                        }
                                    }
                                    
                                    // 填充必填参数
                                    roomInfo.put("tongId", room.getId());
                                    roomInfo.put("siteName", room.getRoomName());
                                    roomInfo.put("venueCode", venueCode);
                                    roomInfo.put("sitePicUrl",
                                            (null != room.getRoomPicUrl() && room.getRoomPicUrl().contains("http"))?room.getRoomPicUrl(): "https" +
                                            "://www" +
                                            ".foshan.gov.cn/fswenhua/cultureCloud" + (room.getRoomPicUrl() != null ?
                                                    room.getRoomPicUrl() : venue.getResourceImgUrl()));
                                    roomInfo.put("siteType", roomSiteType);
                                    roomInfo.put("square", room.getRoomArea() != null ? room.getRoomArea() : "0");
                                    roomInfo.put("capacity", room.getRoomCapacity() != null ? room.getRoomCapacity() : 0);
                                    roomInfo.put("applicantName", StringUtils.isNotEmpty(room.getRoomLiaison())? room.getRoomLiaison() : "无");
                                    roomInfo.put("applicantPhone", StringUtils.isNotEmpty(room.getRoomTel()) ? room.getRoomTel() :
                                            (StringUtils.isNotEmpty(venue.getVenueTel())?
                                            venue.getVenueTel() : "无"));
                                    
                                    // 开放时间
                                    String openHour = " "; // 默认开放时间
                                    if (venue.getVenueOpenTime() != null && venue.getVenueEndTime() != null) {
                                        openHour = venue.getVenueOpenTime() + "-" + venue.getVenueEndTime();
                                    }
                                    roomInfo.put("openHour", openHour);
                                    
                                    roomInfo.put("location", 1); // 默认内部
                                    roomInfo.put("address", venue.getVenueAddress());
                                    roomInfo.put("longitude", venue.getVenueLon() != null ? venue.getVenueLon().toString() : "113.121416");
                                    roomInfo.put("latitude", venue.getVenueLat() != null ? venue.getVenueLat().toString() : "23.021834");
                                    roomInfo.put("introduce", room.getRoomDesc() != null ? room.getRoomDesc() : "无");
                                    roomInfo.put("isNeedAppoint", room.getRoomIsClosed() != null && room.getRoomIsClosed() != 1 ? 1 : 0);
                                    roomInfo.put("isOpenAppoint", room.getRoomIsClosed() != null && room.getRoomIsClosed() != 1 ? 1 : 0);

                                    roomInfo.put("code", venueCode + "_" + room.getId());
                                    
                                    // 填充选填参数
                                    if (room.getRoomFacility() != null) {
                                        roomInfo.put("facility", room.getRoomFacility());
                                    }
                                    if (room.getRoomTel() != null) {
                                        roomInfo.put("sitePhone", room.getRoomTel());
                                    }
                                    
                                    roomInfoList.add(roomInfo);
                                }
                                
                                // 构建API请求参数
                                Map<String, Object> roomSyncData = new HashMap<>();
                                roomSyncData.put("infoList", roomInfoList);
                                
                                // 调用文化广东API同步场地
                                String roomApiUrl = WHGD_API_BASE_URL + "/siteBatchSave";
                                String roomResult = sendToWhgdApi(roomApiUrl, roomSyncData);
                                
                                // 处理同步结果
                                if (StringUtils.isNotEmpty(roomResult)) {
                                    JSONObject roomResultJson = JSONObject.parseObject(roomResult);
                                    if (roomResultJson.getInteger("code") == 2000) {
                                        roomSuccessCount += roomBatchList.size();
                                        logger.info("成功同步场地批次: " + (j/batchSize + 1) + ", 数量: " + roomBatchList.size());
                                        
                                        // 同步场地规则，未启用
//                                        syncRoomRules(roomBatchList, venueCode);
                                    } else {
                                        logger.error("同步场地批次失败: " + (j/batchSize + 1) + ", 错误: " + roomResultJson.getString("message"));
                                    }
                                } else {
                                    logger.error("同步场地批次失败: " + (j/batchSize + 1) + ", API返回为空");
                                }
                            }
                        }

                totalProcessed += batchList.size();
                logger.info("已处理: " + totalProcessed + "/" + venueList.size() + " 场馆");
            }

            // 设置返回结果
            if (successCount > 0) {
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo("成功同步 " + successCount + "/" + venueList.size() + " 个场馆，" + roomSuccessCount + " 个场地");
            } else {
                res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                res.setRetInfo("场馆同步失败，请查看日志");
            }

        } catch (Exception e) {
            logger.error("同步场馆信息到文化广东失败：", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("同步场馆信息到文化广东失败：" + e.getMessage());
        }

        return res;
    }
    
    /**
     * 同步场地规则
     *
     * @param roomList 场地列表
     * @param venueCode 场馆编码
     */
    private void syncRoomRules(List<CultureCloudRoomEntity> roomList, String venueCode) {
        try {
            // 构建同步数据集合
            List<Map<String, Object>> infoList = new ArrayList<>();
            
            // 处理每个场地规则
            for (CultureCloudRoomEntity room : roomList) {
                Map<String, Object> ruleInfo = new HashMap<>();
                
                // 填充必填参数
                ruleInfo.put("tongId", room.getId());
                ruleInfo.put("advance", 1); // 默认提前7天可预约
                
                // 预约公告（选填）
                if (StringUtils.isNotEmpty(room.getOrderRemark())) {
                    ruleInfo.put("notice", room.getOrderRemark());
                }
                
                // 是否开启预约须知
                boolean hasKnowContent = StringUtils.isNotEmpty(room.getOrderRemark());
                ruleInfo.put("isKnow", hasKnowContent ? 1 : 0);
                
                // 预约须知内容
                if (hasKnowContent) {
                    ruleInfo.put("knowContent", room.getOrderRemark());
                }
                
                // 是否需要审核
                Integer auditEnable = room.getAuditEnable();
                ruleInfo.put("reservationNeedAudit", auditEnable != null && auditEnable == 1 ? 1 : 0);
                
                infoList.add(ruleInfo);
            }
            
            // 构建API请求参数
            Map<String, Object> syncData = new HashMap<>();
            syncData.put("infoList", infoList);
            
            // 调用文化广东API
            String apiUrl = WHGD_API_BASE_URL + "/siteRuleBatchSave";
            String result = sendToWhgdApi(apiUrl, syncData);
            
            // 处理同步结果
            if (StringUtils.isNotEmpty(result)) {
                JSONObject resultJson = JSONObject.parseObject(result);
                if (resultJson.getInteger("code") == 200) {
                    logger.info("成功同步场地规则，数量: " + infoList.size());
                } else {
                    logger.error("同步场地规则失败，错误: " + resultJson.getString("message"));
                }
            } else {
                logger.error("同步场地规则失败，API返回为空");
            }
        } catch (Exception e) {
            logger.error("同步场地规则失败：", e);
        }
    }


    @Override
    public IResponse syncAssetToWhgd(WhgdSyncReq req) {
        GenericResponse res = new GenericResponse();

        try {
            // 获取需要同步的资讯信息
            StringBuilder hql = new StringBuilder("from CultureCloudAssetEntity n where n.needSync = 1 and (n.hasSynced = 0 or n.hasSynced = 2)");
            List<CultureCloudAssetEntity> assetList = cultureCloudAssetDao.getListByHql(hql.toString());
            if (assetList.size() == 0) {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo("没有需要同步的资讯信息");
                return res;
            }

            logger.info("需要同步的资讯总数: " + assetList.size());

            // 每批次处理19条数据
            int batchSize = 19;
            int totalProcessed = 0;
            int successCount = 0;

            for (int i = 0; i < assetList.size(); i += batchSize) {
                // 获取当前批次的资讯列表
                int endIndex = Math.min(i + batchSize, assetList.size());
                List<CultureCloudAssetEntity> batchList = assetList.subList(i, endIndex);

                // 构建同步数据集合
                List<Map<String, Object>> infoList = new ArrayList<>();

                // 处理每条资讯
                for (CultureCloudAssetEntity asset : batchList) {
                    Map<String, Object> assetInfo = new HashMap<>();

                    // 填充必填参数
                    assetInfo.put("tongId", asset.getId());
                    assetInfo.put("name", asset.getAssetName());

                    String assetType = "";
                    Map<Integer, List<CultureCloudTagEntity>> collect1 = (Map<Integer, List<CultureCloudTagEntity>>) asset.getTagList()
                            .parallelStream().collect(groupingBy(CultureCloudTagEntity::getCategory));
                    for(Integer key : collect1.keySet()) {
                        if(key==2){
                            for(CultureCloudTagEntity t : collect1.get(key)) {
                                assetType = t.getTagName();
                            }
                        }
                    }

                    assetInfo.put("type", getNewsType(assetType));
                    assetInfo.put("detail", asset.getAssetContent());
                    assetInfo.put("unitName", asset.getAffiliatedUnit() != null ? asset.getAffiliatedUnit() : "佛山市文化馆");
                    assetInfo.put("showed", asset.getAssetState() == 2 ? 1 : 0);
                    assetInfo.put("creatorUnitId", 64929); // 默认机构ID，需要根据实际情况调整

                    // 填充选填参数
                    if (!asset.getSubImageSet().isEmpty()) {
                        assetInfo.put("picUrl", "https://www.foshan.gov.cn/fswenhua/cultureCloud" + asset.getSubImageSet().iterator().next().getImageFile());
                    }

                    if (asset.getUserName() != null) {
                        assetInfo.put("author", asset.getUserName());
                    }

                    infoList.add(assetInfo);
                }

                // 构建API请求参数
                Map<String, Object> syncData = new HashMap<>();
                syncData.put("infoList", infoList);

                // 调用文化广东API
                String apiUrl = WHGD_API_BASE_URL + "/infoBatchSave";
                String result = sendToWhgdApi(apiUrl, syncData);

                // 处理同步结果
                if (StringUtils.isNotEmpty(result)) {
                    JSONObject resultJson = JSONObject.parseObject(result);
                    if (resultJson.getInteger("code") == 2000) {
                        // 更新已同步的资讯状态
                        for (CultureCloudAssetEntity news : batchList) {
                            news.setHasSynced(1); // 设置为已同步
                            cultureCloudAssetDao.update(news);
                        }
                        successCount += batchList.size();
                        logger.info("成功同步资讯批次: " + (i/batchSize + 1) + ", 数量: " + batchList.size());
                    } else {
                        logger.error("同步资讯批次失败: " + (i/batchSize + 1) + ", 错误: " + resultJson.getString("message"));
                    }
                } else {
                    logger.error("同步资讯批次失败: " + (i/batchSize + 1) + ", API返回为空");
                }

                totalProcessed += batchList.size();
                logger.info("已处理: " + totalProcessed + "/" + assetList.size() + " 资讯");
            }

            // 设置返回结果
            if (successCount > 0) {
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo("成功同步 " + successCount + "/" + assetList.size() + " 条资讯");
            } else {
                res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                res.setRetInfo("资讯同步失败，请查看日志");
            }

        } catch (Exception e) {
            logger.error("同步资讯信息到文化广东失败：", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("同步资讯信息到文化广东失败：" + e.getMessage());
        }

        return res;
    }

    @Override
    public IResponse getUserByToken(String token) {
        GenericResponse res = new GenericResponse();

        Integer successCount = 0;
        try {

                // 调用文化广东API
                String apiUrl = WHGD_API_BASE_URL + "/getUserByToken";
                String result = HttpClientUtil.get(apiUrl, "token=" + token,getHeaders());

                // 处理同步结果
                if (StringUtils.isNotEmpty(result)) {
                    JSONObject resultJson = JSONObject.parseObject(result);
                    if (resultJson.getInteger("code") == 2000) {
                        Map resultMap = resultJson.getJSONObject("result");
                        Integer userId = (Integer) resultMap.get("userId");
                        Integer authenticationFlag = (Integer) resultMap.get("authenticationFlag");
                        Integer credentialsType = (Integer) resultMap.get("credentialsType");
                        String headsUrl = resultMap.get("headsUrl").toString();
                        String nickName = resultMap.get("nickName").toString();
                        String mobile = DigestUtil.decryptEcb(SM4KEY, resultMap.get("mobile").toString());
                        String name = DigestUtil.decryptEcb(SM4KEY, resultMap.get("name").toString());
                        String credentialsCode = DigestUtil.decryptEcb(SM4KEY, resultMap.get("credentialsCode").toString());
                        successCount = 1;
                        logger.info("获取信息成功: " + resultMap.toString());
                    } else {
                        logger.error("获取信息失败: " + ", 错误: " + resultJson.getString("message"));
                    }
                } else {
                    logger.error("获取信息失败:  API返回为空");
                }




            // 设置返回结果
            if (successCount > 0) {
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo("成功同步");
            } else {
                res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                res.setRetInfo("资讯同步失败，请查看日志");
            }

        } catch (Exception e) {
            logger.error("同步资讯信息到文化广东失败：", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("同步资讯信息到文化广东失败：" + e.getMessage());
        }

        return res;
    }

    public static IResponse syncActivityEnrollToWhgd(Integer activityId, String orderNum, String accountPhoneNum, List<WhgdPersonInfo> personList) {
        GenericResponse res = new GenericResponse();

        try {
            // 验证请求参数
            if (activityId == null || StringUtils.isEmpty(orderNum) 
                    || StringUtils.isEmpty(accountPhoneNum) || personList == null || personList.isEmpty()) {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo("参数不完整，活动ID、订单编码、账号手机号和报名人员信息不能为空");
                return res;
            }

            logger.info("开始同步活动报名信息，活动ID: " + activityId + ", 订单编码: " + orderNum);

            // 构建同步数据
            Map<String, Object> enrollData = new HashMap<>();
            enrollData.put("activityId", activityId);
            enrollData.put("orderNum", orderNum);
            
            // 加密账号手机号
            String encryptedAccountPhone = DigestUtil.encryptEcb(SM4KEY, accountPhoneNum);
            enrollData.put("accountPhoneNum", encryptedAccountPhone);
            
            // 处理报名人员信息
            List<Map<String, Object>> personInfoList = new ArrayList<>();
            for (WhgdPersonInfo person : personList) {
                Map<String, Object> personInfo = new HashMap<>();
                
                // 核销码（可选）
                if (StringUtils.isNotEmpty(person.getCheckCode())) {
                    personInfo.put("checkCode", person.getCheckCode());
                }
                
                // 加密姓名
                String encryptedName = DigestUtil.encryptEcb(SM4KEY, person.getName());
                personInfo.put("name", encryptedName);
                
                // 证件类型
                personInfo.put("papersType", person.getPapersType());
                
                // 加密证件号
                String encryptedPapers = DigestUtil.encryptEcb(SM4KEY, person.getPapers());
                personInfo.put("papers", encryptedPapers);
                
                // 加密手机号
                String encryptedPhone = DigestUtil.encryptEcb(SM4KEY, person.getPhoneNum());
                personInfo.put("phoneNum", encryptedPhone);
                
                personInfoList.add(personInfo);
            }
            enrollData.put("personList", personInfoList);

            // 调用文化广东API
            String apiUrl = WHGD_API_BASE_URL + "/activityCallbackReport";
            logger.info("数据："+enrollData);
            String result = sendToWhgdApi(apiUrl, enrollData);

            // 处理同步结果
            if (StringUtils.isNotEmpty(result)) {
                JSONObject resultJson = JSONObject.parseObject(result);
                if (resultJson.getInteger("code") == 2000) {
                    logger.info("活动报名信息同步成功，活动ID: " + activityId + ", 订单编码: " + orderNum);
                    res.setRet(ResponseContext.RES_SUCCESS_CODE);
                    res.setRetInfo("活动报名信息同步成功");
                } else {
                    logger.error("活动报名信息同步失败，错误: " + resultJson.getString("message"));
                    res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                    res.setRetInfo("活动报名信息同步失败: " + resultJson.getString("message"));
                }
            } else {
                logger.error("活动报名信息同步失败，API返回为空");
                res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                res.setRetInfo("活动报名信息同步失败，API返回为空");
            }

        } catch (Exception e) {
            logger.error("同步活动报名信息到文化广东失败：", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("同步活动报名信息到文化广东失败：" + e.getMessage());
        }

        return res;
    }

    public static IResponse activityCallbackCancel(Integer activityId, String orderNum) {
        GenericResponse res = new GenericResponse();

        try {
            // 验证请求参数
            if (activityId == null || StringUtils.isEmpty(orderNum)) {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo("参数不完整，活动ID、订单编码信息不能为空");
                return res;
            }

            logger.info("开始同步取消文化广东活动订单，活动ID: " + activityId + ", 订单编码: " + orderNum);

            // 构建同步数据
            Map<String, Object> enrollData = new HashMap<>();
            enrollData.put("activityId", activityId);
            enrollData.put("orderNum", orderNum);


            // 调用文化广东API
            String apiUrl = WHGD_API_BASE_URL + "/activityCallbackCancel";
            String result = sendToWhgdApi(apiUrl, enrollData);

            // 处理同步结果
            if (StringUtils.isNotEmpty(result)) {
                JSONObject resultJson = JSONObject.parseObject(result);
                if (resultJson.getInteger("code") == 2000) {
                    logger.info("同步取消文化广东活动订单成功，活动ID: " + activityId + ", 订单编码: " + orderNum);
                    res.setRet(ResponseContext.RES_SUCCESS_CODE);
                    res.setRetInfo("活动报名信息同步成功");
                } else {
                    logger.error("同步取消文化广东活动订单失败，错误: " + resultJson.getString("message"));
                    res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                    res.setRetInfo("同步取消文化广东活动订单失败: " + resultJson.getString("message"));
                }
            } else {
                logger.error("同步取消文化广东活动订单失败，API返回为空");
                res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                res.setRetInfo("同步取消文化广东活动订单失败，API返回为空");
            }

        } catch (Exception e) {
            logger.error("同步取消文化广东活动订单失败：", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("同步取消文化广东活动订单失败：" + e.getMessage());
        }
        return res;
    }


    public static IResponse venueCallbackBookingToWhgd(String venueName,Integer roomId,Integer type, String orderNum,
            String intoDate, String infoStartTime, String infoEndTime, String timesUniqueId, 
            String accountPhoneNum, Integer approvalStatus, String approvadInfo, List<Map> personList) {
        GenericResponse res = new GenericResponse();

        logger.info("开始同步场馆预约信息，场馆名称: " + venueName + ", 订单编码: " + orderNum + ", 订单类型: " + type);
        try {
            // 验证请求参数
//            if (type == null || StringUtils.isEmpty(orderNum)
//                    || StringUtils.isEmpty(intoDate) || StringUtils.isEmpty(infoStartTime)
//                    || StringUtils.isEmpty(infoEndTime) || StringUtils.isEmpty(timesUniqueId)
//                    || StringUtils.isEmpty(accountPhoneNum) || personList == null || personList.isEmpty()) {
//                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//                res.setRetInfo("参数不完整，场馆编码、类型、订单编码、入馆日期、时间段、场次ID、账号手机号和预约人员信息不能为空");
//                return res;
//            }

            // 场馆编码映射
            Map<String, String> venueCodeMap = new HashMap<>();
            venueCodeMap.put("佛山市图书馆", "1922547435771662336");
            venueCodeMap.put("佛山市文化馆", "1922213199915126784");
            venueCodeMap.put("佛山市石景宜刘紫英伉俪文化艺术馆", "1922547975033327616");
            venueCodeMap.put("佛山市图书馆祖庙路分馆", "1922545209565122560");


            String uniqueCode = venueCodeMap.getOrDefault(venueName,"");
            if (StringUtils.isEmpty(uniqueCode)){
                logger.info("场馆编码不在同步范围内");
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo("场馆编码不在同步范围内");
                return res;
            }else {
                //场地
                if (type == 2){
                    uniqueCode = uniqueCode + "_" + roomId;
                }
            }



            // 构建同步数据
            Map<String, Object> bookingData = new HashMap<>();
            bookingData.put("uniqueCode", uniqueCode);
            bookingData.put("type", type);
            bookingData.put("orderNum", orderNum);
            bookingData.put("intoDate", intoDate);
            bookingData.put("infoStartTime", infoStartTime);
            bookingData.put("infoEndTime", infoEndTime);
            bookingData.put("timesUniqueId", timesUniqueId);
            
            // 加密账号手机号
            String encryptedAccountPhone = DigestUtil.encryptEcb(SM4KEY, accountPhoneNum);
            bookingData.put("accountPhoneNum", encryptedAccountPhone);
            
            // 场地预定审核状态（可选）
            if (type == 2 && approvalStatus != null) {
                bookingData.put("approvalStatus", approvalStatus);
                if (StringUtils.isNotEmpty(approvadInfo)) {
                    bookingData.put("approvadInfo", approvadInfo);
                }
            }
            
            // 处理预约人员信息
            List<Map<String, Object>> checkCodes = new ArrayList<>();
            for (Map person : personList) {
                Map<String, Object> personInfo = new HashMap<>();

                if(null != person.get("checkCode") && StringUtils.isNotEmpty(person.get("checkCode").toString())){
                    personInfo.put("checkCode", person.get("checkCode"));
                }

                // 加密姓名
                String encryptedName = DigestUtil.encryptEcb(SM4KEY, person.get("name").toString());
                personInfo.put("name", encryptedName);
                
                // 证件类型
                personInfo.put("papersType", person.get("papersType"));
                
                // 加密证件号
                String encryptedPapers = DigestUtil.encryptEcb(SM4KEY, person.get("papers").toString());
                personInfo.put("papers", encryptedPapers);
                
                // 加密手机号
                String encryptedPhone = DigestUtil.encryptEcb(SM4KEY, person.get("phoneNum").toString());
                personInfo.put("phoneNum", encryptedPhone);
                
                // 核销状态（可选）
                if (person.get("verifyStatus") != null) {
                    personInfo.put("verifyStatus", person.get("verifyStatus"));
                }
                
                checkCodes.add(personInfo);
            }
            bookingData.put("checkCodes", checkCodes);

            // 调用文化广东API
            String apiUrl = WHGD_API_BASE_URL + "/venueCallbackBooking";
            String result = sendToWhgdApi(apiUrl, bookingData);
            logger.info("同步场馆预约信息到文化广东，API地址: " + apiUrl + ", 请求参数: " + bookingData);

            // 处理同步结果
            if (StringUtils.isNotEmpty(result)) {
                JSONObject resultJson = JSONObject.parseObject(result);
                if (resultJson.getInteger("code") == 2000) {
                    logger.info("场馆预约信息同步成功，场馆编码: " + uniqueCode + ", 订单编码: " + orderNum);
                    res.setRet(ResponseContext.RES_SUCCESS_CODE);
                    res.setRetInfo("场馆预约信息同步成功");
                } else {
                    logger.error("场馆预约信息同步失败，错误: " + resultJson.getString("message"));
                    res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                    res.setRetInfo("场馆预约信息同步失败: " + resultJson.getString("message"));
                }
            } else {
                logger.error("场馆预约信息同步失败，API返回为空");
                res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                res.setRetInfo("场馆预约信息同步失败，API返回为空");
            }

        } catch (Exception e) {
            logger.error("同步场馆预约信息到文化广东失败：", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("同步场馆预约信息到文化广东失败：" + e.getMessage());
        }

        return res;
    }

    /**
     * 发送数据到文化广东API
     *
     * @param apiUrl API地址
     * @param data 请求数据
     * @return API返回结果
     */
    private static String sendToWhgdApi(String apiUrl, Map<String, Object> data) {
        try {
            // 添加公共参数
            Map<String, Object> requestParams = new HashMap<>(data);

            // 获取请求头
            Map<String, String> headers = getHeaders();

            // 发送HTTP请求
            return HttpClientUtil.jsonPost(apiUrl, "UTF-8", JSONObject.toJSONString(requestParams), headers);
        } catch (Exception e) {
            logger.error("调用文化广东API失败：", e);
            return null;
        }
    }

    /**
     * 获取活动类型对应的文化广东字典值
     *
     * @param activityType 活动类型
     * @return 文化广东字典值
     */
    private Integer getActivityType(String activityType) {
        // 根据系统活动类型映射到文化广东活动类型字典值
        if (StringUtils.isEmpty(activityType)) {
            return 4; // 默认类型
        }

        // 这里需要根据实际情况映射类型
        Map<String, Integer> typeMap = new HashMap<>();
        typeMap.put("演出", 1);
        typeMap.put("展览", 2);
        typeMap.put("阅读推广", 3);

        return typeMap.getOrDefault(activityType, 4);
    }

    /**
     * 获取区域编码
     *
     * @param province 省份
     * @param city 城市
     * @param area 区域
     * @return 区域编码
     */
    private String getAreaCode(String province, String city, String area) {
        // 这里需要根据实际情况获取区域编码
        // 默认返回佛山市禅城区编码
        if ("广东省".equals(province) && "佛山市".equals(city)) {
            if ("禅城区".equals(area)) {
                return "440604";
            } else if ("南海区".equals(area)) {
                return "440605";
            } else if ("顺德区".equals(area)) {
                return "440606";
            } else if ("高明区".equals(area)) {
                return "440607";
            } else if ("三水区".equals(area)) {
                return "440608";
            }
        }

        return "440604"; // 默认禅城区
    }

    /**
     * 获取资讯类型对应的文化广东字典值
     *
     * @param newsType 资讯类型
     * @return 文化广东字典值
     */
    private Integer getNewsType(String newsType) {
        // 根据系统资讯类型映射到文化广东资讯类型字典值
        if (StringUtils.isEmpty(newsType)) {
            return 6; // 默认为新闻动态
        }

        // 文化广东资讯类型字典
        Map<String, Integer> typeMap = new HashMap<>();
        typeMap.put("演出资讯", 1);
        typeMap.put("展览资讯", 2);
        typeMap.put("非遗资讯", 3);
        typeMap.put("培训资讯", 4);
        typeMap.put("活动资讯", 5);
        typeMap.put("新闻动态", 6);
        typeMap.put("文化头条", 7);
        typeMap.put("活动公告", 8);
        
        // 兼容旧的类型映射
        typeMap.put("政策法规", 6); // 映射到新闻动态
        typeMap.put("通知公告", 8); // 映射到活动公告
        typeMap.put("行业动态", 6); // 映射到新闻动态
        typeMap.put("文化资讯", 7); // 映射到文化头条

        return typeMap.getOrDefault(newsType, 6); // 默认为新闻动态
    }

    public static IResponse venueCallbackCancel(Integer venueId,String venueName,Integer type, String orderNum) {
        GenericResponse res = new GenericResponse();

        try {

            logger.info("开始同步取消文化广东场馆订单，场馆名称: " + venueName + ", 订单编码: " + orderNum +", 订单类型: " + type);
            // 场馆编码映射
            Map<String, String> venueCodeMap = new HashMap<>();
            venueCodeMap.put("佛山市图书馆", "1922547435771662336");
            venueCodeMap.put("佛山市文化馆", "1922213199915126784");//正式用
            venueCodeMap.put("佛山市石景宜刘紫英伉俪文化艺术馆", "1922547975033327616");
            venueCodeMap.put("佛山市图书馆祖庙路分馆", "1922545209565122560");


            String uniqueCode = venueCodeMap.getOrDefault(venueName,"");
            if (StringUtils.isEmpty(uniqueCode)){
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo("场馆编码不在同步范围内");
                return res;
            }else {
                //场地
                if (type == 2){
                    uniqueCode = uniqueCode + "_" + venueId;
                }
            }


            // 构建同步数据
            Map<String, Object> enrollData = new HashMap<>();
            enrollData.put("uniqueCode", uniqueCode);
            enrollData.put("type",type);
            enrollData.put("orderNum", orderNum);


            // 调用文化广东API
            String apiUrl = WHGD_API_BASE_URL + "/venueCallbackCancel";
            String result = sendToWhgdApi(apiUrl, enrollData);

            // 处理同步结果
            if (StringUtils.isNotEmpty(result)) {
                JSONObject resultJson = JSONObject.parseObject(result);
                if (resultJson.getInteger("code") == 2000) {
                    logger.info("同步取消文化广东场馆订单成功，场馆名称: " + venueName + ", 订单编码: " + orderNum);
                    res.setRet(ResponseContext.RES_SUCCESS_CODE);
                    res.setRetInfo("取消场馆报名信息同步成功");
                } else {
                    logger.error("同步取消文化广东场馆订单失败，错误: " + resultJson.getString("message"));
                    res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                    res.setRetInfo("同步取消文化广东场馆订单失败: " + resultJson.getString("message"));
                }
            } else {
                logger.error("同步取消文化广东场馆订单失败，API返回为空");
                res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                res.setRetInfo("同步取消文化广东场馆订单失败，API返回为空");
            }

        } catch (Exception e) {
            logger.error("同步取消文化广东场馆订单失败：", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("同步取消文化广东场馆订单失败：" + e.getMessage());
        }
        return res;
    }


    @Override
    public String cancelWhgdActivityOrder(WhgdCancelOrderReq req) {
        Map<String, Object> res = new HashMap<>();
        res.put("success", true);
        try {
            logger.info("开始处理文化广东取消订单请求，活动ID: " + req.getActivityId() + ", 订单编号: " + req.getOrderNum());

            CultureCloudActivityOrderEntity order = cultureCloudActivityOrderDao.getUniqueByHql("from CultureCloudActivityOrderEntity where " +
                    "orderNumber = '"+req.getOrderNum()+"' and orderPayStatus not in (2,4,5)");
            if (order == null) {
                logger.error("未找到对应的订单，订单编号: " + req.getOrderNum());
                res.put("code", ResponseContext.RES_DATA_ERROR_CODE);
                res.put("message", "未找到对应的订单，订单编号: " + req.getOrderNum());
                return JSONObject.toJSONString(res);
            }

            List<CultureCloudVenueSeatEntity> seatList = cultureCloudVenueSeatDao.getListByHql("select a from CultureCloudVenueSeatEntity a "
                            + "inner join a.activity b inner join a.member c  where b.id=" + order.getActivityId() + " and c.id=" + order.getMember().getId(),
                    "");
            if (null != seatList) {
                seatList.forEach(o -> {
                    o.setMember(null);
                    o.setSeatStatus(1);
                });
            }
            order.setOrderPayStatus(Short.valueOf("2"));

            //恢复票数
            CultureCloudActivityEventEntity event = cultureCloudActivityEventDao.get(Integer.parseInt(order.getEventId()));
            event.setAvailableCount(event.getAvailableCount() + order.getOrderVotes());
            cultureCloudActivityEventDao.update(event);

            String content = "【佛山文化云】亲爱的"+order.getOrderName()+"，您预定的"+order.getActivityName()+"活动的"+order.getOrderVotes()+"张票已成功退订";
            String phone = order.getOrderPhoneNo();
            sendSms(phone, content);

            res.put("code", 2000);
            res.put("message", "操作成功");
            return JSONObject.toJSONString(res);
        } catch (Exception e) {
            logger.error("处理文化广东取消订单请求失败：", e);
            res.put("code", ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.put("message", "处理文化广东取消订单请求失败,请联系管理员");
            return JSONObject.toJSONString(res);
        }
    }

}
