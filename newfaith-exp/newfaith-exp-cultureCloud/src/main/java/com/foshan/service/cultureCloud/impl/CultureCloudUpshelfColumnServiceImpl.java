package com.foshan.service.cultureCloud.impl;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.ColumnEntity;
import com.foshan.entity.cultureCloud.CultureCloudUpshelfColumnEntity;
import com.foshan.form.ArticleAssetForm;
import com.foshan.form.ArticleForm;
import com.foshan.form.UpshelfColumnForm;
import com.foshan.form.cultureCloud.request.CultureCloudUpshelfColumnReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.upshelfColumn.AddUpshelfColumnRes;
import com.foshan.form.response.upshelfColumn.DeleteUpshelfColumnRes;
import com.foshan.form.response.upshelfColumn.GetUpshelfColumnInfoRes;
import com.foshan.form.response.upshelfColumn.GetUpshelfColumnListRes;
import com.foshan.form.response.upshelfColumn.ModifyUpshelfColumnRes;
import com.foshan.service.annotation.Audit;
import com.foshan.service.cultureCloud.ICultureCloudUpshelfColumnService;
import com.foshan.util.DateUtil;

@Transactional
@Service("cultureCloudUpshelfColumnService")
public class CultureCloudUpshelfColumnServiceImpl extends GenericCultureCloudService implements ICultureCloudUpshelfColumnService {
	@Override
	public IResponse getCultureCloudUpshelfColumnList(CultureCloudUpshelfColumnReq req) {
		GetUpshelfColumnListRes res = new GetUpshelfColumnListRes();
		if(null != req.getColumnId()) {
			Page<CultureCloudUpshelfColumnEntity> page = new Page<CultureCloudUpshelfColumnEntity>();
			page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
			page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1)
					* page.getPageSize());
			page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
	
			StringBuilder hql = new StringBuilder("select a from CultureCloudUpshelfColumnEntity a "
					+ "where a.column.id="+req.getColumnId());
			if (null != req.getIsRecommend()) {
				hql = hql.append(" and a.isRecommend=" + req.getIsRecommend() );
			}
			if (null != req.getType()) {
				hql = hql.append(" and a.type=" + req.getType() );
			}
	
			hql = (hql.toString().endsWith("where") ? hql.delete(hql.length() - 5, hql.length())
					: hql.toString().endsWith("and") ? hql.delete(hql.length() - 3, hql.length()) : hql)
							.append(" ORDER BY a.orderNumber,a.orderTime desc");
	
			page = cultureCloudUpshelfColumnDao.queryPage(page, hql.toString());
	
			res.setTotalResult(page.getTotalCount());
			res.setPageSize(page.getPageSize());
			res.setCurrentPage(page.getCurrentPage());
			res.setTotal(page.getTotalPage());
	
			page.getResultList().forEach(o -> {
				UpshelfColumnForm upshelfColumnForm = new UpshelfColumnForm(null,o.getId(),o.getIsRecommend(),
					o.getOrderNumber(),o.getResourceId(),new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").
					format(o.getUpshelfTime()),
					o.getTerminalType(),o.getType(),o.getAttributeValue1(),o.getAttributeValue2(),
					o.getAttributeValue3(),o.getAttributeValue4(),o.getAttributeValue5()) ;
				upshelfColumnForm.setRemarks(o.getRemarks());
				upshelfColumnForm.setTargetUrl(o.getTargetUrl());
				upshelfColumnForm.setTitle(o.getTitle());
				upshelfColumnForm.setImage(getAsset(o.getAsset()));

				if(o.getType()==1) {
					upshelfColumnForm.setArticleForm(getArticleForm(o.getResourceId()));
				} else if(o.getType()==0){
					upshelfColumnForm.setAssetForm(getAsset(assetDao.get(o.getResourceId())));
				} 
				
				res.getUpshelfColumnList().add(upshelfColumnForm);
			});
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		
		return res;
	}

	@Override
	public IResponse getCultureCloudUpshelfColumnInfo(CultureCloudUpshelfColumnReq req) {
		GetUpshelfColumnInfoRes res = new GetUpshelfColumnInfoRes();
		if(null != req.getUpshelfColumnId()) {
			CultureCloudUpshelfColumnEntity upshelfColumn = cultureCloudUpshelfColumnDao.get(req.getUpshelfColumnId());
			if(null!=upshelfColumn) {
				UpshelfColumnForm upshelfColumnForm = new UpshelfColumnForm(null,
						upshelfColumn.getId(),upshelfColumn.getIsRecommend(),
						upshelfColumn.getOrderNumber(),upshelfColumn.getResourceId(),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(upshelfColumn.getUpshelfTime()),
						upshelfColumn.getTerminalType(),upshelfColumn.getType(),
						upshelfColumn.getAttributeValue1(),upshelfColumn.getAttributeValue2(),
						upshelfColumn.getAttributeValue3(),upshelfColumn.getAttributeValue4(),
						upshelfColumn.getAttributeValue5()) ;
				upshelfColumnForm.setRemarks(upshelfColumn.getRemarks());

				if(upshelfColumn.getType()==1) {
					upshelfColumnForm.setArticleForm(getArticleForm(upshelfColumn.getResourceId()));
				}
				res.setUpshelfColumnForm(upshelfColumnForm);
			}else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	public ArticleForm getArticleForm(Integer articleId) {
		ArticleForm articleForm = null;
		if(null != articleId) {
			AssetEntity article =  assetDao.get(articleId);
			articleForm = new ArticleForm(article.getId(),article.getAssetName() ,
					article.getSubTitle() ,article.getSummaryShort() ,article.getUserName() ,
					null ,article.getAssetOrders() ,null ,
					article.getIdea() ,article.getContentType() ,
					article.getContentUrl() ,article.getSource() ,article.getResourceName() ,
					null,//new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(article.getCreateTime()) ,
					null,//new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(article.getLastModifyTime()) ,
					null,//new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(article.getPublishTime()) ,
					null//article.getState() 
					,null);
			List<ArticleAssetForm> articleAssetList = new ArrayList<ArticleAssetForm>();
/*			article.getArticleAssetList().forEach(o->{
				AssetForm assetForm = new AssetForm();
				BeanUtils.copyProperties(o.getAsset(), assetForm);
				assetForm.setAssetId(o.getAsset().getId());
				ArticleAssetForm  articleAssetForm = new ArticleAssetForm(o.getId(),o.getImageType() ,
					o.getOrderNumber() ,assetForm);
				articleAssetList.add(articleAssetForm);
			});*/
			//子分类按OrderNumber升序排序
			Collections.sort(articleAssetList,new Comparator<ArticleAssetForm>(){
				public int compare(ArticleAssetForm o1, ArticleAssetForm o2) {
					if(null==o1.getOrderNumber() || null==o2.getOrderNumber()) {
						return -1;
					}
					if(o1.getOrderNumber()<o2.getOrderNumber()){
						return -1;
					}
					if(o1.getOrderNumber()==o2.getOrderNumber())
						return 0;
					return 1;
				}
			});

			articleForm.setArticleAssetList(articleAssetList);
		}

		return articleForm;
	}
	
	@Audit(operate = "增加栏目内容上架")
	public IResponse addCultureCloudUpshelfColumn(CultureCloudUpshelfColumnReq req) {
		AddUpshelfColumnRes res = new AddUpshelfColumnRes();
		if (null!=req.getColumnId() && null != req.getTerminalType() && null!=req.getType() 
				&& StringUtils.isNotEmpty(req.getResourceIdList()) && 
				StringUtils.isNotEmpty(req.getStartTime()) &&
				StringUtils.isNotEmpty(req.getEndTime())) {
			Timestamp startTime = null;
			Timestamp endTime = null;
			try {
				startTime = new Timestamp(DateUtil.parseLongFormat(req.getStartTime()).getTime());
				endTime = new Timestamp(DateUtil.parseLongFormat(req.getEndTime()).getTime());
			} catch (ParseException e) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			
			if(endTime.before(startTime)) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO+"结束时间不能比开始时间早！");
				return res;
			}	
			ColumnEntity column = columnDao.get(req.getColumnId());
			if(null != column) {
				String[] resourceIdList = req.getResourceIdList().split(",");
				//Timestamp timeStamp = new Timestamp(new Date().getTime());
				for (String resourceId : resourceIdList) {
					CultureCloudUpshelfColumnEntity upshelfColumn = new CultureCloudUpshelfColumnEntity();
/*					upshelfColumn.setColumn(column);
					upshelfColumn.setEndTime(endTime);
					upshelfColumn.setIsRecommend(null!=req.getIsRecommend() ? req.getIsRecommend() :0);
					upshelfColumn.setIsTop(null!=req.getIsTop() ? req.getIsTop() : 0);
					upshelfColumn.setResourceId(Integer.valueOf(resourceId));
					upshelfColumn.setStartTime(startTime);
					upshelfColumn.setTerminalType(req.getTerminalType());
					upshelfColumn.setType(req.getType());
					upshelfColumn.setOrderNumber(0);
					upshelfColumn.setUpshelfTime(timeStamp);*/
					upshelfColumn.setColumn(column);
					upshelfColumn.setOrderNumber(null!=req.getOrderNumber()?req.getOrderNumber():0);
					upshelfColumn.setIsRecommend(null != req.getIsRecommend() ? req.getIsRecommend() : 0);
					upshelfColumn.setResourceId(Integer.valueOf(resourceId));
					upshelfColumn.setType(req.getType());
					upshelfColumn.setTerminalType(req.getTerminalType());
					upshelfColumn.setEndTime(endTime);
					upshelfColumn.setStartTime(startTime);
					upshelfColumn.setIsTop(null != req.getIsTop() ? req.getIsTop() : 0);
					upshelfColumn.setTitle(StringUtils.isNotEmpty(req.getTitle()) ? req.getTitle() : "");
					upshelfColumn.setTargetUrl(StringUtils.isNotEmpty(req.getTargetUrl()) ? req.getTargetUrl() : "");
					upshelfColumn.setRemarks(StringUtils.isNotEmpty(req.getRemarks()) ? req.getRemarks() : "");
					upshelfColumn.setOrderTime(new Timestamp(new Date().getTime()));
					upshelfColumn.setAttributeValue1(StringUtils.isNotEmpty(req.getAttributeValue1()) 
							? req.getAttributeValue1() : "");
					upshelfColumn.setAttributeValue2(StringUtils.isNotEmpty(req.getAttributeValue2()) 
							? req.getAttributeValue2() : "");
					upshelfColumn.setAttributeValue3(StringUtils.isNotEmpty(req.getAttributeValue3()) 
							? req.getAttributeValue3() : "");
					upshelfColumn.setAttributeValue4(StringUtils.isNotEmpty(req.getAttributeValue4()) 
							? req.getAttributeValue4() : "");
					upshelfColumn.setAttributeValue5(StringUtils.isNotEmpty(req.getAttributeValue5()) 
							? req.getAttributeValue5() : "");
					cultureCloudUpshelfColumnDao.save(upshelfColumn);
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	@Audit(operate = "修改栏目内容上架")
	public IResponse modifyCultureCloudUpshelfColumn(CultureCloudUpshelfColumnReq req) {
		ModifyUpshelfColumnRes res = new ModifyUpshelfColumnRes();
		if (null!=req.getColumnId() && null != req.getTerminalType() && null!=req.getType() 
				&& StringUtils.isNotEmpty(req.getStartTime()) &&
				StringUtils.isNotEmpty(req.getEndTime()) && StringUtils.isNotEmpty(req.getUpshelfColumnIdList())&&null!=req.getResourceId()) {
			Timestamp startTime = null;
			Timestamp endTime = null;
			try {
				startTime = new Timestamp(DateUtil.parseLongFormat(req.getStartTime()).getTime());
				endTime = new Timestamp(DateUtil.parseLongFormat(req.getEndTime()).getTime());
			} catch (ParseException e) {
				System.out.print(e.getMessage());
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			if(endTime.before(startTime)) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO+"结束时间不能比开始时间早！");
				return res;
			}	
			ColumnEntity column = columnDao.get(req.getColumnId());
			if(null != column) {
				String[] upshelfColumnIdList = req.getUpshelfColumnIdList().split(",");
				Timestamp timeStamp = new Timestamp(new Date().getTime());
				for (String upshelfColumnId : upshelfColumnIdList) {
					CultureCloudUpshelfColumnEntity upshelfColumn = cultureCloudUpshelfColumnDao.get(Integer.valueOf(upshelfColumnId));
					if(null != upshelfColumn) {
						upshelfColumn.setColumn(column);
						upshelfColumn.setEndTime(endTime);
						upshelfColumn.setType(req.getType());
						upshelfColumn.setResourceId(req.getResourceId());
						upshelfColumn.setIsRecommend(null!=req.getIsRecommend() 
								? req.getIsRecommend() :upshelfColumn.getIsRecommend());
						upshelfColumn.setIsTop(null!=req.getIsTop() 
								? req.getIsTop() : upshelfColumn.getIsTop());
						upshelfColumn.setStartTime(startTime);
						upshelfColumn.setTerminalType(req.getTerminalType());
						//upshelfColumn.setType(req.getType());
						upshelfColumn.setUpshelfTime(timeStamp);
						upshelfColumn.setTitle(StringUtils.isNotEmpty(req.getTitle()) 
								? req.getTitle() : upshelfColumn.getTitle());
						upshelfColumn.setTargetUrl(StringUtils.isNotEmpty(req.getTargetUrl()) 
								? req.getTargetUrl() : "");
						upshelfColumn.setRemarks(StringUtils.isNotEmpty(req.getRemarks()) 
								? req.getRemarks() : "");
						upshelfColumn.setOrderNumber(null!=req.getOrderNumber()?req.getOrderNumber() : upshelfColumn.getOrderNumber());
						upshelfColumn.setAttributeValue1(StringUtils.isNotEmpty(req.getAttributeValue1()) 
								? req.getAttributeValue1() : "");
						upshelfColumn.setAttributeValue2(StringUtils.isNotEmpty(req.getAttributeValue2()) 
								? req.getAttributeValue2() : "");
						upshelfColumn.setAttributeValue3(StringUtils.isNotEmpty(req.getAttributeValue3()) 
								? req.getAttributeValue3() : "");
						upshelfColumn.setAttributeValue4(StringUtils.isNotEmpty(req.getAttributeValue4()) 
								? req.getAttributeValue4() : "");
						upshelfColumn.setAttributeValue5(StringUtils.isNotEmpty(req.getAttributeValue5()) 
								? req.getAttributeValue5() : "");
						try {
							upshelfColumn.setOrderTime(StringUtils.isNotEmpty(req.getOrderTime()) ? 
									new Timestamp(DateUtil.parseLongFormat(req.getOrderTime()).getTime()) 
									: upshelfColumn.getOrderTime());
						} catch (ParseException e) {
							e.printStackTrace();
						}
						if (null != req.getAssetId()
								&& (null == upshelfColumn.getAsset() || 
								(null != upshelfColumn.getAsset()
								&& upshelfColumn.getAsset().getId() != req.getAssetId()))) {
							upshelfColumn.setAsset(assetDao.get(req.getAssetId()));
						}
						cultureCloudUpshelfColumnDao.update(upshelfColumn);
					}else {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
						return res;
					}
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	@Audit(operate = "栏目内容下架")
	public IResponse deleteCultureCloudUpshelfColumn(CultureCloudUpshelfColumnReq req) {
		DeleteUpshelfColumnRes res = new DeleteUpshelfColumnRes();
		if (StringUtils.isNotEmpty(req.getUpshelfColumnIdList())) {
			String[] upshelfColumnIdList = req.getUpshelfColumnIdList().split(",");
			for (String upshelfColumnId : upshelfColumnIdList) {
				CultureCloudUpshelfColumnEntity upshelfColumn = cultureCloudUpshelfColumnDao.get(Integer.valueOf(upshelfColumnId));
				if(null!=upshelfColumn) {
					upshelfColumn.setColumn(null);
					upshelfColumn.setAsset(null);
					cultureCloudUpshelfColumnDao.delete(upshelfColumn);
				}else {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
			}
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Audit(operate = "修改栏目内容排序")
	public IResponse setCultureCloudUpshelfColumnOrderNumber(CultureCloudUpshelfColumnReq req) {
		ModifyUpshelfColumnRes res = new ModifyUpshelfColumnRes();
		if (null != req.getUpshelfColumnId()) {
			CultureCloudUpshelfColumnEntity upshelfColumn = cultureCloudUpshelfColumnDao.get(req.getUpshelfColumnId());
			if(null!=upshelfColumn) {
				upshelfColumn.setOrderNumber(null!=req.getOrderNumber()? req.getOrderNumber() :upshelfColumn.getOrderNumber());
				try {
					upshelfColumn.setOrderTime(StringUtils.isNotEmpty(req.getOrderTime()) ? 
							new Timestamp(DateUtil.parseLongFormat(req.getOrderTime()).getTime()) 
							: upshelfColumn.getOrderTime());
				} catch (ParseException e) {
					e.printStackTrace();

				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}			
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}


	@Audit(operate = "修改上架时间")
	public IResponse setCultureCloudUpshelfColumnUpshelfTime(CultureCloudUpshelfColumnReq req) {
		ModifyUpshelfColumnRes res = new ModifyUpshelfColumnRes();
		if (null != req.getUpshelfColumnId() && StringUtils.isNotEmpty(req.getUpshelfTime())) {
			CultureCloudUpshelfColumnEntity upshelfColumn = cultureCloudUpshelfColumnDao.get(req.getUpshelfColumnId());
			if(null!=upshelfColumn) {
				try {
					upshelfColumn.setUpshelfTime(new Timestamp(DateUtil.parseLongFormat(req.getOrderTime()).getTime()));
				} catch (ParseException e) {
					e.printStackTrace();
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}			
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}


}
