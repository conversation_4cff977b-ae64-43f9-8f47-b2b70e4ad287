package com.foshan.form.health;

import com.foshan.form.AssetForm;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="活动对象(HealthReservationActivitiesForm)")
@JsonInclude(Include.NON_NULL)
public  class HealthReservationActivitiesForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = 3985018705495499964L;
	
	@ApiModelProperty(value = "活动ID", example = "1")
	protected Integer activitiesId;
	@ApiModelProperty(value = "预约活动名称")
	protected String activitiesName;
	@ApiModelProperty(value = "介绍")
	protected String content;
	@ApiModelProperty(value = "协议")
	protected String agreement;
	@ApiModelProperty(value = "开始时间")
	protected String startTime;
	@ApiModelProperty(value = "结束时间")
	protected String endTime;
	@ApiModelProperty(value = "需要确认协议 0否1是", example = "1")
	protected Integer needConfirmAgreement;
	@ApiModelProperty(value = "预约种类", example = "1")
	protected Integer reservationType;
	@ApiModelProperty(value = "创建时间")
	protected String createTime;
	@ApiModelProperty(value = "修改时间")
	protected String lastModifyTime;
	@ApiModelProperty(value = "状态 0--无效数据 1--有效数据", example = "1")
	protected Integer state;
	@ApiModelProperty(value = "排序值", example = "1")
	protected Integer orders;
	@ApiModelProperty(value = "使用 0否 1是", example = "1")
	protected Integer employ;
	@ApiModelProperty(value = "策略")
	protected HealthReservationStrategyForm strategyForm;
	@ApiModelProperty(value = "站点")
	protected ServiceStationForm stationForm;
	@ApiModelProperty(value = "会员")
	protected HealthMemberForm member;
	@ApiModelProperty(value = "图片", example = "1")
	protected List<AssetForm> imageList = new ArrayList<>();

	
	
	public HealthReservationActivitiesForm(Integer activitiesId, String activitiesName, String content, String agreement,
										   String startTime, String endTime, Integer needConfirmAgreement, String createTime, String lastModifyTime
			, Integer state, Integer orders,Integer reservationType) {
		this.activitiesId = activitiesId;
		this.activitiesName = activitiesName;
		this.content = content;
		this.agreement = agreement;
		this.startTime = startTime;
		this.endTime = endTime;
		this.needConfirmAgreement = needConfirmAgreement;
		this.createTime = createTime;
		this.lastModifyTime = lastModifyTime;
		this.state = state;
		this.orders = orders;
		this.reservationType = reservationType;
	}
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
