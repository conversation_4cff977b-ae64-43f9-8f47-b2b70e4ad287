package com.foshan.service.health.impl;

import com.foshan.dao.generic.Page;
import com.foshan.entity.MenuEntity;
import com.foshan.entity.PermissionEntity;
import com.foshan.entity.UserEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.health.HealthRoleEntity;
import com.foshan.entity.health.ServiceStationEntity;
import com.foshan.form.PermissionForm;
import com.foshan.form.PermissionGroupForm;
import com.foshan.form.RoleForm;
import com.foshan.form.health.HealthRoleForm;
import com.foshan.form.health.ServiceStationForm;
import com.foshan.form.health.request.HealthRoleReq;
import com.foshan.form.health.response.role.GetHealthRoleInfoRes;
import com.foshan.form.health.response.role.GetHealthRoleListRes;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.role.*;
import com.foshan.service.annotation.Audit;
import com.foshan.service.health.IHealthRoleService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Transactional
@Service("healthRoleService")
public class HealthRoleServiceImpl extends GenericHealthService implements IHealthRoleService {

    @Override
    @Audit(operate = "增加管理员角色")
    public AddRoleRes addRole(HealthRoleReq req) {
        AddRoleRes res = new AddRoleRes();

        UserEntity loginUser = getCurrentUser();
        if (null == loginUser) {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
            return res;
        }

        if (StringUtils.isEmpty(req.getRoleName()) || StringUtils.isEmpty(req.getDisplayName())) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "roleName & parentRoleId & displayName参数不能为空！");
            return res;
        }
        HealthRoleEntity r = healthRoleDao.getUniqueByHql("from HealthRoleEntity where roleName = '" + req.getRoleName()
                + "' and roleState = " + EntityContext.RECORD_STATE_VALID);
        if (null != r) {
            res.setRet(ResponseContext.RES_DATA_DUPLICATE_CODE);
            res.setRetInfo(ResponseContext.RES_DATA_DUPLICATE_INFO + "已有该角色名！");
            return res;
        }
        HealthRoleEntity role = new HealthRoleEntity();

        if (StringUtils.isNotEmpty(req.getStationIds())) {
            for (String stationId : req.getStationIds().split(",")) {
                ServiceStationEntity station = serviceStationDao.get(Integer.valueOf(stationId));
                if (null == station) {
                    res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                    res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "无此站点！");
                    return res;
                }
                role.getStationList().add(station);
            }
        }



        // 设置父色角
        HealthRoleEntity parentRole = null;
        if (req.getParentRoleId() == null) {
            // 获取根管理员
            parentRole = healthRoleDao.getUniqueByHql("from HealthRoleEntity where isRoot = 1 and isBuiltIn = 1 and roleState ="
                    + EntityContext.RECORD_STATE_VALID);
        } else {
            parentRole = healthRoleDao.getUniqueByHql("from HealthRoleEntity where id = " + req.getParentRoleId()
                    + " and roleState = " + EntityContext.RECORD_STATE_VALID);
        }

        if (null == parentRole) {
            res.setRet(ResponseContext.RES_NULL_ERROR_INFO);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "无此父角色！");
            return res;
        }
        role.setParentRole(parentRole);

        // 设置角色权限
        if (StringUtils.isNotEmpty(req.getPermissionIdList())) {
            // 获取父权限
            List<PermissionEntity> parentRolePermissions = new ArrayList<PermissionEntity>();
            if (parentRole.isBuiltIn() && parentRole.isRoot() && parentRole.getRoleState() == 1) {
                parentRolePermissions = permissionDao.getList();
            } else {
                parentRolePermissions = parentRole.getPermissionList();
            }

            String parentRolePermissionsStr = "";
            if (!parentRolePermissions.isEmpty()) {
                for (PermissionEntity p : parentRolePermissions) {
                    parentRolePermissionsStr = parentRolePermissionsStr + p.getId() + ",";
                }
                parentRolePermissionsStr = parentRolePermissionsStr.substring(0, parentRolePermissionsStr.length() - 1);
            }

            // 限制权限不能超出父角色
            List<PermissionEntity> permissionList = permissionDao.getListByHql("from PermissionEntity where id in ("
                    + req.getPermissionIdList() + ") and id in (" + parentRolePermissionsStr + ")");
            role.getPermissionList().addAll(permissionList);
        }
        if (StringUtils.isNotEmpty(req.getMenuIdList())) {
            List<MenuEntity> menuList = menuDao.getListByHql("from MenuEntity where id in ("
                    + req.getMenuIdList() + ")");
            role.getMenuList().addAll(menuList);
        }


        role.setStationList(new ArrayList<>());
        role.setRoleName(req.getRoleName());
        role.setRoleState(req.getRoleState() == null ? 1 : req.getRoleState());
        role.setDisplayName(req.getDisplayName());
        Integer roleId = (Integer) healthRoleDao.save(role);


        res.setRoleId(roleId);
        res.setRoleName(role.getRoleName());
        res.setBuiltIn(role.isBuiltIn());
        res.setDisplayName(role.getDisplayName());
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        return res;
    }

    @Override
    public GetHealthRoleInfoRes getRoleInfo(HealthRoleReq req) {
        GetHealthRoleInfoRes res = new GetHealthRoleInfoRes();
        UserEntity loginUser = getCurrentUser();
        if (null == loginUser) {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
            return res;
        }
        if (null == req.getRoleId()) {
            res.setRet(ResponseContext.RES_NULL_ERROR_INFO);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
            return res;
        }
        HealthRoleEntity role = healthRoleDao.getUniqueByHql("from HealthRoleEntity where id = " + req.getRoleId() + " and isRoot != 1");// 不可查根角色
        if (null == role) {
            res.setRet(ResponseContext.RES_DATA_NULL_CODE);
            res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "无此角色!");
            return res;
        }
        List<PermissionEntity> permissionList = role.getPermissionList();
        List<PermissionGroupForm> permissionGroupList = new ArrayList<PermissionGroupForm>();
        List<PermissionForm> permissionFormList = new ArrayList<PermissionForm>();
        HealthRoleForm rf = new HealthRoleForm();
        rf.setPermissionList(null);
        if (req.getOnlyPermission() == null || req.getOnlyPermission() == 0) {
            Map<String, List<PermissionEntity>> pm = permissionList.stream()
                    .collect(Collectors.groupingBy(PermissionEntity::getGroupName));
            pm.forEach((g, p) -> {
                PermissionGroupForm gf = new PermissionGroupForm();
                gf.setGroupName(g);
                List<PermissionForm> permissions = new ArrayList<PermissionForm>();
                p.forEach(o -> {
                    PermissionForm f = new PermissionForm();
                    f.setId(o.getId());
                    f.setPermissionName(o.getPermissionName());
                    f.setDisplayName(o.getDisplayName());
                    f.setDescription(StringUtils.isNotEmpty(o.getDescription()) ? o.getDescription() : "");
                    f.setUrl(StringUtils.isNotEmpty(o.getUrl()) ? o.getUrl() : "");
                    // f.setGroupName(o.getGroupName());
                    permissions.add(f);
                });
                gf.setPermissionList(permissions);
                permissionGroupList.add(gf);
            });
            rf.setPermissionGroupList(permissionGroupList);
        } else if (req.getOnlyPermission() == 1) {
            permissionList.stream().distinct().collect(Collectors.toList()).forEach(o -> {
                PermissionForm f = new PermissionForm();
                f.setId(o.getId());
                f.setPermissionName(o.getPermissionName());
                f.setDisplayName(o.getDisplayName());
                permissionFormList.add(f);
            });
            rf.setPermissionList(permissionFormList);
        }

        rf.setStationList(null);
        if (!role.getStationList().isEmpty()) {
            for (ServiceStationEntity station : role.getStationList()) {
                ServiceStationForm sf = new ServiceStationForm();
                sf.setStationId(station.getId());
                sf.setStationName(station.getStationName());
                sf.setStationDetail(station.getStationDetail());
                rf.getStationList().add(sf);
            }
        }

        rf.setRoleId(role.getId());
        rf.setRoleName(role.getRoleName());
        rf.setBuiltIn(role.isBuiltIn());
        rf.setRoleState(role.getRoleState());
        rf.setRoleName(role.getRoleName());

        rf.setDisplayName(role.getDisplayName());
        res.setRole(rf);
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        return res;
    }

    @Override
    public GetHealthRoleListRes getRoleList(HealthRoleReq req) {
        GetHealthRoleListRes res = new GetHealthRoleListRes();
        UserEntity loginUser = getCurrentUser();
        if (null == loginUser) {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
            return res;
        }
        Page<HealthRoleEntity> page = new Page<HealthRoleEntity>();
        page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 100);
        page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
        page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
        String hql = "from HealthRoleEntity role where isRoot != 1";
        if (StringUtils.isNotEmpty(req.getDisplayName())) {
            hql += " and displayName like '%" + req.getDisplayName() + "%'";
        }
        if (req.getRoleState() != null) {
            hql += " and roleState =" + req.getRoleState() + "";
        }

        if (StringUtils.isNotEmpty(req.getParentRoleIdList())) {
            List<HealthRoleEntity> parentRoles = healthRoleDao.getListByHql("from HealthRoleEntity role where id in ("
                    + req.getParentRoleIdList() + ") and roleState = " + EntityContext.RECORD_STATE_VALID);
            if (parentRoles.size() == 0) {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "存在无效父角色id!");
                return res;
            }
            hql += " and role.parentRole.id in (" + req.getParentRoleIdList() + ")";
        }
        page = healthRoleDao.queryPage(page, hql);
        List<HealthRoleEntity> roleList = page.getResultList();

        // 获取当前用户有权查看的角色id，用于过滤无权查看的角色
        List<HealthRoleForm> rfList = new ArrayList<HealthRoleForm>();
        if (!roleList.isEmpty()) {
            int totalResult = roleList.size();
            int total = 0;
            int pageSize = req.getPageSize();
            total = (int) (totalResult == 0 ? 0
                    : (totalResult > pageSize ? Math.ceil((double) totalResult / (double) pageSize) : 1));
            int startIndex = ((req.getRequestPage() < total ? req.getRequestPage() : total) - 1) * pageSize;
            int endIndex = (startIndex + pageSize) < totalResult ? startIndex + pageSize : totalResult;
            List<HealthRoleEntity> subRoleList = roleList.subList(startIndex, endIndex);
            subRoleList.forEach(o -> {
                HealthRoleForm r = new HealthRoleForm();
                r.setRoleId(o.getId());
                r.setRoleName(o.getRoleName());
                r.setBuiltIn(o.isBuiltIn());
                r.setRoleState(o.getRoleState());
                r.setDisplayName(o.getDisplayName());

                r.setStationList(null);
                if (!o.getStationList().isEmpty()) {
                    for (ServiceStationEntity station : o.getStationList()) {
                        ServiceStationForm sf = new ServiceStationForm();
                        sf.setStationId(station.getId());
                        sf.setStationName(station.getStationName());
                        sf.setStationDetail(station.getStationDetail());
                        r.getStationList().add(sf);
                    }
                }

                if (null != req.getMenuId()) {
                    r.setIsBindingMenu(0);
                    o.getMenuList().forEach(m -> {
                        if (null != req.getMenuId() && m.getId().toString().equals(req.getMenuId().toString())) {
                            r.setIsBindingMenu(1);
                        }
                    });
                }
                if (null != req.getPermissionId()) {
                    r.setIsBindingPermission(0);
                    o.getPermissionList().forEach(p -> {
                        if (null != req.getPermissionId() && p.getId().toString().equals(req.getPermissionId().toString())) {
                            r.setIsBindingPermission(1);
                        }
                    });
                }
                rfList.add(r);
            });
            res.setPageSize(pageSize);
            res.setCurrentPage((req.getRequestPage() < total ? req.getRequestPage() : total));
            res.setTotal(total);
            res.setTotalResult(totalResult);
        }
        res.setTotalResult(page.getTotalCount());
        res.setPageSize(page.getPageSize());
        res.setCurrentPage(page.getCurrentPage());
        res.setTotal(page.getTotalPage());
        res.setRoles(rfList);
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        return res;
    }

    @Override
    @Audit(operate = "删除管理员角色")
    public DeleteRoleRes deleteRole(HealthRoleReq req) {
        DeleteRoleRes res = new DeleteRoleRes();
        UserEntity loginUser = getCurrentUser();
        if (null == loginUser) {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
            return res;
        }
        if (StringUtils.isEmpty(req.getRoleIds())) {
            res.setRet(ResponseContext.RES_NULL_ERROR_INFO);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "roleIds不能为空");
            return res;
        }
        String hql = "from HealthRoleEntity where id in (" + req.getRoleIds() + ")";
        List<HealthRoleEntity> roleList = healthRoleDao.getListByHql(hql);
        for (HealthRoleEntity role : roleList) {
            if (null != role && !role.isRoot() && !role.isBuiltIn()) {
                if (role.getSubRoleList().size() > 0) {
                    res.setRet(ResponseContext.RES_SUBDATA_NOTNULL_CODE);
                    res.setRetInfo(ResponseContext.RES_SUBDATA_NOTNULL_INFO + "roleId:" + role.getId() + ",["
                            + role.getDisplayName() + "]");
                    return res;
                }
            } else if (null == role) {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
                return res;
            } else {
                res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
                res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
                return res;
            }
        }
        for (HealthRoleEntity role : roleList) {
            role.setParentRole(null);
            role.getPermissionList().clear();
            role.getMenuList().clear();
            healthRoleDao.delete(role);
        }
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        return res;
    }

    @Override
    @Audit(operate = "修改管理员角色")
    public ModifyRoleRes modifyRole(HealthRoleReq req) {
        ModifyRoleRes res = new ModifyRoleRes();
        UserEntity loginUser = getCurrentUser();
        if (null == loginUser) {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
            return res;
        }
        if (null == req.getRoleId()) {
            res.setRet(ResponseContext.RES_NULL_ERROR_INFO);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
            return res;
        } else {
            if (null != req.getParentRoleId()) {
                List<HealthRoleEntity> curRoleList = new ArrayList<HealthRoleEntity>();
                List<Integer> ids = getAllSubRoles(curRoleList);
                if (!ids.contains(req.getParentRoleId())) {
                    res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                    res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "只能关联本用户角色/子孙角色！");
                    return res;
                }
            }
        }
        String hql = "from HealthRoleEntity where id = " + req.getRoleId();
        HealthRoleEntity role = healthRoleDao.getUniqueByHql(hql);
        //
        if (null != role && !role.isRoot() && !role.isBuiltIn()) {
            if (null != req.getParentRoleId()) {
                HealthRoleEntity parentRole = healthRoleDao.get(req.getParentRoleId());
                if (null == parentRole) {
                    res.setRet(ResponseContext.RES_NULL_ERROR_INFO);
                    res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "无此父角色");
                    return res;
                }
                role.setParentRole(parentRole);
            }

            role.getStationList().clear();
            if (StringUtils.isNotEmpty(req.getStationIds())) {
                for (String stationId : req.getStationIds().split(",")) {
                    ServiceStationEntity station = serviceStationDao.get(Integer.valueOf(stationId));
                    if (null == station) {
                        res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                        res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "无此站点！");
                        return res;
                    }
                    role.getStationList().add(station);
                }
            }


            if (StringUtils.isNotEmpty(req.getPermissionIdList())) {
                if (null != role.getParentRole()) {
                    List<PermissionEntity> parentRolePermissions = role.getParentRole().getPermissionList();
                    String parentRolePermissionsStr = "";
                    if (!parentRolePermissions.isEmpty()) {
                        // 限制权限不能超出父角色
                        for (PermissionEntity p : parentRolePermissions) {
                            parentRolePermissionsStr = parentRolePermissionsStr + p.getId() + ",";
                        }
                        parentRolePermissionsStr = parentRolePermissionsStr.substring(0, parentRolePermissionsStr.length() - 1);
                    }
                    List<PermissionEntity> permissionList = permissionDao.getListByHql("from PermissionEntity where id in ("
                            + req.getPermissionIdList() + ") and id in (" + parentRolePermissionsStr + ")");
                    role.setPermissionList(permissionList);
                } else {
                    List<PermissionEntity> permissionList = permissionDao.getListByHql("from PermissionEntity where id in ("
                            + req.getPermissionIdList() + ")");
                    role.setPermissionList(permissionList);
                }
            }
            role.setRoleName(StringUtils.isNotEmpty(req.getRoleName()) ? req.getRoleName() : role.getRoleName());
            role.setDisplayName(
                    StringUtils.isNotEmpty(req.getDisplayName()) ? req.getDisplayName() : role.getDisplayName());
            role.setRoleState(req.getRoleState() != null ? req.getRoleState() : role.getRoleState());
            healthRoleDao.update(role);
        } else {
            res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
            return res;
        }
        res.setRoleId(role.getId());
        res.setRoleName(role.getRoleName());
        res.setBuiltIn(role.isBuiltIn());
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        return res;
    }

    /**
     * 获取入参列表的角色id和所有子、孙角色id
     */
    public List<Integer> getAllSubRoles(Collection<HealthRoleEntity> curRoles) {
        List<Integer> roleIds = new ArrayList<Integer>();
        if (curRoles.size() > 0) {
            curRoles.forEach(o -> {
                roleIds.add(o.getId());
                roleIds.addAll(getAllSubRoles(o.getSubRoleList()));
            });
        }
        return roleIds;
    }
//
//	private void reloadRolePermission() {
//		RealmSecurityManager rsm = (RealmSecurityManager) SecurityUtils.getSecurityManager();
//		rsm.getRealms().iterator().next();
//		Subject subject = SecurityUtils.getSubject();
//		String realmName = subject.getPrincipals().getRealmNames().iterator().next();
//	}
}
