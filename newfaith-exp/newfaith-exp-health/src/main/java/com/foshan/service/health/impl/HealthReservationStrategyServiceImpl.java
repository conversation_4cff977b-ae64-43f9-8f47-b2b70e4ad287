package com.foshan.service.health.impl;

import com.foshan.dao.generic.Page;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.health.HealthReservationPresetPeriodEntity;
import com.foshan.entity.health.HealthReservationStrategyEntity;
import com.foshan.form.health.HealthReservationPresetPeriodForm;
import com.foshan.form.health.HealthReservationStrategyForm;
import com.foshan.form.health.request.HealthReservationStrategyReq;
import com.foshan.form.health.response.healthReservationStrategy.GetHealthReservationStrategyInfo;
import com.foshan.form.health.response.healthReservationStrategy.GetHealthReservationStrategyListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.health.IHealthReservationStrategyService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import static java.util.Comparator.comparingInt;

@Transactional
@Service("healthReservationStrategyService")
public class HealthReservationStrategyServiceImpl extends GenericHealthService implements IHealthReservationStrategyService {

    private final static Logger logger = LoggerFactory.getLogger(HealthReservationStrategyServiceImpl.class);

    @Override
    @Audit(operate = "新增策略")
    public IResponse addHealthReservationStrategy(HealthReservationStrategyReq req) {
        GenericResponse res = new GenericResponse();
        if (null != req.getPeriodType() && StringUtils.isNotEmpty(req.getStrategyName())) {

            HealthReservationStrategyEntity strategy = new HealthReservationStrategyEntity();

            strategy.setStrategyName(req.getStrategyName());
            strategy.setPeriodType(req.getPeriodType());
            strategy.setSkipDate(StringUtils.isNotEmpty(req.getSkipDate())? req.getSkipDate():"");
            strategy.setSkipDay(StringUtils.isNotEmpty(req.getSkipDay())? req.getSkipDay():"");
            strategy.setAutoExtend(null != req.getAutoExtend() ? req.getAutoExtend() : 0);
            strategy.setLagDay(null != req.getLagDay() ? req.getLagDay() : 0);
            strategy.setState(EntityContext.RECORD_STATE_VALID);
            if (StringUtils.isNotEmpty(req.getPresetPeriodList())) {
                String[] presetPeriodIds = req.getPresetPeriodList().split(",");
                for (String presetPeriodId : presetPeriodIds) {
                    HealthReservationPresetPeriodEntity presetPeriod = healthReservationPresetPeriodDao.get(Integer.parseInt(presetPeriodId));
                    if (null != presetPeriod) {
                        strategy.getPresetPeriodList().add(presetPeriod);
                    } else {
                        res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                        res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + " " + presetPeriodId + " 不存在此时间段");
                        return res;
                    }
                }
            }
            strategy.getPresetPeriodList();

            healthReservationStrategyDao.save(strategy);
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }

        return res;
    }

    @Override
    public IResponse getHealthReservationStrategyList(HealthReservationStrategyReq req) {
        GetHealthReservationStrategyListRes res = new GetHealthReservationStrategyListRes();
        Page<HealthReservationStrategyEntity> page = new Page<HealthReservationStrategyEntity>();
        page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
        page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
        page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
        StringBuilder hql = new StringBuilder("select distinct a from HealthReservationStrategyEntity a  ");

        hql.append(" where 1=1").append(StringUtils.isNotEmpty(req.getStateList()) ? " and  a.state in(" + req.getStateList()+")"  :
                " and a.state="+EntityContext.RECORD_STATE_VALID)
                .append(StringUtils.isNotEmpty(req.getStrategyName()) ? " and  a.strategyName like '" + req.getStrategyName() + "'" :
                        "")
                .append(null != req.getPeriodType() ? " and a.periodType = " + req.getPeriodType() : "")
                .append(StringUtils.isNotEmpty(req.getSkipDay()) ? " and a.skipDay like'" + req.getSkipDay() + "'" : "")
                .append(null != req.getAutoExtend() ? " and a.autoExtend=" + req.getAutoExtend() : "");
        hql.append(" ORDER BY a.createTime DESC");
        page = healthReservationStrategyDao.queryPage(page, hql.toString());

        res.setTotalResult(page.getTotalCount());
        res.setPageSize(page.getPageSize());
        res.setCurrentPage(page.getCurrentPage());
        res.setTotal(page.getTotalPage());
        page.getResultList().forEach(o -> {
            HealthReservationStrategyForm reservationStrategyForm = new HealthReservationStrategyForm(o.getId(),
                    o.getStrategyName(), o.getPeriodType(), o.getSkipDay(), o.getAutoExtend(),o.getLagDay(),o.getSkipDate());
            o.getPresetPeriodList().forEach(p -> {

                HealthReservationPresetPeriodForm healthReservationPresetPeriodForm = new HealthReservationPresetPeriodForm(p.getId(), p.getMaxNum(),
                        p.getOrders(), p.getStartTime(), p.getEndTime());
                reservationStrategyForm.getPresetPeriodFormList().add(healthReservationPresetPeriodForm);

            });
            reservationStrategyForm.getPresetPeriodFormList().sort(comparingInt(HealthReservationPresetPeriodForm::getOrders));
            res.getReservationStrategyFormList().add(reservationStrategyForm);
        });
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        return res;
    }


    @Override
    @Audit(operate = "修改策略")
    public IResponse modifyHealthReservationStrategy(HealthReservationStrategyReq req) {
        GenericResponse res = new GenericResponse();
        if (null != req.getStrategyId()) {
            HealthReservationStrategyEntity reservationStrategy = healthReservationStrategyDao.get(req.getStrategyId());
            if (null != reservationStrategy) {
                reservationStrategy.setStrategyName(StringUtils.isNotEmpty(req.getStrategyName()) ? req.getStrategyName() :
                        reservationStrategy.getStrategyName());
                reservationStrategy.setPeriodType(null != req.getPeriodType() ? req.getPeriodType() : reservationStrategy.getPeriodType());
                reservationStrategy.setSkipDay(req.getSkipDay());
                reservationStrategy.setSkipDate(req.getSkipDate());
                reservationStrategy.setLagDay(null != req.getLagDay() ? req.getLagDay():reservationStrategy.getLagDay());
                reservationStrategy.setAutoExtend(null != req.getAutoExtend() ? req.getAutoExtend() : reservationStrategy.getAutoExtend());

                if (StringUtils.isNotEmpty(req.getPresetPeriodList())) {
                    String[] presetPeriodIds = req.getPresetPeriodList().split(",");
                    reservationStrategy.getPresetPeriodList().clear();
                    for (String presetPeriodId : presetPeriodIds) {
                        HealthReservationPresetPeriodEntity presetPeriod = healthReservationPresetPeriodDao.get(Integer.parseInt(presetPeriodId));
                        reservationStrategy.getPresetPeriodList().add(presetPeriod);
                    }
                }

                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }

        return res;
    }

    @Override
    @Audit(operate = "删除策略")
    public IResponse deleteHealthReservationStrategy(HealthReservationStrategyReq req) {
        GenericResponse res = new GenericResponse();
        if (null != req.getStrategyId()) {
            HealthReservationStrategyEntity reservationStrategy = healthReservationStrategyDao.get(req.getStrategyId());
            if (null != reservationStrategy) {
                reservationStrategy.setState(EntityContext.RECORD_STATE_INVALID);
                reservationStrategy.getPresetPeriodList().clear();
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }

        return res;
    }

    @Override
    public IResponse getHealthReservationStrategyInfo(HealthReservationStrategyReq req) {
        GetHealthReservationStrategyInfo res = new GetHealthReservationStrategyInfo();

        if (null != req.getStrategyId()) {
            HealthReservationStrategyEntity strategy = healthReservationStrategyDao.get(req.getStrategyId());


            HealthReservationStrategyForm reservationStrategyForm = new HealthReservationStrategyForm(strategy.getId(),
                    strategy.getStrategyName(), strategy.getPeriodType(), strategy.getSkipDay(), strategy.getAutoExtend(),strategy.getLagDay(),
                    strategy.getSkipDate());
            strategy.getPresetPeriodList().forEach(p -> {

                HealthReservationPresetPeriodForm healthReservationPresetPeriodForm = new HealthReservationPresetPeriodForm(p.getId(),
                        p.getMaxNum(),
                        p.getOrders(), p.getStartTime(), p.getEndTime());
                reservationStrategyForm.getPresetPeriodFormList().add(healthReservationPresetPeriodForm);

            });

            res.setStrategyForm(reservationStrategyForm);
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }


}
