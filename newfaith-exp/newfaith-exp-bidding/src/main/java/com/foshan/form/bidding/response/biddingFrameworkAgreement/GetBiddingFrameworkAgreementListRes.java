package com.foshan.form.bidding.response.biddingFrameworkAgreement;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.bidding.BiddingFrameworkAgreementForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="框架协议列表响应(GetBiddingFrameworkAgreementListRes)")
@JsonInclude(Include.NON_NULL)
public class GetBiddingFrameworkAgreementListRes extends BasePageResponse {/**
	 * 
	 */
	private static final long serialVersionUID = -5330839769788219213L;
	
	@ApiModelProperty(value = "框架协议列表")
	private List<BiddingFrameworkAgreementForm> frameworkAgreementList = new ArrayList<BiddingFrameworkAgreementForm>();

}
