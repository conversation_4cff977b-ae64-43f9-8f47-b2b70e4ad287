package com.foshan.form.bidding.request;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "框架协议请求对象(BiddingFrameworkAgreementReq)")
public class BiddingFrameworkAgreementReq extends BasePageRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = 421626045999228693L;
	
	@ApiModelProperty(value = "框架协议文件id")
	private Integer frameworkAgreementId;
	@ApiModelProperty(value = "框架协议文件id列表")
	private String frameworkAgreementIds;
	@ApiModelProperty(value = "框架协议文件名称")
	private String frameworkAgreementName;
	@ApiModelProperty(value = "合作伙伴id")
	private String partnerId;

}
