package com.foshan.service.bidding.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

import javax.transaction.Transactional;

import com.foshan.dao.bidding.impl.BiddingPartnerPerformanceDaoImpl;
import com.foshan.entity.DictionaryDataEntity;
import com.foshan.entity.bidding.*;
import com.foshan.form.DictionaryDataForm;
import com.foshan.form.bidding.BiddingPartnerAttachmentForm;
import com.foshan.form.bidding.BiddingPartnerContractForm;
import com.foshan.form.bidding.BiddingPartnerPerformanceForm;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.foshan.dao.generic.Page;
import com.foshan.form.bidding.BiddingPartnerForm;
import com.foshan.form.bidding.request.BiddingPartnerReq;
import com.foshan.form.bidding.response.biddingPartner.AddBiddingPartnerRes;
import com.foshan.form.bidding.response.biddingPartner.DeleteBiddingPartnerRes;
import com.foshan.form.bidding.response.biddingPartner.GetBiddingPartnerListRes;
import com.foshan.form.bidding.response.biddingPartner.ModifyBiddingPartnerRes;
import com.foshan.form.bidding.response.biddingPartner.GetBiddingPartnerInfoRes;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.bidding.IBiddingPartnerService;
import com.foshan.util.DateUtil;

@Transactional
@Service("biddingPartnerService")
public class BiddingPartnerServiceImpl extends GenericBiddingService implements IBiddingPartnerService{

	private final static Logger logger = LoggerFactory.getLogger(BiddingPartnerContractServiceImpl.class);
	private final BiddingPartnerPerformanceDaoImpl biddingPartnerPerformanceDao;

	public BiddingPartnerServiceImpl(BiddingPartnerPerformanceDaoImpl biddingPartnerPerformanceDao) {
		super();
		this.biddingPartnerPerformanceDao = biddingPartnerPerformanceDao;
	}

	public GetBiddingPartnerListRes getBiddingPartnerList(BiddingPartnerReq req) {
		GetBiddingPartnerListRes res = new GetBiddingPartnerListRes();
		List<BiddingPartnerForm> pfList = new ArrayList<BiddingPartnerForm>();
		Page<BiddingPartnerEntity> page = new Page<>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 0) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		
		StringBuilder hql = new StringBuilder();
		
		// 根据条件决定是否从 partnerContract 检索
		if ((StringUtils.isNotEmpty(req.getName()) || 
			 StringUtils.isNotEmpty(req.getCategory()) || 
			 StringUtils.isNotEmpty(req.getSubCategory()))) {
			
			hql.append("select distinct a from BiddingPartnerEntity a join a.partnerContractList pc ");
			
			// 处理 qualification 和 performance 的条件
			if(null != req.getQualificationName() && StringUtils.isNotEmpty(req.getQualificationName())) {
				hql.append("JOIN a.partnerQualificationList pq where pq.name like '%" + req.getQualificationName() + "%' ");
			}
			else if(null != req.getPerformanceValue() && StringUtils.isNotEmpty(req.getPerformanceValue())) {
				String[] performanceKeywords = req.getPerformanceValue().split(",");
				hql.append("JOIN a.partnerPerformanceList pp where (");
				for (int i = 0; i < performanceKeywords.length; i++) {
					if (i > 0) {
						hql.append(" or ");
					}
					hql.append("pp.performanceType like '%" + performanceKeywords[i].trim() + "%' or pp.performanceValue like '%" + performanceKeywords[i].trim() + "%'");
				}
				hql.append(") ");
			}
			else {
				hql.append("where 1=1 ");
			}
			
			// 添加 partnerContract 相关的查询条件
			if(StringUtils.isNotEmpty(req.getName())) {
				hql.append("and pc.name like '%" + req.getName() + "%' ");
			}
			if(StringUtils.isNotEmpty(req.getCategory())) {
				String[] categoryIds = req.getCategory().split(",");
				hql.append("and exists (select 1 from pc.categoryList cat where cat.id in (");
				for (int i = 0; i < categoryIds.length; i++) {
					if (i > 0) {
						hql.append(",");
					}
					hql.append(categoryIds[i].trim());
				}
				hql.append(")) ");
			}
			if(StringUtils.isNotEmpty(req.getSubCategory())) {
				String[] subCategoryKeywords = req.getSubCategory().split("，");
				hql.append("and (");
				for (int i = 0; i < subCategoryKeywords.length; i++) {
					if (i > 0) {
						hql.append(" or ");
					}
					hql.append("pc.subCategory like '%" + subCategoryKeywords[i].trim() + "%'");
				}
				hql.append(") ");
			}
		} else {
			// 原有的查询逻辑
			hql.append("select distinct a from BiddingPartnerEntity a ");
			if(null != req.getQualificationName() && StringUtils.isNotEmpty(req.getQualificationName())) {
				hql.append("JOIN a.partnerQualificationList pq where pq.name like '%" + req.getQualificationName() + "%' ");
			}
			else if(null != req.getPerformanceValue() && StringUtils.isNotEmpty(req.getPerformanceValue())) {
				String[] performanceKeywords = req.getPerformanceValue().split(",");
				hql.append("JOIN a.partnerPerformanceList pp where (");
				for (int i = 0; i < performanceKeywords.length; i++) {
					if (i > 0) {
						hql.append(" or ");
					}
					hql.append("pp.performanceType like '%" + performanceKeywords[i].trim() + "%' or pp.performanceValue like '%" + performanceKeywords[i].trim() + "%'");
				}
				hql.append(") ");
			}
			else {
				hql.append("where 1=1 ");
			}
		}
		
		// 公司名称条件始终使用 partner 的字段
		if(StringUtils.isNotEmpty(req.getCompany())) {
			hql.append("and a.company like '%" + req.getCompany() + "%' ");
		}
		
		hql.append("ORDER BY a.id ASC");
		
		// 执行查询
		page = biddingPartnerDao.queryPage(page, hql.toString());
		
		// 设置分页信息
		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		
		// 处理结果
		page.getResultList().forEach(o -> {
			BiddingPartnerForm pf = new BiddingPartnerForm();
			pf.setId(o.getId());
			pf.setCompany(o.getCompany());
			
			// 获取合同列表
			List<BiddingPartnerContractForm> partnerContractList = new ArrayList<>();
			String sql = "select * from t_bidding_partner_contract where partnerId = " + o.getId();
			List<BiddingPartnerContractEntity> pcList = biddingPartnerContractDao.getListBySql(sql);
			pcList.forEach(o2 -> {
				BiddingPartnerContractForm partnerContract = new BiddingPartnerContractForm();
				partnerContract.setId(o2.getId());
				partnerContract.setName(o2.getName());
				partnerContract.setEmail(o2.getEmail());
				partnerContract.setPhone(o2.getPhone());
				partnerContract.setCompany(o.getCompany());
				partnerContract.setEndDate(null != o2.getEndDate() ? DateUtil.formatShortFormat(o2.getEndDate()) : "");
				partnerContract.setSubCategory(o2.getSubCategory());
				
				// 添加 category 列表
				List<DictionaryDataForm> categoryList = new ArrayList<>();
				o2.getCategoryList().forEach(item -> {
					DictionaryDataForm df = new DictionaryDataForm();
					df.setDictionaryDataId(item.getId());
					df.setDataKey(item.getDataKey());
					df.setDataName(item.getDataName());
					categoryList.add(df);
				});
				partnerContract.setCategoryList(categoryList);
				
				partnerContractList.add(partnerContract);
			});
			pf.setPartnerContractList(partnerContractList);
			// 获取业绩列表
			List<BiddingPartnerPerformanceForm> partnerPerformanceList = new ArrayList<>();
			sql = "select * from t_bidding_partner_performance where partnerId = " + o.getId();
			List<BiddingPartnerPerformanceEntity> ppfList = biddingPartnerPerformanceDao.getListBySql(sql);
			ppfList.forEach(o2 -> {
				BiddingPartnerPerformanceForm partnerPerformance = new BiddingPartnerPerformanceForm();
				partnerPerformance.setPartnerPerformanceId(o2.getId());
				partnerPerformance.setPerformanceType(o2.getPerformanceType());
				partnerPerformance.setPerformanceValue(o2.getPerformanceValue());
				partnerPerformanceList.add(partnerPerformance);
			});
			pf.setPartnerPerformanceList(partnerPerformanceList);
			pfList.add(pf);
		});
		
		res.setPartnerList(pfList);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	public DeleteBiddingPartnerRes deleteBiddingPartner(BiddingPartnerReq req) {
		DeleteBiddingPartnerRes res = new DeleteBiddingPartnerRes();
		if(null == req.getPartnerId()) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}
		String hql = "from BiddingPartnerEntity where id = " + req.getPartnerId();
		BiddingPartnerEntity pne = biddingPartnerDao.getUniqueByHql(hql);
		if(null != pne){
			biddingPartnerDao.deleteById(req.getPartnerId());
			String sql = "select * from t_bidding_partner_performance where partnerId = " + req.getPartnerId();
			List<BiddingPartnerPerformanceEntity> ppList = biddingPartnerPerformanceDao.getListBySql(sql);
			ppList.forEach(o->{
				biddingPartnerPerformanceDao.deleteById(o.getId());
			});
			String sql2 = "select * from t_bidding_partner_qualification where partnerId = " + req.getPartnerId();
			List<BiddingPartnerQualificationEntity> pqList = biddingPartnerQualificationDao.getListBySql(sql2);
			pqList.forEach(o->{
				biddingPartnerPerformanceDao.deleteById(o.getId());
			});
		}else{
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
			return res;
		}
		
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;	
	}
	public AddBiddingPartnerRes addBiddingPartner(BiddingPartnerReq req) {
		AddBiddingPartnerRes res = new AddBiddingPartnerRes();
		if(StringUtils.isEmpty(req.getCompany())){
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "公司不能为空！");
			return res;
		}
		BiddingPartnerEntity p = biddingPartnerDao.getUniqueBySql("select * from t_bidding_partner where company = '" + req.getCompany() + "'");
		if(null != p){
			res.setRet(ResponseContext.RES_DATA_DUPLICATE_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_DUPLICATE_INFO + "已有该合作伙伴！");
			return res;
		}
		
		BiddingPartnerEntity pne = new BiddingPartnerEntity();
//		pne.setName(req.getName());
//		pne.setCategory(req.getCategory());
//		pne.setPhone(req.getPhone());
		pne.setCompany(req.getCompany());
//		pne.setEmail(req.getEmail());
//		pne.setEndDate(req.getEndDate());
		pne.setCompanyIntro(req.getCompanyIntro());
		pne.setRegisteredCapital(req.getRegisteredCapital());
		pne.setRealCapital(req.getRealCapital());
		pne.setAddress(req.getAddress());
		pne.setEmployeesNum(req.getEmployeesNum());
		pne.setIct(req.getIct());
		pne.setSafeCity(req.getSafeCity());
		pne.setCommunicationEngineering(req.getCommunicationEngineering());
		pne.setIdc(req.getIdc());
		pne.setEmergencyBroadcast(req.getEmergencyBroadcast());
//		pne.setSubCategory(req.getSubCategory());
		biddingPartnerDao.save(pne);
		String hql = "from BiddingPartnerEntity where company = '" + req.getCompany() + "'";
		BiddingPartnerEntity p2 = biddingPartnerDao.getUniqueByHql(hql);
		List<BiddingPartnerPerformanceEntity> ppList = p2.getPartnerPerformanceList();
		req.getPartnerPerformanceList().forEach(item -> {
			BiddingPartnerPerformanceEntity ppe = new BiddingPartnerPerformanceEntity();
			ppe.setPartner(p2);
			ppe.setPerformanceType(item.getPerformanceType());
			ppe.setPerformanceValue(item.getPerformanceValue());
			ppe.setIct(item.getIct());
			ppe.setSafeCity(item.getSafeCity());
			ppe.setCommunicationEngineering(item.getCommunicationEngineering());
			ppe.setIdc(item.getIdc());
			ppe.setEmergencyBroadcast(item.getEmergencyBroadcast());
			biddingPartnerPerformanceDao.save(ppe);
			ppList.add(ppe);
		});
		List<BiddingPartnerContractEntity> pcList = p2.getPartnerContractList();
		req.getPartnerContractList().forEach(item -> {
			BiddingPartnerContractEntity pce = new BiddingPartnerContractEntity();
			pce.setPartner(p2);
			pce.setName(item.getName());
			pce.setEmail(item.getEmail());
			pce.setPhone(item.getPhone());
			pce.setCompany(req.getCompany());
			try {
				pce.setEndDate(StringUtils.isNotEmpty(item.getEndDate()) ? 
					DateUtil.parseShortFormat(item.getEndDate()) : null);
			} catch (ParseException e) {
				logger.error("Error parsing endDate: " + item.getEndDate(), e);
			}
            pce.setSubCategory(item.getSubCategory());
			
			List<DictionaryDataEntity> categoryList = new ArrayList<>();
			if (StringUtils.isNotEmpty(item.getCategoryIds())) {
				String[] categoryIds = item.getCategoryIds().split(",");
				for (String categoryId : categoryIds) {
					try {
						String hql2 = "from DictionaryDataEntity where id = " + categoryId.trim();
						DictionaryDataEntity dde = dictionaryDataDao.getUniqueByHql(hql2);
						if (dde != null) {
							categoryList.add(dde);
						}
					} catch (Exception e) {
						logger.error("Error processing categoryId: " + categoryId, e);
					}
				}
			}
			pce.setCategoryList(categoryList);
			biddingPartnerContractDao.save(pce);
			pcList.add(pce);
		});
		if(StringUtils.isNotEmpty(req.getAttachmentIdList())){
			String[] attachmentIds = req.getAttachmentIdList().split(",");
			for(String attachmentId : attachmentIds){
				hql = "from BiddingPartnerAttachmentEntity where id = '" + attachmentId + "'";
				BiddingPartnerAttachmentEntity pae = biddingPartnerAttachmentDao.getUniqueByHql(hql);
				pae.setPartner(p2);
				biddingPartnerAttachmentDao.update(pae);
			}
		}
		biddingPartnerDao.update(p2);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	public ModifyBiddingPartnerRes modifyBiddingPartner(BiddingPartnerReq req) {
		ModifyBiddingPartnerRes res = new ModifyBiddingPartnerRes();
		if(null == req.getPartnerId()) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}
		BiddingPartnerEntity p = biddingPartnerDao.getUniqueBySql(
				"select * from t_bidding_partner where id!= " + req.getPartnerId()
				+ " and company = '" + req.getCompany() + "'");
		if(null != p) {
			res.setRet(ResponseContext.RES_DATA_DUPLICATE_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_DUPLICATE_INFO + "已有该合作伙伴！");
			return res;
		}
		
		String hql = "from BiddingPartnerEntity where id = " + req.getPartnerId();
		BiddingPartnerEntity pne = biddingPartnerDao.getUniqueByHql(hql);
		if(null != pne) {
			pne.setCompany(req.getCompany());
			pne.setCompanyIntro(req.getCompanyIntro());
			pne.setRegisteredCapital(req.getRegisteredCapital());
			pne.setRealCapital(req.getRealCapital());
			pne.setAddress(req.getAddress());
			pne.setEmployeesNum(req.getEmployeesNum());
			pne.setIct(req.getIct());
			pne.setSafeCity(req.getSafeCity());
			pne.setCommunicationEngineering(req.getCommunicationEngineering());
			pne.setIdc(req.getIdc());
			pne.setEmergencyBroadcast(req.getEmergencyBroadcast());

			// 处理 performance 列表
			String sql = "select * from t_bidding_partner_performance where partnerId = " + req.getPartnerId();
			List<BiddingPartnerPerformanceEntity> ppList = biddingPartnerPerformanceDao.getListBySql(sql);
			ppList.forEach(o -> {
				biddingPartnerPerformanceDao.deleteById(o.getId());
			});
			List<BiddingPartnerPerformanceEntity> ppList2 = pne.getPartnerPerformanceList();
			req.getPartnerPerformanceList().forEach(item -> {
				BiddingPartnerPerformanceEntity ppe = new BiddingPartnerPerformanceEntity();
				ppe.setPartner(pne);
				ppe.setPerformanceType(item.getPerformanceType());
				ppe.setPerformanceValue(item.getPerformanceValue());
				ppe.setIct(item.getIct());
				ppe.setSafeCity(item.getSafeCity());
				ppe.setCommunicationEngineering(item.getCommunicationEngineering());
				ppe.setIdc(item.getIdc());
				ppe.setEmergencyBroadcast(item.getEmergencyBroadcast());
				ppList2.add(ppe);
				biddingPartnerPerformanceDao.save(ppe);
			});
			pne.setPartnerPerformanceList(ppList2);

			// 处理 contract 列表
			sql = "select * from t_bidding_partner_contract where partnerId = " + req.getPartnerId();
			List<BiddingPartnerContractEntity> pcList = biddingPartnerContractDao.getListBySql(sql);
			pcList.forEach(o -> {
				biddingPartnerContractDao.deleteById(o.getId());
			});
			List<BiddingPartnerContractEntity> pcList2 = pne.getPartnerContractList();
			req.getPartnerContractList().forEach(item -> {
				BiddingPartnerContractEntity pce = new BiddingPartnerContractEntity();
				pce.setPartner(pne);
				pce.setName(item.getName());
				pce.setEmail(item.getEmail());
				pce.setPhone(item.getPhone());
				pce.setCompany(req.getCompany());
				try {
					pce.setEndDate(StringUtils.isNotEmpty(item.getEndDate()) ? 
						DateUtil.parseShortFormat(item.getEndDate()) : null);
				} catch (ParseException e) {
					logger.error("Error parsing endDate: " + item.getEndDate(), e);
				}
				pce.setSubCategory(item.getSubCategory());

				// 处理 category 列表
				List<DictionaryDataEntity> categoryList = new ArrayList<>();
				if (StringUtils.isNotEmpty(item.getCategoryIds())) {
					String[] categoryIds = item.getCategoryIds().split(",");
					for (String categoryId : categoryIds) {
						try {
							String hql2 = "from DictionaryDataEntity where id = " + categoryId.trim();
							DictionaryDataEntity dde = dictionaryDataDao.getUniqueByHql(hql2);
							if (dde != null) {
								categoryList.add(dde);
							}
						} catch (Exception e) {
							logger.error("Error processing categoryId: " + categoryId, e);
						}
					}
				}
				pce.setCategoryList(categoryList);
				pcList2.add(pce);
				biddingPartnerContractDao.save(pce);
			});
			pne.setPartnerContractList(pcList2);
			
			biddingPartnerDao.update(pne);
		} else {
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO);
			return res;
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	public GetBiddingPartnerInfoRes getBiddingPartnerInfo(BiddingPartnerReq req) {
		GetBiddingPartnerInfoRes res = new GetBiddingPartnerInfoRes();
		if(null == req.getPartnerId()) {
			res.setRet(ResponseContext.RES_NULL_ERROR_INFO);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}
		
		String hql = "from BiddingPartnerEntity where id = " + req.getPartnerId();
		BiddingPartnerEntity partner = biddingPartnerDao.getUniqueByHql(hql);
		if(null != partner) {
			Integer partnerId = partner.getId();
			res.setPartnerId(partnerId);
			res.setCompany(partner.getCompany());
			res.setCompanyIntro(partner.getCompanyIntro());
			res.setRegisteredCapital(partner.getRegisteredCapital());
			res.setRealCapital(partner.getRealCapital());
			res.setAddress(partner.getAddress());
			res.setEmployeesNum(partner.getEmployeesNum());
			res.setIct(partner.getIct());
			res.setSafeCity(partner.getSafeCity());
			res.setCommunicationEngineering(partner.getCommunicationEngineering());
			res.setIdc(partner.getIdc());
			res.setEmergencyBroadcast(partner.getEmergencyBroadcast());

			// 处理 performance 列表
			List<BiddingPartnerPerformanceForm> partnerPerformanceList = new ArrayList<>();
			String sql = "select * from t_bidding_partner_performance where partnerId = " + partnerId;
			List<BiddingPartnerPerformanceEntity> ppList = biddingPartnerPerformanceDao.getListBySql(sql);
			ppList.forEach(o -> {
				BiddingPartnerPerformanceForm partnerPerformance = new BiddingPartnerPerformanceForm();
				partnerPerformance.setPerformanceType(o.getPerformanceType());
				partnerPerformance.setPerformanceValue(o.getPerformanceValue());
				partnerPerformance.setIct(o.getIct());
				partnerPerformance.setSafeCity(o.getSafeCity());
				partnerPerformance.setCommunicationEngineering(o.getCommunicationEngineering());
				partnerPerformance.setIdc(o.getIdc());
				partnerPerformance.setEmergencyBroadcast(o.getEmergencyBroadcast());
				partnerPerformanceList.add(partnerPerformance);
			});
			res.setPartnerPerformanceList(partnerPerformanceList);

			// 处理 contract 列表
			List<BiddingPartnerContractForm> partnerContractList = new ArrayList<>();
			sql = "select * from t_bidding_partner_contract where partnerId = " + partnerId;
			List<BiddingPartnerContractEntity> pcList = biddingPartnerContractDao.getListBySql(sql);
			pcList.forEach(o2 -> {
				BiddingPartnerContractForm partnerContract = new BiddingPartnerContractForm();
				partnerContract.setId(o2.getId());
				partnerContract.setName(o2.getName());
				partnerContract.setEmail(o2.getEmail());
				partnerContract.setPhone(o2.getPhone());
				partnerContract.setCompany(partner.getCompany());
				partnerContract.setEndDate(null != o2.getEndDate() ? DateUtil.formatShortFormat(o2.getEndDate()) : "");
				partnerContract.setSubCategory(o2.getSubCategory());

				// 处理 category 列表
				List<DictionaryDataForm> categoryList = new ArrayList<>();
				o2.getCategoryList().forEach(item -> {
					DictionaryDataForm df = new DictionaryDataForm();
					df.setDictionaryDataId(item.getId());
					df.setDataKey(item.getDataKey());
					df.setDataName(item.getDataName());
					categoryList.add(df);
				});
				partnerContract.setCategoryList(categoryList);
				
				partnerContractList.add(partnerContract);
			});
			res.setPartnerContractList(partnerContractList);

			// 处理附件列表
			List<BiddingPartnerAttachmentForm> partnerAttachmentList = new ArrayList<>();
			hql = "select distinct a from BiddingPartnerAttachmentEntity a inner join a.partner b where b.id = " + partnerId;
			List<BiddingPartnerAttachmentEntity> paList = biddingPartnerAttachmentDao.getListByHql(hql);
			paList.forEach(o -> {
				BiddingPartnerAttachmentForm partnerAttachment = new BiddingPartnerAttachmentForm();
				partnerAttachment.setId(o.getId());
				partnerAttachment.setName(o.getAssetName());
				partnerAttachmentList.add(partnerAttachment);
			});
			res.setPartnerAttachmentList(partnerAttachmentList);
		} else {
			res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
			return res;
		}
		
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
}
