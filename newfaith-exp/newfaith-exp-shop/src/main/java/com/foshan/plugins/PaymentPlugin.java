package com.foshan.plugins;

import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.CompareToBuilder;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.servlet.ModelAndView;
import com.foshan.entity.PluginEntity;
import com.foshan.entity.shop.OrderRefundEntity;
//import com.foshan.entity.shop.PaymentSessionEntity;
import com.foshan.util.ContextInfo;
import com.foshan.util.ShopContextInfo;


/**
 * 
* @ClassName: PaymentPlugin 
* @Description: TODO(支付插件的父类) 
* <AUTHOR>
* @date 2019年3月12日 下午3:00:44 
*
 */

public abstract class PaymentPlugin implements Comparable<PaymentPlugin>,Serializable{

//	private static final long serialVersionUID = 4262987251641006603L;
//
//	private PluginEntity pluginConfig;
//	@Autowired
//	private ShopContextInfo shopContextInfo;
//
//   
//
//	/**
//	 * "支付方式名称"属性名称
//	 */
//	public static final String PAYMENT_NAME_ATTRIBUTE_NAME = "paymentName";
//
//	/**
//	 * "LOGO"属性名称
//	 */
//	public static final String LOGO_ATTRIBUTE_NAME = "logo";
//
//	/**
//	 * "手续费类型"属性名称
//	 */
//	public static final String CHARGE_TYPE_ATTRIBUTE_NAME = "chargeType";
//
//	/**
//	 * "描述"属性名称
//	 */
//	public static final String DESCRIPTION_ATTRIBUTE_NAME = "description";
//	/**
//	 * "手续费"属性名称
//	 */
//	public static final String CHARGE_ATTRIBUTE_NAME = "charge";
//	
//	/**
//	 * 默认支付视图名称
//	 */
//	public static final String DEFAULT_PAY_VIEW_NAME = "/shop/payment/pay";
//
//	/**
//	 * 默认支付结果视图名称
//	 */
//	public static final String DEFAULT_PAY_RESULT_VIEW_NAME = "/shop/payment/payResult";
//	
//	/**
//	 * 默认超时时间
//	 */
//	public static final Integer DEFAULT_TIMEOUT = 24 * 60 * 60;
//	
//
//	public PaymentPlugin(PluginEntity pluginConfig) {
//
//		this.pluginConfig = pluginConfig;
//	}
//	
//
//	public void setPluginConfig(PluginEntity pluginConfig) {
//		this.pluginConfig = pluginConfig;
//	}
//
//	/**
//	 * 手续费类型
//	 */
//	enum ChargeType {
//
//		/**
//		 * 按比例收费
//		 */
//		ratable,
//
//		/**
//		 * 固定收费
//		 */
//		fixed
//	}
//
//	/**
//	 * 获取插件的ID，即插件实现类的完整类名
//	 * 
//	 * @return ID
//	 */
//	public String getId() {
//		//return getClass().getAnnotation(Component.class).value();
//		return getClass().getName();
//	}
//
//	/**
//	 * 获取名称
//	 * 
//	 * @return 名称
//	 */
//	public String getName(){
//		return pluginConfig != null ? pluginConfig.getName() : null;
//	}
//
//	/**
//	 * 获取版本
//	 * 
//	 * @return 版本
//	 */
//	public String getVersion(){
//		return pluginConfig != null ? pluginConfig.getVersion() : null;
//	}
//
//	
//	/**
//	 * 插件的状态0未安装，1已经安装 ，2禁用
//	 * 
//	 * @return int状态
//	 */
//	public  int getStatus(){
//		return pluginConfig.getStatus();
//	}
//	
//
//	/**
//	 * 获取LOGO
//	 * 
//	 * @return LOGO
//	 */
//	public String getLogo() {
//		// PluginConfigEntity pluginConfig = getPluginConfig();
//		return pluginConfig != null ? pluginConfig.getLogo() : null;
//	}
//
//	/**
//	 * 获取描述
//	 * 
//	 * @return 描述
//	 */
//	public String getDescription() {
//		 //PluginConfigEntity pluginConfig = getPluginConfig();
//		return pluginConfig != null ?pluginConfig.getDesc() : null;
//	}
//	
//	/**
//	 * 获取是否已安装
//	 * 
//	 * @return 是否已安装
//	 */
//	public boolean getIsInstalled() {
//		//PluginConfigEntity pce = pluginConfig.getPluginConfigBySn(getId());
//		return pluginConfig.getStatus() > 0;
//	}
//
//	/**
//	 * 获取插件配置
//	 * 
//	 * @return 插件配置
//	 */
//	public PluginEntity getPluginConfig() {
//		return pluginConfig;
//	}
//
//	/**
//	 * 获取是否已启用
//	 * 
//	 * @return 是否已启用
//	 */
//	public boolean getIsEnabled() {
//		//PluginConfigEntity  pluginConfig = getPluginConfig();
//		return pluginConfig != null ? (pluginConfig.getStatus()==1) : false;
//	}
//
//	/**
//	 * 获取属性值
//	 * 
//	 * @param name
//	 *            属性名称
//	 * @return 属性值
//	 */
//	public String getAttribute(String name) {
//		if(pluginConfig!=null){
//			String n = pluginConfig.getAttributies().get(name);
//			return n;
//		}
//		return null;
//	}
//
//	/**
//	 * 获取排序
//	 * 
//	 * @return 排序
//	 */
//	public Integer getOrder() {
//		return 0;
//	}
//	
//
//	/**
//	 * 获取手续费类型
//	 * 
//	 * @return 手续费类型
//	 */
//	public PaymentPlugin.ChargeType getChargeType() {
//		PluginEntity pluginConfig = getPluginConfig();
//		 if(pluginConfig != null){
//			 String chargetype =  pluginConfig.getAttributies().get(CHARGE_TYPE_ATTRIBUTE_NAME);
//			 if(!StringUtils.isEmpty(chargetype)){
//				 return PaymentPlugin.ChargeType.valueOf(chargetype.toString());
//			 }
//		 }
//		
//		return  null;
//	}
//
//	/**
//	 * 获取手续费
//	 * 
//	 * @return 手续费
//	 */
//	public BigDecimal getFee() {
//		 PluginEntity pluginConfig = getPluginConfig();
//		 if(pluginConfig != null){
//			 String charge =  pluginConfig.getAttributies().get(CHARGE_ATTRIBUTE_NAME);
//			 if(!StringUtils.isEmpty(charge)){
//				 return new BigDecimal(charge.toString());
//			 }
//		 }
//		
//		return  null;
//	}
//
//
//	/**
//	 * 是否支持
//	 * 
//	 * @param request
//	 *            HttpServletRequest
//	 * @return 是否支持
//	 */
//	public boolean supports(HttpServletRequest request) {
//		return true;
//	}
//
//	/**
//	 * 支付前处理
//	 * 
//	 * @param paymentPlugin
//	 *            支付插件
//	 * @param PaymentSession
//	 *            支付事务
//	 * @param paymentDescription
//	 *            支付描述
//	 * @param params
//	 *            附加内容
//	 * @param request
//	 *            HttpServletRequest
//	 * @param response
//	 *            HttpServletResponse
//	 * @param modelAndView
//	 *            ModelAndView
//	 * @throws Exception
//	 */
//	public void beforePayHandle(PaymentPlugin paymentPlugin, PaymentSessionEntity paymentSession, String paymentDescription, String params, HttpServletRequest request, HttpServletResponse response, ModelAndView modelAndView) throws Exception {
//		modelAndView.setViewName("redirect:" + paymentPlugin.getPayUrl(paymentPlugin, paymentSession));
//	}
//
//	
//	/**
//	 * 支付后处理
//	 * 
//	 * @param paymentPlugin
//	 *            支付插件
//	 * @param isVerifyPass 
//	 * @param PaymentSession
//	 *            支付事务
//	 * @param paymentDescription
//	 *            支付描述
//	 * @param params
//	 *            附加内容
//	 * @param isPaySuccess
//	 *            是否支付成功
//	 * @param request
//	 *            HttpServletRequest
//	 * @param response
//	 *            HttpServletResponse
//	 * @param modelAndView
//	 *            ModelAndView
//	 * @throws Exception
//	 */
//	public void afterPayHandle(PaymentPlugin paymentPlugin, PaymentSessionEntity paymentSession, boolean isVerifyPass, Map<String, String> reqMap , String paymentDescription, String params, boolean isPaySuccess, HttpServletRequest request, HttpServletResponse response, ModelAndView modelAndView) throws Exception {
//		modelAndView.addObject("PaymentSession", paymentSession);
//		modelAndView.setViewName(DEFAULT_PAY_RESULT_VIEW_NAME);
//	}
//
//
//	/**
//	 * 获取支付前处理URL
//	 * 
//	 * @param paymentPlugin
//	 *            支付插件
//	 * @param PaymentSession
//	 *            支付事务
//	 * @return 支付前处理URL,绝对路径URL
//	 */
//	public String getBeforePayUrl(PaymentPlugin paymentPlugin, PaymentSessionEntity paymentSession) {
//		return getBeforePayUrl(paymentPlugin, paymentSession, null);
//	}
//
//	/**
//	 * 获取支付前处理URL
//	 * 
//	 * @param paymentPlugin
//	 *            支付插件
//	 * @param PaymentSession
//	 *            支付事务
//	 * @param params
//	 *            附加内容
//	 * @return 支付前处理URL，绝对路径URL
//	 */
//	public String getBeforePayUrl(PaymentPlugin paymentPlugin, PaymentSessionEntity paymentSession, String params) {
//		
//		return getBeforePayUrl(paymentPlugin,paymentSession,params,false);
//	}
//	
//	/**
//	 * 获取支付前处理URL
//	 * 
//	 * @param paymentPlugin
//	 *            支付插件
//	 * @param PaymentSession
//	 *            支付事务
//	 * @param params
//	 *            附加内容
//	 * @param relativePath
//	 *            true返回相对URL，false返回绝对URL
//	 * @return 支付前处理URL
//	 */
//	public String getBeforePayUrl(PaymentPlugin paymentPlugin, PaymentSessionEntity paymentSession, String params,boolean relativePath) {
//		Assert.notNull(paymentPlugin,"支付插件对象不能为NULL");
//		Assert.hasText(paymentPlugin.getId(),"支付插件的ID不能为空");
//		Assert.notNull(paymentSession,"支付会话对象不能为NULL");
//		Assert.hasText(paymentSession.getSn(),"支付会话的ID不能为空");
//		
//		return createBeforePayUrl(paymentPlugin,paymentSession,params,relativePath);
//	}
//	
//	/**
//	 * 获取支付前处理URL
//	 * 
//	 * @param paymentPlugin
//	 *            支付插件
//	 * @param PaymentSession
//	 *            支付事务
//	 * @param params
//	 *            附加内容
//	 * @param relativePath
//	 *            true返回相对URL，false返回绝对URL
//	 * @return 支付前处理URL
//	 */
//	private String createBeforePayUrl(PaymentPlugin paymentPlugin, PaymentSessionEntity paymentSession, String params,boolean  relativePath) {
//		if(relativePath) {
//			return "beforePay?psSn=" + paymentSession.getSn() + (StringUtils.isNotEmpty(params) ? "&params=" + params : "");
//		}
//		else {
//			return shopContextInfo.getSiteUrl() + "/member/payment/beforePay?psSn=" + paymentSession.getSn() + (StringUtils.isNotEmpty(params) ? "&params=" + params : "");
//		}
//	}
//
//	/**
//	 * 获取支付处理URL
//	 * 
//	 * @param paymentPlugin
//	 *            支付插件
//	 * @param PaymentSession
//	 *            支付事务
//	 * @return 支付处理URL,，绝对路径URL
//	 */
//	public String getPayUrl(PaymentPlugin paymentPlugin, PaymentSessionEntity paymentSession) {
//		return getPayUrl(paymentPlugin, paymentSession, null);
//	}
//
//	/**
//	 * 获取支付处理URL
//	 * 
//	 * @param paymentPlugin
//	 *            支付插件
//	 * @param PaymentSession
//	 *            支付事务
//	 * @param params
//	 *            附加内容
//	 * @return 支付处理URL,，绝对路径URL
//	 */
//	public String getPayUrl(PaymentPlugin paymentPlugin, PaymentSessionEntity paymentSession, String params) {
//		return getPayUrl(paymentPlugin,paymentSession,params,false);
//	}
//	
//	/**
//	 * 获取支付处理URL
//	 * 
//	 * @param paymentPlugin
//	 *            支付插件
//	 * @param PaymentSession
//	 *            支付事务
//	 * @param params
//	 *            附加内容
//	 * @param relativePath
//	 *            true返回相对URL，false返回绝对URL
//	 * @return 支付处理URL
//	 */
//	public String getPayUrl(PaymentPlugin paymentPlugin, PaymentSessionEntity paymentSession, String params, boolean relativePath) {
//		Assert.notNull(paymentPlugin,"支付插件对象不能为NULL");
//		Assert.hasText(paymentPlugin.getId(),"支付插件的ID不能为空");
//		Assert.notNull(paymentSession,"支付会话对象不能为NULL");
//		Assert.hasText(paymentSession.getSn(),"支付会话的ID不能为空");
//
//		return createPayUrl(paymentPlugin,paymentSession,params,relativePath);
//	}
//	
//	/**
//	 * 获取支付处理URL
//	 * 
//	 * @param paymentPlugin
//	 *            支付插件
//	 * @param PaymentSession
//	 *            支付事务
//	 * @param params
//	 *            附加内容
//	 * @param relativePath
//	 *            true返回相对URL，false返回绝对URL
//	 * @return 支付处理URL
//	 */
//	private String createPayUrl(PaymentPlugin paymentPlugin, PaymentSessionEntity paymentSession, String params,boolean relativePath) {
//		if(relativePath) {
//			return "pay?psSn=" + paymentSession.getSn() + (StringUtils.isNotEmpty(params) ? "&params=" + params : "");
//		}
//		else {
//			return shopContextInfo.getSiteUrl() + "/member/payment/pay?psSn=" + paymentSession.getSn() + (StringUtils.isNotEmpty(params) ? "&params=" + params : "");
//		}
//	}
//
//
//	/**
//	 * 获取支付后处理URL
//	 * 
//	 * @param paymentPlugin
//	 *            支付插件
//	 * @param PaymentSession
//	 *            支付事务
//	 * @return 支付后处理URL
//	 */
//	public String getAfterPayUrl(PaymentPlugin paymentPlugin, PaymentSessionEntity paymentSession) {
//		return getAfterPayUrl(paymentPlugin, paymentSession, null);
//	}
//
//	/**
//	 * 获取支付后处理URL
//	 * 
//	 * @param paymentPlugin
//	 *            支付插件
//	 * @param PaymentSession
//	 *            支付事务
//	 * @param params
//	 *            附加内容
//	 * @return 支付后处理URL
//	 */
//	public String getAfterPayUrl(PaymentPlugin paymentPlugin, PaymentSessionEntity paymentSession, String params) {
//		Assert.notNull(paymentPlugin,"支付插件对象不能为NULL");
//		Assert.hasText(paymentPlugin.getId(),"支付插件的ID不能为空");
//		Assert.notNull(paymentSession,"支付会话对象不能为NULL");
//		Assert.hasText(paymentSession.getSn(),"支付会话的ID不能为空");
//
//		return shopContextInfo.getSiteUrl() + "/member/payment/afterPay/" + paymentSession.getSn() + (StringUtils.isNotEmpty(params) ? "/" + params : "");
//	}
//
//	
//
//	/**
//	 * 获取超时时间
//	 * 
//	 * @return 超时时间
//	 */
//	public Integer getTimeout() {
//		return PaymentPlugin.DEFAULT_TIMEOUT;
//	}
//
//	/**
//	 * 计算支付手续费
//	 * 
//	 * @param amount
//	 *            金额
//	 * @return 支付手续费
//	 */
//	public BigDecimal calculateFee(BigDecimal amount) {
//		Assert.notNull(amount,"金额不能为Null");
//		Assert.state(amount.compareTo(BigDecimal.ZERO) >= 0,"金额不能小于零");
//
//		if (amount.compareTo(BigDecimal.ZERO) == 0) {
//			return BigDecimal.ZERO;
//		}
//		BigDecimal Fee = getFee(); 
//		if (PaymentPlugin.ChargeType.ratable.equals(getChargeType())) {
//			return ShopContextInfo.setScale(amount.multiply(Fee));
//		} else {
//			return ShopContextInfo.setScale(Fee);
//		}
//	}
//
//	/**
//	 * 计算支付金额
//	 * 
//	 * @param amount
//	 *            金额
//	 * @return 支付金额
//	 */
//	public BigDecimal calculateAmount(BigDecimal amount) {
//		Assert.notNull(amount,"金额不能为Null");
//		Assert.state(amount.compareTo(BigDecimal.ZERO) >= 0,"金额不能小于零");
//
//		return amount.add(calculateFee(amount)).setScale(2, RoundingMode.UP);
//	}
//
//
//	/**
//	 * 实现compareTo方法
//	 * 
//	 * @param paymentPlugin
//	 *            支付插件
//	 * @return 比较结果
//	 */
//	public int compareTo(PaymentPlugin paymentPlugin) {
//		if (paymentPlugin == null) {
//			return 1;
//		}
//		return new CompareToBuilder().append(getOrder(), paymentPlugin.getOrder()).append(getId(), paymentPlugin.getId()).toComparison();
//	}
//
//	/**
//	 * 重写equals方法
//	 * 
//	 * @param obj
//	 *            对象
//	 * @return 是否相等
//	 */
//	@Override
//	public boolean equals(Object obj) {
//		if (obj == null) {
//			return false;
//		}
//		if (getClass() != obj.getClass()) {
//			return false;
//		}
//		if (this == obj) {
//			return true;
//		}
//		PaymentPlugin other = (PaymentPlugin) obj;
//		return new EqualsBuilder().append(getId(), other.getId()).isEquals();
//	}
//
//	/**
//	 * 重写hashCode方法
//	 * 
//	 * @return HashCode
//	 */
//	@Override
//	public int hashCode() {
//		return new HashCodeBuilder(17, 37).append(getId()).toHashCode();
//	}
//	
//
//	/** 
//	* @Title: getXmlContentByRequest 
//	* @Description: TODO(获取HTTP请求BODY内容) 
//	* @param @param request
//	* @param @return
//	* @param @throws IOException  参数说明 
//	* @return String    返回类型 
//	* @throws 
//	*/
//	protected String getContentByRequest(HttpServletRequest request)  {
//		String resXml = null;
//		try {
//			resXml = IOUtils.toString(request.getInputStream(), "UTF-8");
//		} catch (IOException e) {
//		    e.printStackTrace();
//		}
//		return resXml;
//	}
//
//	/**
//	 * 获取安装URL
//	 * 
//	 * @return 安装URL
//	 */
//	public abstract String getInstallUrl();
//
//	/**
//	 * 获取卸载URL
//	 * 
//	 * @return 卸载URL
//	 */
//	public abstract String getUninstallUrl();
//
//	/**
//	 * 获取设置URL
//	 * 
//	 * @return 设置URL
//	 */
//	public abstract String getSettingUrl();
//	
//	/**
//	 * 支付处理
//	 * 
//	 * @param paymentPlugin
//	 *            支付插件
//	 * @param PaymentSession
//	 *            支付事务
//	 * @param paymentDescription
//	 *            支付描述
//	 * @param params
//	 *            附加内容
//	 * @param request
//	 *            HttpServletRequest
//	 * @param response
//	 *            HttpServletResponse
//	 * @param modelAndView
//	 *            ModelAndView
//	 * @throws Exception
//	 */
//	public abstract void payHandle(PaymentPlugin paymentPlugin, PaymentSessionEntity paymentSession, String paymentDescription, String params, HttpServletRequest request, HttpServletResponse response, ModelAndView modelAndView) throws Exception;
//
//	/**
//	 * 判断是否支付成功
//	 * 
//	 * @param paymentPlugin
//	 *            支付插件
//	 * @param PaymentSession
//	 *            支付事务
//	 * @param paymentDescription
//	 *            支付描述
//	 * @param params
//	 *            附加内容
//	 * @param request
//	 *            HttpServletRequest
//	 * @param response
//	 *            HttpServletResponse
//	 * @return 是否支付成功
//	 * @throws Exception
//	 */
//	public abstract boolean isPaySuccess(PaymentPlugin paymentPlugin, PaymentSessionEntity paymentSession, String paymentDescription, String params, HttpServletRequest request, HttpServletResponse response) throws Exception;
//
//	/** 
//	* @Title: verifyNotifyData 
//	* @Description: TODO(验证支付回调请求报文是否合法) 
//	* @param @param paymentSession
//	* @param @param request
//	* @param @param response  参数说明 
//	* @return void    返回类型 
//	* @throws 
//	*/
//	public abstract boolean verifyNotifyData(PaymentSessionEntity paymentSession,Map<String, String> reqMap, HttpServletRequest request,
//			HttpServletResponse response) ;
//	
//	/** 
//	* @Title: getProcessResultView 
//	* @Description: TODO(验证支付回调请求报文是否合法) 
//	* @param  isProcessSuccess 是否返回已经处理完成报文，给第三支付系统 ，true返回处理完成视图，false返回处理失败视图。
//	* @return ModelAndView    返回类型 
//	* @throws 
//	*/
//	public abstract ModelAndView getProcessResultView(boolean isProcessSuccess) ;
//	
//
//	/** 
//	* @Title: refund 
//	* @Description: TODO(向第三方支付系统申请退款) 
//	* @param @param orderRefundEntity  订单退款
//	* @return boolean    返回类型 true申请退款成功 ，申请退款失败 
//	* @throws 
//	*/
//	public abstract  boolean refund(OrderRefundEntity orderRefundEntity);
//
//	/** 
//	* @Title: refundQuery 
//	* @Description: TODO(向第三系统查询退款结果，即退款金额是否已经到达目标账户) 
//	* @param @param orderRefundEntity  订单退款
//	* @return int    返回类型 1退款已经成功到账，0退款还未完成，-1退款异常。 
//	* @throws 
//	*/
//	public abstract int refundQuery(OrderRefundEntity orderRefundEntity);
//
//	/** 
//	* @Title: verifyRefundNotifyData 
//	* @Description: TODO(验证退款回调报文并解密报文) 
//	* @param reqInfo 加密信息
//	* @param decryptDataMap 解密后的参数MAP
//	* @param paymentPlugin 支付插件
//	* @param modelAndView 返回视图
//	* @return boolean  是否获取成功
//	* @throws 
//	*/
//    public abstract boolean verifyRefundNotifyData(String reqInfo, PaymentPlugin paymentPlugin,
//			Map<String, String> decryptDataMap) ;
//
//    
//    /**
//     * 
//    * @Title: isRefundSuccess 
//    * @Description: TODO(根据退款解密的报报文件验证是否退款成功，如果成功返回outTraceNo,否则返回 null)
//    * @param decryptDataMap 已经解密的数据
//    * @param OrderRefundEntity 返回outTradeNo
//    * @return 
//    * @throws
//     */
//	public abstract boolean getRefundNotifyInfo(Map<String, String> decryptDataMap,OrderRefundEntity orf);

}