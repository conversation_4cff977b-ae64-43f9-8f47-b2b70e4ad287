package com.foshan.dao.shop.impl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.foshan.dao.generic.HibernateDao;
import com.foshan.dao.generic.Page;
import com.foshan.dao.shop.ICompanyInvoiceInfoDao;
import com.foshan.entity.shop.CompanyInvoiceInfoEntity;
import com.foshan.entity.shop.PurchasingCompanyEntity;
@Repository("companyInvoiceInfoDao")
public class CompanyInvoiceInfoDaoImp  extends HibernateDao< CompanyInvoiceInfoEntity,Integer> implements ICompanyInvoiceInfoDao {

	/* (non-Javadoc)
	 * @see com.foshan.dao.shop.CompanyInvoiceInfoDao#getCompanyInvoiceInfoList(com.foshan.dao.generic.Page, com.foshan.entity.shop.PurchasingCompanyEntity, java.lang.String, java.lang.Integer, java.lang.Integer, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	public Page<CompanyInvoiceInfoEntity> getCompanyInvoiceInfoList(Page<CompanyInvoiceInfoEntity> page, PurchasingCompanyEntity company,
			String companyName, Integer auditState, Integer invoiceType, String blank, String blankAccount,
			String paytaxNo, String registeredAddress, String registeredTelephone) {
		StringBuilder hql = new StringBuilder();

		Map<String, Object> params = new HashMap<String, Object>();
		hql.append(
				"select p from CompanyInvoiceInfoEntity p where p.state = 1");
		if (company != null) {
			hql.append(" and p.company = :company");
			params.put("company", company);
		}
		if (StringUtils.isNotEmpty(companyName)) {
			hql.append(" and p.companyName like :companyName");
			params.put("companyName", "%"+companyName+"%");
		}
		if (auditState != null) {
			hql.append(" and p.auditState = :auditState");
			params.put("auditState", auditState); 
		}
		if (invoiceType != null) {
			hql.append(" and p.invoiceType = :invoiceType");
			params.put("invoiceType", invoiceType); 
		}
		if (StringUtils.isNotEmpty(blank)) {
			hql.append(" and p.blank like :blank");
			params.put("blank", "%"+blank+"%");
		}
		if (StringUtils.isNotEmpty(blankAccount)) {
			hql.append(" and p.blankAccount like :blankAccount");
			params.put("blankAccount", "%"+blankAccount+"%");
		}
		if (StringUtils.isNotEmpty(paytaxNo)) {
			hql.append(" and p.paytaxNo like :paytaxNo");
			params.put("paytaxNo", "%"+blank+"%");
		}
		if (StringUtils.isNotEmpty(blankAccount)) {
			hql.append(" and p.registeredAddress like :registeredAddress");
			params.put("registeredAddress", "%"+registeredAddress+"%");
		}
		if (StringUtils.isNotEmpty(registeredTelephone)) {
			hql.append(" and p.registeredTelephone like :registeredTelephone");
			params.put("registeredTelephone", "%"+registeredTelephone+"%");
		}
		hql.append(" order by p.lastModifyTime desc");
		return queryPage(page, hql.toString(), params);
	
		
	}

}
