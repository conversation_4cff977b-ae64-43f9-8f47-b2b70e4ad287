package com.foshan.dao.shop;

import java.util.List;

import com.foshan.dao.generic.GenericDao;
import com.foshan.dao.generic.Page;
import com.foshan.entity.shop.CourierFeeScheduleEntity;

public interface ICourierFeeScheduleDao extends  GenericDao<CourierFeeScheduleEntity,Integer>{

	void getCourierFeeScheduleListPage(Page<CourierFeeScheduleEntity> page, String name, Integer courierType,
			Integer scheduleType, Integer storeId, Integer scheduleLevel);
	
	List<CourierFeeScheduleEntity> getCourierFeeScheduleList(String name, Integer courierType,
			Integer scheduleType, Integer storeId, Integer scheduleLevel,Integer isValid);
}
