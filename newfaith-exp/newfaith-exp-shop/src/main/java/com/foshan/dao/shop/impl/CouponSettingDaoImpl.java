package com.foshan.dao.shop.impl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.foshan.dao.generic.HibernateDao;
import com.foshan.dao.generic.Page;
import com.foshan.dao.shop.ICouponSettingDao;
import com.foshan.entity.shop.CouponSettingEntity;
import com.foshan.shop.promotion.PromotionType;


@Repository("couponSettingDao")
public class CouponSettingDaoImpl  extends HibernateDao<CouponSettingEntity,Integer> implements ICouponSettingDao {

	@Override
	public Page<CouponSettingEntity> getCouponSettingList(Page<CouponSettingEntity> page, String name, PromotionType couponType, Integer modelState,
			Integer storeId) {
		// TODO Auto-generated method stub
		StringBuilder hql = new StringBuilder();

		Map<String, Object> params = new HashMap<String, Object>();
		hql.append(
				"select p from CouponSettingEntity p inner join p.store s where p.state = 1");
		if (storeId != null) {
			hql.append(" and s.id = :storeId");
			params.put("storeId", storeId);
		}
		if (couponType != null) {
			hql.append(" and p.couponType = :couponType");
			params.put("couponType", couponType); 
		}
		if (StringUtils.isNotEmpty(name)) {
			hql.append(" and p.modelName like :name");
			params.put("name", "%"+name+"%");
		}
		if (modelState != null) {
			hql.append(" and p.modelState = :modelState");
			params.put("modelState", modelState);
		}
		hql.append(" order by p.lastModifyTime desc");
		return queryPage(page, hql.toString(), params);
	}

}
