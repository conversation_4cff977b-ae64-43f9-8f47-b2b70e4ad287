package com.foshan.form.shop.response.statistical;

import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;
import com.foshan.form.shop.SalesDetailForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class GetSalesDetailRes extends BasePageResponse{
	/**
	 * 
	 */
	private static final long serialVersionUID = 6539315968345648256L;
	private List<SalesDetailForm> salesDetailFormList = new ArrayList<SalesDetailForm>();
	public GetSalesDetailRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}
	public GetSalesDetailRes(List<SalesDetailForm> salesDetailFormList) {
		this.salesDetailFormList= salesDetailFormList;
	}
}
