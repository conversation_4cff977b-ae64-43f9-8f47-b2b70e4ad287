package com.foshan.form.shop.request;

import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "鉴权对象(AuditReq)")
public class AuthenticationReq extends BasePageRequest{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 5034947074875438081L;
	@ApiModelProperty(value = "会员ID", example = "1")
	private Integer memberId;
	@ApiModelProperty(value = "产品ID", example = "1")
	private Integer productId;
	@ApiModelProperty(value = "视频ID", example = "1")
	private Integer videoId;

}
