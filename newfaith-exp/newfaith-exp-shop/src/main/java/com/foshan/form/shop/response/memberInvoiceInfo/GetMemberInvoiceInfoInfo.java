package com.foshan.form.shop.response.memberInvoiceInfo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.shop.MemberInvoiceInfoForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取会员发票信息返回对象(GetMemberInvoiceInfoInfo)")
@JsonInclude(Include.NON_NULL)
public class GetMemberInvoiceInfoInfo extends BaseResponse{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 6589354320179316052L;
	@ApiModelProperty(value = "会员发票信息",example="1")
	private MemberInvoiceInfoForm memberInvoiceInfoForm; 

	public GetMemberInvoiceInfoInfo(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO 自动生成的构造函数存根
	}
	public GetMemberInvoiceInfoInfo(MemberInvoiceInfoForm memberInvoiceInfoForm) {
		super();
		this.memberInvoiceInfoForm = memberInvoiceInfoForm;
	}

	
}
