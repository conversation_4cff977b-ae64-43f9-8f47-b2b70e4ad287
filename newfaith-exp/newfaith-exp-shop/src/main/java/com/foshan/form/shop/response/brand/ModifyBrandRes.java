package com.foshan.form.shop.response.brand;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "修改品牌返回对象(ModifyBrandRes)")
@JsonInclude(Include.NON_NULL)
public class ModifyBrandRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 2549609914302708432L;
	@ApiModelProperty(value = "品牌Id",example="1")
	private Integer brandId;
	@ApiModelProperty(value = "品牌名称")
	private String brandName;
	@ApiModelProperty(value = "品牌logo路径")
	private String logoFile;
	@ApiModelProperty(value = "品牌类型",example="1")
	private Integer brandType;
	@ApiModelProperty(value = "品牌网址")
	private String brandURL;
	@ApiModelProperty(value = "排序值",example="1")
	private Integer orders;
	@ApiModelProperty(value = "关键字")
	private String keywords;
	@ApiModelProperty(value = "说明")
	private String introduction;

	public ModifyBrandRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public ModifyBrandRes(Integer brandId, String brandName, String logoFile, Integer brandType, String brandURL,
			Integer orders, String keywords, String introduction) {
		super();
		this.brandId = brandId;
		this.brandName = brandName;
		this.logoFile = logoFile;
		this.brandType = brandType;
		this.brandURL = brandURL;
		this.orders = orders;
		this.keywords = keywords;
		this.introduction = introduction;
	}

	
}
