package com.foshan.form.shop;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "特殊属性对象(SpecialParameter)")
@JsonInclude(Include.NON_NULL)
public class SpecialParameter implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4207189648992359175L;
	@ApiModelProperty(value = "sku属性名称")
	private String name;
	@ApiModelProperty(value = "sku属性待选值")
	private String[] options;


	public SpecialParameter(String name, String[] options) {
		super();
		this.name = name;
		this.options = options;
	}


	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
