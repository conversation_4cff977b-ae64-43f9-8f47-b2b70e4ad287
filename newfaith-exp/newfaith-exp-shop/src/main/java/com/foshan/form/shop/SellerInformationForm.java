package com.foshan.form.shop;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL) 
public class SellerInformationForm implements IForm{
	/**
	 * 
	 */
	private static final long serialVersionUID = 5493397066187753809L;
	private Integer  sellerInformationId;

	//方便以后对接不同平台，用字符串类型
	private String storeId;
	//来源系统标识
	private String mappingSystem;
	//开票方名称
	//private String kp_nsrmc;
	private String drawerName;
	//销售方纳税人识别号
	//private String xsf_nsrsbh;
	private String sellerIdentifyNumber;
	//销售方名称
	//private String xsf_mc;
	private String sellerName;
	//销售方地址
	//private String xsf_dz;
	private String sellerAddre;
	//销售方电话
	//private String xsf_dh;
	private String sellerPhone;
	//销售方银行账号
	//private String xsf_yhzh;
	private String sellerBankAccount;
	
	//开票人
	//private String kpr;
	private String drawer;
	//收款人
	//private String skr;
	private String payee;
	//复核人
	//private String fhr;
	private String checker;
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
	
	
}
