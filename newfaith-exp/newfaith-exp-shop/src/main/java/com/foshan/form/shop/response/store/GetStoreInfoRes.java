package com.foshan.form.shop.response.store;


import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.shop.ProductLimitForm;
import com.foshan.form.shop.QuestionAnswerForm;
import com.foshan.form.shop.StoreForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class GetStoreInfoRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4118285313695319412L;
	private StoreForm storeForm;

	public GetStoreInfoRes(StoreForm storeForm) {
		super();
		this.storeForm = storeForm;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
