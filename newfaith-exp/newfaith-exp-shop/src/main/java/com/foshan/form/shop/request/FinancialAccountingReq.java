package com.foshan.form.shop.request;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
public class FinancialAccountingReq extends BasePageRequest{
	/**
	 * 
	 */
	private static final long serialVersionUID = 3675346920746250479L;
	@ApiModelProperty(value = "业务标识ＩＤ")
	private String businessCode;
	@ApiModelProperty(value = "部门编码")
	private String departmentCode;
	@ApiModelProperty(value = "部门名称")
	private String departmentName;
	@ApiModelProperty(value = "客商编码")
	private String supplierCode;
	@ApiModelProperty(value = "客商名称")
	private String supplierName;
	@ApiModelProperty(value = "期间")
	private String period;
	@ApiModelProperty(value = "收支项目编码")
	private String expensesReceiptsCode;
	@ApiModelProperty(value = "收支项目名称")
	private String expensesReceiptsName;
	@ApiModelProperty(value = "含税收入金额")
	private String amountReceived;
	@ApiModelProperty(value = "保留字段")
	private String reserved;
	@ApiModelProperty(value = "税率")
	private String taxRate;
	@ApiModelProperty(value = "税金")
	private String expensesTaxation;
	@ApiModelProperty(value = "是否集客标识")
	private String gatheringCustomers;
	@ApiModelProperty(value = "系统来源，区分不同系统的数据")
	private String systemSource;
	@ApiModelProperty(value = "源系统数据标识")
	private String systemSourceCode;
	

}
