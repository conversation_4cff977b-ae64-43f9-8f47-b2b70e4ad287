package com.foshan.form.shop.request;

import com.foshan.form.request.BasePageRequest;
import com.foshan.form.shop.LiveTicketsForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
@Getter
@Setter
@NoArgsConstructor
public class LiveTicketsReq extends BasePageRequest {
    /**
	 * 
	 */
	private static final long serialVersionUID = 6636289645925158040L;

    @ApiModelProperty(value = "门票信息")
    private ArrayList<LiveTicketsForm> liveTicketsList = new ArrayList<LiveTicketsForm>();


   

}
