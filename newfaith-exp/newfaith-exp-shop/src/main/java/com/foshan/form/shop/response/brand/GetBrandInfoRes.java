package com.foshan.form.shop.response.brand;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取品牌信息返回对象(GetBrandInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetBrandInfoRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6570876282193920942L;
	@ApiModelProperty(value = "品牌Id",example="1")
	private Integer brandId;
	@ApiModelProperty(value = "品牌名称")
	private String brandName;
	@ApiModelProperty(value = "品牌logo路径")
	private String logoFile;
	@ApiModelProperty(value = "品牌类型",example="1")
	private Integer brandType;
	@ApiModelProperty(value = "品牌网址")
	private String brandURL;
	@ApiModelProperty(value = "排序值",example="1")
	private Integer orders;
	@ApiModelProperty(value = "关键字")
	private String keywords;
	@ApiModelProperty(value = "说明")
	private String introduction;
	@ApiModelProperty(value = "状态 0--无效 1--有效",example="1")
	private Integer state;


	public GetBrandInfoRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}


}
