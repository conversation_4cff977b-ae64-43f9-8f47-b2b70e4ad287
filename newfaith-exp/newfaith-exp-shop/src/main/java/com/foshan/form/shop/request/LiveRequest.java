package com.foshan.form.shop.request;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取微信直播请求对象(LiveRequest)")
public class LiveRequest extends BasePageRequest{
	/**
	 * 
	 */
	private static final long serialVersionUID = 8381228038482735405L;
	@ApiModelProperty(value = "直播间Id", example = "1")
	private Integer roomId;

}
