package com.foshan.form.shop.response.invoice;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "修改企业发票信息返回对象(AddCompanyInvoiceInfoRes)")
@JsonInclude(Include.NON_NULL)
public class ModifyCompanyInvoiceInfoRes extends GenericResponse {

	private static final long serialVersionUID = -630103719042146275L;
	@ApiModelProperty(value = "Integer comment '修改企业发票信息的Id'",example="1")
	Integer companyInvoiceInfoId;

	public ModifyCompanyInvoiceInfoRes(Integer companyInvoiceInfoId) {
		super();
		this.companyInvoiceInfoId = companyInvoiceInfoId;
	}

	public ModifyCompanyInvoiceInfoRes(String ret, String retInfo) {
		super(ret, retInfo);
	}
	
	
}
