package com.foshan.form.shop.request;
import com.foshan.form.request.BaseRequest;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
/** 
* @ClassName: GetPluginConfigListBySnReq 
* @Description: TODO(获取支付信息的请求报文) 
* <AUTHOR>
* @date 2019年3月12日 下午11:48:55 
*  
*/
@Getter
@Setter
@NoArgsConstructor
public class GetPaymentInfoReq extends BaseRequest {
     

	private static final long serialVersionUID = -4344398571372825407L;
	
	private int orderId;
	private int paymentMethodId;

}
