package com.foshan.form.shop.response.store;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;
import com.foshan.form.shop.ProductForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class GetStoreProductListRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8529352163886132274L;
	private Integer storeId;
	private String storeName;
	private List<ProductForm> productList = new ArrayList<ProductForm>();

	public GetStoreProductListRes(int currentPage, int pageSize, int total, int totalResult) {
		super(currentPage, pageSize, total, totalResult);
		// TODO Auto-generated constructor stub
	}

	public GetStoreProductListRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}

	public GetStoreProductListRes(Integer storeId, String storeName, List<ProductForm> productList) {
		super();
		this.storeId = storeId;
		this.storeName = storeName;
		this.productList = productList;
	}


	public static long getSerialversionuid() {
		return serialVersionUID;
	}

}
