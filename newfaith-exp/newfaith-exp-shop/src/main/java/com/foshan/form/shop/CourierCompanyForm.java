package com.foshan.form.shop;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="物流公司对象(CourierCompanyForm)")
@JsonInclude(Include.NON_NULL)
public class CourierCompanyForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = -6631292729315362710L;
	@ApiModelProperty(value = "物流公司Id",example="1")
	private Integer courierId;
	@ApiModelProperty(value = "物流公司名称")
	private String companyName;
	@ApiModelProperty(value = "物流公司缩写")
	private String companyCode;
	@ApiModelProperty(value = "物流公司说明")
	private String companyInfo;
	@ApiModelProperty(value = "物流公司收费列表")
	private List<CourierFeeScheduleForm> feeList = new ArrayList<CourierFeeScheduleForm>();

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
