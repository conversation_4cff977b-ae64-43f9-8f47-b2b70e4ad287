package com.foshan.form.shop.request;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "企业采购员登录请求对象(PromotionReq)",description="")
public class PurchasingAgentReq extends BasePageRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1605853280718578596L;
	@ApiModelProperty(value = "密码")
	private String password;
	@ApiModelProperty(value = "帐号")
	private String loginName;

}
