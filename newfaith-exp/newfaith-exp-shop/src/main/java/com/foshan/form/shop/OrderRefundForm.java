/**   
 * Copyright © 2019 gcable foshan Info. Tech Ltd. All rights reserved.
 * 
 * @Package: com.foshan.form.shop 
 * @author: pgq   
 * @date: 2019年4月13日 下午9:11:49 
 */
package com.foshan.form.shop;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.entity.shop.OrderRefundEntity;
import com.foshan.form.IForm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 *
 */
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class OrderRefundForm implements IForm {
	
	private static final long serialVersionUID = 586563816134101074L;

	private int id;

	/**
	 * 编号
	 */
	private String sn;

	/**
	 * 订单退款类型
	 */
	private OrderRefundEntity.Method method;

	/**
	 * 退款方式
	 */
	private String paymentMethod;

	/**
	 * 收款银行
	 */
	private String bank;

	/**
	 * 收款账号
	 */
	private String account;

	/**
	 * 退款金额
	 */
	private BigDecimal amount;

	/**
	 *  退款使用的支付插件SN
	 */
	private String paymentPlunginSn;
	
	/**
	 * 第三方支付系统交易ID/退款ID
	 */
	private String outTradeNo;
	
	/**
	 * 向第三方系统支付时传入的订单编号
	 */
	private String paymentSessionSn;
	
	/**
	 * 订单退款状态：0未向支付系统发起退款申请；1已经发起退款请求第三方支付正在处理退款；2完成退款退已经到达标账户；3退款出现异常
	 */
	private Integer refundStatus;
	
	/**
	 * 订单退款状态：0未向支付系统发起退款申请；1已经发起退款请求第三方支付正在处理退款；2完成退款退已经到达标账户；3退款出现异常
	 */
	private String refundStatuName;
	
	/**
	 * 退款申请创建时间
	 */
	private String createTime;
	
	/**
	 *收款人
	 */
	private String payee;

	/**
	 * 备注
	 */
	private String memo;

	/**
	 * 订单SN
	 */
	private String orderCode;
	

	/**
	 * 订单ID
	 */
	private Integer orderId;
	
	/**
	 * 订单项ID
	 */
	private Integer orderItemId;
	
	/**
	 * 退款失败原因
	 */
	private String failReason;
	
	/**
	 * 申请退款原因
	 */
	private String refundReason;
	
	
	public void setMethod(OrderRefundEntity.Method method) {
		this.method = method;
	}

	

	/* (non-Javadoc)
	 * @see java.lang.Comparable#compareTo(java.lang.Object)
	 */
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
	
	

}
