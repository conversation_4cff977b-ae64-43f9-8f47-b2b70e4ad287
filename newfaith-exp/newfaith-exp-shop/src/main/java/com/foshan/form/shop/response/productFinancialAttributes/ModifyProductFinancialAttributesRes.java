package com.foshan.form.shop.response.productFinancialAttributes;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "修改产品财务属性返回对象(ModifyProductFinancialAttributesRes)")
@JsonInclude(Include.NON_NULL)
public class ModifyProductFinancialAttributesRes extends BasePageResponse {
	
	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -601806185421420068L;


	public ModifyProductFinancialAttributesRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO Auto-generated constructor stub
	}
}
