package com.foshan.controller.shop;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.shop.request.PromotionReq;
import com.foshan.form.shop.response.product.GetProductListRes;
import com.foshan.form.shop.response.promotion.AddPromotionRes;
import com.foshan.form.shop.response.promotion.GetPromotionListRes;
import com.foshan.form.shop.response.promotion.GetPromotionRes;
import com.foshan.form.shop.response.promotion.ModifyPromotionRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "商城--促销活动模块")
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
public class PromotionController extends BaseShopController {

	// 获取促销活动列表
	@ApiOperation(value = "获取促销活动列表(GetPromotionList)", httpMethod = "POST", notes = "获取促销活动列表<p>1:平台管理员权限storeId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getPromotionList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetPromotionListRes getPromotionList(@RequestBody PromotionReq req,HttpServletRequest request)throws JsonProcessingException   {
		GetPromotionListRes res = (GetPromotionListRes)promotionService.getPromotionList(req);
        return res;
    }

	// 获取促销活动
	@ApiOperation(value = "获取促销活动(GetPromotion)", httpMethod = "POST", notes = "获取促销活动")
	@ResponseBody
	@RequestMapping(value = "/getPromotion", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetPromotionRes getPromotion(@RequestBody PromotionReq req,HttpServletRequest request)throws JsonProcessingException   {
		GetPromotionRes res = (GetPromotionRes) promotionService.getPromotion(req);
        return res;
    }

	// 增加促销活动
	@ApiOperation(value = "新增促销活动(AddPromotion)", httpMethod = "POST", notes = "新增促销活动，添加成功后促销活动默认处于未生效状态<p>1:storeId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addPromotion", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public AddPromotionRes addPromotion(@RequestBody PromotionReq req,HttpServletRequest request)throws JsonProcessingException   {
		AddPromotionRes res = (AddPromotionRes) promotionService.addPromotion(req);
        return res;
    }

	// 修改促销活动
	@ApiOperation(value = "修改促销活动(ModifyPromotion)", httpMethod = "POST", notes = "修改促销活动，只有当活动处于未生效以及关闭状态才能修改，修改后促销活动会变成为未生效状态<p>1:id不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyPromotion", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ModifyPromotionRes modifyPromotion(@RequestBody PromotionReq req,HttpServletRequest request)throws JsonProcessingException   {
		ModifyPromotionRes res = (ModifyPromotionRes) promotionService.modifyPromotion(req);
        return res;
    }

	// 删除促销活动
	@ApiOperation(value = "删除促销活动(DeletePromotion)", httpMethod = "POST", notes = "删除促销活动<p>1:id不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deletePromotion", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse deletePromotion(@RequestBody PromotionReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse) promotionService.deletePromotion(req);
        return res;
    }

	// 根据促销活动id，获取关联的产品列表
	@ApiOperation(value = "查询促销关联的商品(getProductListByPromotion)", httpMethod = "POST", notes = " 根据促销活动ID，获取关联的商品列表<p>1:id不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getProductListByPromotion", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetProductListRes getProductListByPromotion(@RequestBody PromotionReq req,HttpServletRequest request)throws JsonProcessingException   {
		GetProductListRes res = (GetProductListRes) promotionService.getProductListByPromotion(req);
        return res;
    }

	// 获取可关联促销活动的所有产品列表
	@ApiOperation(value = "获取可关联促销活动的产品列表(getSupportPromotionProdctionList)", httpMethod = "POST", notes = " 获取可关联促销活动的商品列表")
	@ResponseBody
	@RequestMapping(value = "/getSupportPromotionProdctionList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetProductListRes getSupportPromotionProdctionList(@RequestBody PromotionReq req,HttpServletRequest request)throws JsonProcessingException   {
		GetProductListRes res = (GetProductListRes) promotionService.getSupportPromotionProdctionList(req);
        return res;
    }

	// 为促销活动添加参与的商品
	@ApiOperation(value = "为促销活动添加参与的商品(addProductToPromotion)", httpMethod = "POST", notes = "为促销活动添加参与的商品<p>1:id，促销ID，不能为空；<p>productIds不能为空，可以传多个商品ID，用逗号隔开；")
	@ResponseBody
	@RequestMapping(value = "/addProductToPromotion", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse addProductToPromotion(@RequestBody PromotionReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse) promotionService.addProductToPromotion(req);
        return res;
    }

	// 对某些商品取消参与促销活动
	@ApiOperation(value = "取消参与商品与促销活动的关联(deleteProductFromPromotion)", httpMethod = "POST", notes = "取消参与商品与促销活动的关联<p>1:id，促销ID，不能为空;<p>productIds不能为空，可以传多个商品ID，用逗号隔开;")
	@ResponseBody
	@RequestMapping(value = "/deleteProductFromPromotion", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse deleteProductFromPromotion(@RequestBody PromotionReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse) promotionService.deleteProductFromPromotion(req);
        return res;
    }

	// 使用促销活动生效
	@ApiOperation(value = "使用促销活动生效(EnablePromotion)", httpMethod = "POST", notes = "使未生效状态的促销活动变为生效状态<p>1:id不能为空，且对应的活动处于初始未生效状态；")
	@ResponseBody
	@RequestMapping(value = "/enablePromotion", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse enablePromotion(@RequestBody PromotionReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse) promotionService.enablePromotion(req);
        return res;
    }

	// 使用促销活动生效
	@ApiOperation(value = "关闭促销活动(DisablePromotion)", httpMethod = "POST", notes = "使生效状态的促销活动变为关闭状态<p>1:id不能为空，且对应的活动处于生效状态；")
	@ResponseBody
	@RequestMapping(value = "/disablePromotion", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse disablePromotion(@RequestBody PromotionReq req,HttpServletRequest request)throws JsonProcessingException   {
		GenericResponse res = (GenericResponse) promotionService.disablePromotion(req);
        return res;
    }


	// 获取指定商品的有效促销活动列表
	@ApiOperation(value = "获取指定商品的有效促销活动列表(GetValidPromotionsByProduct)", httpMethod = "POST", notes = "获取指定商品的有效促销活动列表<p>1:productIds 商品ID，必填且只能为一个值；")
	@ResponseBody
	@RequestMapping(value = "/getValidPromotionsByProduct", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetPromotionListRes getValidPromotionsByProduct(@RequestBody PromotionReq req,HttpServletRequest request)throws JsonProcessingException   {
		GetPromotionListRes res = (GetPromotionListRes) promotionService.getValidPromotionsByProduct(req);
        return res;
    }

}
