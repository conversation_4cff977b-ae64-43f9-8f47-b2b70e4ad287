package com.foshan.controller.shop;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.shop.request.GetSpecificationListReq;
import com.foshan.form.shop.request.ProductCategoryReq;
import com.foshan.form.shop.response.category.AddProductCategoryRes;
import com.foshan.form.shop.response.category.GetProductCategoryInfoRes;
import com.foshan.form.shop.response.category.GetProductCategoryListRes;
import com.foshan.form.shop.response.category.ModifyProductCategoryRes;
import com.foshan.form.shop.response.specification.GetProductSpecificationListRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "商城--产品分类模块")
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
public class ProductCategoryController extends BaseShopController {

	// 新增产品类别
	@ApiOperation(value = "新增产品分类(AddProductCategory)", httpMethod = "POST", notes = "新增产品分类<p>1：categoryName不能为空；<p>2：平台仅支持3级分类，如果新增分类属于末级分类，则还必须提供specifications值；")
	@ResponseBody
	@RequestMapping(value = "/addProductCategory", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public AddProductCategoryRes addProductCategory(@RequestBody ProductCategoryReq req, HttpServletRequest request)throws Exception   {
		AddProductCategoryRes res = (AddProductCategoryRes)productCategoryService.addProductCategory(req);
        return res;
    }

	// 修改产品类别
	@ApiOperation(value = "修改产品分类(ModifyProductCategory)", httpMethod = "POST", notes = "修改产品分类<p>1：categoryId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyProductCategory", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ModifyProductCategoryRes modifyProductCategory(@RequestBody ProductCategoryReq req, HttpServletRequest request)throws Exception   {
		ModifyProductCategoryRes res = (ModifyProductCategoryRes)productCategoryService.modifyProductCategory(req);
        return res;
    }

	// 删除产品类别
	@ApiOperation(value = "删除产品分类(DeleteProductCategory)", httpMethod = "POST", notes = "删除产品分类<p>1：categoryId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteProductCategory", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse deleteProductCategory(@RequestBody ProductCategoryReq req, HttpServletRequest request)throws Exception   {
		GenericResponse res = (GenericResponse)productCategoryService.deleteProductCategory(req);
        return res;
    }

	// 获取产品类别列表
	@ApiOperation(value = "获取产品分类列表(GetProductCategoryList)", httpMethod = "POST", notes = "获取产品分类列表")
	@ResponseBody
	@RequestMapping(value = "/getProductCategoryList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetProductCategoryListRes getProductCategoryList(@RequestBody ProductCategoryReq req, HttpServletRequest request)throws Exception   {
		GetProductCategoryListRes res = (GetProductCategoryListRes)productCategoryService.getProductCategoryList(req);
        return res;
    }

	// 获取类别产品列表
	@ApiOperation(value = "获取类别产品列表(GetProductCategoryProductList)", httpMethod = "POST", notes = "获取类别产品列表")
	@ResponseBody
	@RequestMapping(value = "/getProductCategoryProductList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetProductSpecificationListRes getProductCategoryProductList(@RequestBody GetSpecificationListReq req, HttpServletRequest request)throws Exception   {
		GetProductSpecificationListRes res = (GetProductSpecificationListRes)productCategoryService.getProductCategoryProductList(req);
        return res;
    }

	// 获取类别详情
	@ApiOperation(value = "获取类别详情(GetProductCategoryInfo)", httpMethod = "POST", notes = "获取类别详情<p>1：categoryId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getProductCategoryInfo", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetProductCategoryInfoRes getProductCategoryInfo(@RequestBody ProductCategoryReq req, HttpServletRequest request)throws Exception   {
		GetProductCategoryInfoRes res = (GetProductCategoryInfoRes)productCategoryService.getProductCategoryInfo(req);
        return res;
    }
}
