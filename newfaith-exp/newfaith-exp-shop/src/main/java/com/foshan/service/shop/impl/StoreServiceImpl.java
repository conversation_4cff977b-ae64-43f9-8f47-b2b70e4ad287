package com.foshan.service.shop.impl;
import java.io.UnsupportedEncodingException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.foshan.dao.generic.Page;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.RegionEntity;
import com.foshan.entity.RoleEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.shop.MemberEntity;
import com.foshan.entity.shop.ProductSpecificationEntity;
import com.foshan.entity.shop.ShopUserEntity;
import com.foshan.entity.shop.StoreEntity;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.shop.StoreForm;
import com.foshan.form.shop.request.GetSpecificationListReq;
import com.foshan.form.shop.request.ShopUserReq;
import com.foshan.form.shop.request.StoreReq;
import com.foshan.form.shop.response.shopUser.AddShopUserRes;
import com.foshan.form.shop.response.specification.GetProductSpecificationListRes;
import com.foshan.form.shop.response.store.AddStoreRes;
import com.foshan.form.shop.response.store.GetStoreInfoRes;
import com.foshan.form.shop.response.store.GetStoreListRes;
import com.foshan.form.shop.response.store.ModifyStoreRes;
import com.foshan.service.shop.IStoreService;
import com.foshan.util.AuthUtil;
import com.foshan.util.DateUtil;
import com.foshan.util.DigestUtil;

@Transactional
@Service("storeService")
public class StoreServiceImpl extends GenericShopService implements IStoreService {

	private final static Logger logger = LoggerFactory.getLogger(StoreServiceImpl.class);

	//@ResourcePermit
	@Override
	public IResponse addStore(StoreReq req, HttpServletRequest request) {
		// TODO Auto-generated method stub
		AddStoreRes res = new AddStoreRes();
		Object userObj = getPrincipal(false);
		if(null == userObj ) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		if(userObj instanceof PlatformUserEntity) {
			if (StringUtils.isNotEmpty(req.getStoreName()) && 
					null != req.getIsSelfSupport() &&
					null != req.getRegionId() &&
					StringUtils.isNotEmpty(req.getBusinessModel())) {
				StoreEntity store = new StoreEntity();
				BeanUtils.copyProperties(req, store);
				if (null != req.getParentStoreId()) {
					StoreEntity parentStore = storeDao.get(req.getParentStoreId());
					if (null != parentStore) {
						store.setParentStore(parentStore);
					} else {
						res.setRet(ResponseContext.RES_DATA_NULL_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
						return res;
					}
				}
				RegionEntity regionEntity  = null;
				if(null != req.getRegionId()) {
					 regionEntity = regionDao.get(req.getRegionId());
				}else{
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
				store.setRegion(regionEntity);
				store.setServicePhone(StringUtils.isNotEmpty(req.getServicePhone()) ? req.getServicePhone():"");
				store.setReservedField(StringUtils.isNotEmpty(req.getReservedField()) ? req.getReservedField():"");
				store.setPaymentMerchantCode(StringUtils.isNotEmpty(req.getPaymentMerchantCode()) ? req.getPaymentMerchantCode() : null);
				store.setPaymentSecret(StringUtils.isNotEmpty(req.getPaymentSecret()) ? req.getPaymentSecret() : null);
				if (null == req.getAutoAudit() || req.getAutoAudit() == EntityContext.AUTO_AUDIT_ARTIFICAL) {
					store.setAutoAudit(EntityContext.AUTO_AUDIT_ARTIFICAL);
					store.setAuditValidTime(null);
					store.setAutoUpShelf(EntityContext.AUTO_UPSHELF_CLOSE);
				} else {
					if (StringUtils.isNotEmpty(req.getAuditValidTime())) {
						try {
							store.setAuditValidTime(
									new Timestamp(DateUtil.parseLongFormat(req.getAuditValidTime()).getTime()));
							store.setAutoAudit(EntityContext.AUTO_AUDIT_AUTO);
							store.setAutoUpShelf(EntityContext.AUTO_UPSHELF_CLOSE);
						} catch (ParseException e) {
							res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
							res.setRetInfo("auditValidTime时间格式错误！！！");
							return res;
						}
					} else {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo("开启自动审核功能必须提供自动审核截至时间！！！");
						return res;
					}
				}

				store.setState(EntityContext.RECORD_STATE_VALID);

				Integer storeId = (Integer) storeDao.save(store);
				// 增加商铺的默认登录用户
				
				ShopUserReq addShopUserReq = new ShopUserReq();
				
				
				if (null != storeId) {
					addShopUserReq.setUserName(storeId + "shopadmin");
					addShopUserReq.setUserCode(storeId + "shopadmin");
					addShopUserReq.setStoreId(storeId);
					addShopUserReq.setRegionId(req.getRegionId());
					RoleEntity shopRole = roleDao.getUniqueByHql("select r from RoleEntity r where r.roleName = 'shopuser'");
					if(shopRole !=null) {
						addShopUserReq.setRoleIdList(shopRole.getId() + "");
					}
					AddShopUserRes addShopUserRes = (AddShopUserRes) addShopUser(addShopUserReq);
//					ShopUserEntity shopUser = new ShopUserEntity();
//					String pass = null;
//					try {
//						pass = BCUtil.sm3Digest(BCUtil.sm3Digest("123456"));
//						// DateUtil.formatByStyle(new Date(), "123456");
//					} catch (UnsupportedEncodingException e) {
//						// TODO Auto-generated catch block
//						e.printStackTrace();
//					}
//					shopUser.setStore(store);
//					shopUser.setUserState(EntityContext.RECORD_STATE_VALID);
//					shopUser.setUserPassword(MD5Util.MD5(pass));
	//
//					shopUser.setUserCode(storeId + "admin");
//					shopUser.setUserName(storeId + "admin");
//					shopUserDao.save(shopUser);

					if(StringUtils.isNotEmpty(addShopUserRes.getUserCode())) {
						res.setStoreAdminName(addShopUserRes.getUserCode());
						//res.setStoreAdminPassword("123456");
					}
					else {
						logger.info("创建店铺关联管理员失败！失败原因:{}{}",addShopUserRes.getRet(),addShopUserRes.getRetInfo());
					}
				}
				BeanUtils.copyProperties(req, res);
				if (null != store.getAuditValidTime()) {
					res.setAuditValidTime(DateUtil.formatLongFormat(store.getAuditValidTime()));
				}
				res.setStoreId(storeId);

				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			}
		}
		else {
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
		}
		
		return res;
	}

	//@ResourcePermit
	@Override
	public IResponse modifyStore(StoreReq req, HttpServletRequest request) {
		// TODO Auto-generated method stub
		ModifyStoreRes res = new ModifyStoreRes();
		Object userObj = getPrincipal(false);
		if(null == userObj) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		if((userObj instanceof PlatformUserEntity) || (userObj instanceof ShopUserEntity) ){
			if (null != req.getStoreId()) {
				if (userObj instanceof ShopUserEntity) {
					StoreEntity store = ((ShopUserEntity)userObj).getStore();
					req.setStoreId(store.getId());
				}
				StoreEntity store = storeDao.get(req.getStoreId());
				if (null != store) {
					StoreEntity parentStore = store.getParentStore();
					if (null != req.getParentStoreId() && (null == parentStore
							|| (null != parentStore && parentStore.getId() != req.getParentStoreId()))) {
						StoreEntity newParentStore = storeDao.get(req.getParentStoreId());
						if (null != newParentStore) {
							store.setParentStore(newParentStore);
							res.setParentStoreId(req.getParentStoreId());
						} else {
							res.setRet(ResponseContext.RES_DATA_NULL_CODE);
							res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
							return res;
						}
					}
					store.setState(null != req.getState() ? req.getState() : store.getState());
					store.setReservedField(StringUtils.isNotEmpty(req.getReservedField()) ? 
							req.getReservedField() : store.getReservedField());
					store.setStoreAddress(StringUtils.isNotEmpty(req.getStoreAddress()) ? req.getStoreAddress()
							: store.getStoreAddress());
					store.setStoreName(
							StringUtils.isNotEmpty(req.getStoreName()) ? req.getStoreName() : store.getStoreName());
					store.setIsSelfSupport(
							null != req.getIsSelfSupport() ? req.getIsSelfSupport() : store.getIsSelfSupport());
					store.setServicePhone(StringUtils.isNotEmpty(req.getServicePhone()) ? req.getServicePhone()
							: store.getServicePhone());
				
					store.setStoreChargeName(StringUtils.isNotEmpty(req.getStoreChargeName()) ? req.getStoreChargeName()
							: store.getStoreChargeName());
					store.setStoreChargePhone(StringUtils.isNotEmpty(req.getStoreChargePhone()) ? req.getStoreChargePhone()
							: store.getStoreChargePhone());
					store.setStoreMail(
							StringUtils.isNotEmpty(req.getStoreMail()) ? req.getStoreMail() : store.getStoreMail());
					store.setStoreLogo(
							StringUtils.isNotEmpty(req.getStoreLogo()) ? req.getStoreLogo() : store.getStoreLogo());
					store.setStoreImage(
							StringUtils.isNotEmpty(req.getStoreImage()) ? req.getStoreImage() : store.getStoreImage());
					store.setComment(
							StringUtils.isNotEmpty(req.getComment()) ? req.getComment() : store.getComment());
					store.setLogisticsFeeScheduleSetting(req.getLogisticsFeeScheduleSetting() != null ? req.getLogisticsFeeScheduleSetting() : store.getLogisticsFeeScheduleSetting());
					store.setPaymentMerchantCode(StringUtils.isNotEmpty(req.getPaymentMerchantCode()) ? req.getPaymentMerchantCode() : store.getPaymentMerchantCode());
					store.setPaymentSecret(StringUtils.isNotEmpty(req.getPaymentSecret()) ? req.getPaymentSecret() : store.getPaymentSecret());

					if (userObj instanceof PlatformUserEntity) {
						store.setBusinessModel(StringUtils.isNotEmpty(req.getBusinessModel()) ? req.getBusinessModel() : store.getBusinessModel());
						RegionEntity regionEntity  = null;
						if(null != req.getRegionId()) {
							 regionEntity = regionDao.get(req.getRegionId());
						}
						
						store.setRegion(null != regionEntity ? regionEntity : store.getRegion());
						if (store.getAutoAudit() == EntityContext.AUTO_AUDIT_ARTIFICAL && null != req.getAutoAudit()
								&& req.getAutoAudit() == EntityContext.AUTO_AUDIT_AUTO
								&& StringUtils.isNotEmpty(req.getAuditValidTime())) {
							try {
								store.setAuditValidTime(
										new Timestamp(DateUtil.parseLongFormat(req.getAuditValidTime()).getTime()));
								store.setAutoAudit(EntityContext.AUTO_AUDIT_AUTO);
								store.setAutoUpShelf(EntityContext.AUTO_UPSHELF_OPEN);
							} catch (ParseException e) {
								// TODO Auto-generated catch block
								res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
								res.setRetInfo("auditValidTime时间格式错误！！！");
								return res;
							}
						}
					}
	
					res.setStoreAddress(store.getStoreAddress());
					res.setStoreChargeName(store.getStoreChargeName());
					res.setStoreChargePhone(store.getStoreChargePhone());
					res.setServicePhone(store.getServicePhone());
					res.setStoreId(store.getId());
					res.setLogisticsFeeScheduleSetting(store.getLogisticsFeeScheduleSetting());
					res.setParentStoreId(null != store.getParentStore() ? store.getParentStore().getId() : null);
					res.setStoreMail(store.getStoreMail());
					res.setStoreName(store.getStoreName());
					res.setIsSelfSupport(store.getIsSelfSupport());
					res.setAutoAudit(store.getAutoAudit());
					res.setAutoUpShelf(store.getAutoUpShelf());
					res.setAuditValidTime(
							null != store.getAuditValidTime() ? DateUtil.formatLongFormat(store.getAuditValidTime())
									: null);
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
				}
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			}
		}else {
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
		}
		return res;
	}

	//@ResourcePermit
	@Override
	public IResponse deleteStore(StoreReq req, HttpServletRequest request) {
		// TODO Auto-generated method stub
		GenericResponse res = new GenericResponse();
		Object userObj = getPrincipal(false);
		if(null == userObj ) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		if(userObj instanceof PlatformUserEntity) {
			if (null != req.getStoreId()) {
				StoreEntity store = storeDao.get(req.getStoreId());
				if (null != store) {
					store.setState(EntityContext.RECORD_STATE_INVALID);
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
				}
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			}
		}
		return res;
	}

	@Override
	public IResponse getStoreListFlat(StoreReq req) {
		GetStoreListRes res = new GetStoreListRes();
		
		Object userObj = getPrincipal(false);
		if(null == userObj ) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
	    if(userObj instanceof PlatformUserEntity) {
			StringBuilder hql = new StringBuilder("select a from StoreEntity a where a.state="
					+ (null != req.getState() ? req.getState() : 1));
			List<StoreEntity> queryList = storeDao.query(hql.toString());

			for(StoreEntity entity : queryList) {
				StoreForm form = new StoreForm();
//				form.setStoreAddress(entity.getStoreAddress());
//				form.setStoreChargeName(entity.getStoreChargeName());
//				form.setStoreChargePhone(entity.getStoreChargePhone());
//				form.setServicePhone(entity.getServicePhone());
				form.setStoreId(entity.getId());
				form.setParentStoreId(null != entity.getParentStore() ? entity.getParentStore().getId() : null);
//				form.setStoreMail(entity.getStoreMail());
				form.setStoreName(entity.getStoreName());
//				form.setIsSelfSupport(entity.getIsSelfSupport());
				res.getStoreList().add(form);
			}
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			
		}
	    return res;
	}
	
	@Override
	public IResponse getStoreList(StoreReq req) {
		// TODO Auto-generated method stub
		GetStoreListRes res = new GetStoreListRes();
		Integer regionId = req.getRegionId();
		List<RegionEntity> regions = regionDao.getListByHql("from RegionEntity where id = " + regionId);
		
		if (null != req.getStoreId()) {
			StoreEntity store = storeDao.get(req.getStoreId());
			if (null != store && store.getState() == EntityContext.RECORD_STATE_VALID) {
				StoreForm storeForm = new StoreForm();
				storeForm = parseStoreTree(storeForm, store,regions);
				if(null != storeForm) {
					res.getStoreList().add(storeForm);
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
			}
		} else {
			Page<StoreEntity> page = new Page<StoreEntity>();
			page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
			page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
			page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

			
//			String regionIds = "";
//			if(regions.size() > 0 ) {
//				List<Integer> regionIdList = getAllSubRegions(regions);
//				for(Integer rid : regionIdList) {
//					regionIds += rid.toString() + ",";
//				}
//				regionIds = regionIds.substring(0, regionIds.length()-1);
//			}else if(null != regionId) {
//				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
//				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "region为null！");
//				return res;
//			}
			
//			StringBuilder hql = new StringBuilder("select a from StoreEntity a where a.state="
//					+ (null != req.getState() ? req.getState() : 1) + " and a.parentStore is null" + (null == req.getRegionId() ? "" : " and a.region.id in (" + regionIds + ")"));

			String hql = "select a from StoreEntity a where 1=1" + " and a.parentStore is null";

			if (null != req.getState()) {
				hql += " and a.state = " + req.getState();
			}

			page = storeDao.queryPage(page, hql);

			res.setTotalResult(page.getTotalCount());
			res.setPageSize(page.getPageSize());
			res.setCurrentPage(page.getCurrentPage());
			res.setTotal(page.getTotalPage());

			page.getResultList().forEach(o -> {
				if (o.getState() == EntityContext.RECORD_STATE_VALID) {
					StoreForm storeForm = new StoreForm();
					if(null != regionId) {
						storeForm = parseStoreTree(storeForm, o,regions);
					}else {
						storeForm = parseStoreTree(storeForm, o);
					}
					
					if(null != storeForm) {
						res.getStoreList().add(storeForm);
					}
				}
			});
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse getStoreInfo(StoreReq req) {
		// TODO Auto-generated method stub
		GetStoreInfoRes res = new GetStoreInfoRes();
		Object userObj = getPrincipal(false);
		if (userObj instanceof ShopUserEntity) {
			req.setStoreId(((ShopUserEntity) userObj).getStore().getId());
		}
		if (null != req.getStoreId()) {
			StoreEntity store = storeDao.get(req.getStoreId());
			if (null != store) {
				res.setStoreForm(parseStoreTree(new StoreForm(), store));
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	public IResponse getStoreProductList(GetSpecificationListReq req) {
		// TODO Auto-generated method stub
		GetProductSpecificationListRes res = new GetProductSpecificationListRes();
		// 获取登录用户信息
		List<String> marketableStatusList = null;
		if(req.getIsMarketableList() != null) {
			 marketableStatusList = CollectionUtils.asList(req.getIsMarketableList().split(","));
		}
		Object userObj = getPrincipal(false);
		//判断是否有企业购权限
		if((!isEnterpriseUser(userObj) &&  marketableStatusList != null  && 
				(marketableStatusList.contains(EntityContext.UP_SHELF_STATE_UP_FOR_COMPANY + "") 
						|| marketableStatusList.contains(EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL_AND_COMPANY + "")
						||marketableStatusList.contains(EntityContext.UP_SHELF_STATE_UP_FOR_ALL + "")))
				&& !(userObj instanceof PlatformUserEntity)) {
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO + "当前用户无企业购相关权限");
			return res;
		}
		//判断是否有积分商城权限
		if((!isPointsUser(userObj) &&  marketableStatusList != null  &&
				(marketableStatusList.contains(EntityContext.UP_SHELF_STATE_UP_FOR_POINTS + "")
						|| marketableStatusList.contains(EntityContext.UP_SHELF_STATE_UP_FOR_POINTS_AND_INDIVIDUAL + "")
						||marketableStatusList.contains(EntityContext.UP_SHELF_STATE_UP_FOR_ALL + "")))
			&& !(userObj instanceof PlatformUserEntity)) {
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO + "当前用户无积分购相关权限");
		}
		if(!isEnterpriseUser(userObj) && !isPointsUser(userObj) && StringUtils.isEmpty(req.getIsMarketableList())) {
			req.setIsMarketableList(EntityContext.UP_SHELF_STATE_DOWN + "," + EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL);
		}
		req.setIsMarketable(null); //后端统一采用isMarketableList字段来查询商品的上架状态,取消使用isMarketable来查询。
		//用户登录后，根据用户的角色，返回对应的商品列表
		if (null == userObj || (null != userObj && userObj instanceof MemberEntity)) {
			if (null != req.getStoreId()) {
				req.setAuditState(EntityContext.RECORD_AUDIT_PASS);
				//req.setIsMarketable(EntityContext.UP_SHELF_STATE_UP);
				req.setProductState(EntityContext.RECORD_STATE_VALID);
				req.setRegionCode(StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "1");
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				return res;
			}
			MemberEntity member = (MemberEntity) userObj;
			req.setIsMarketableList(member.getType() == EntityContext.ACCOUNT_TYPE_PUBLIC_ACCOUNT ? 
					"("+EntityContext.UP_SHELF_STATE_UP_FOR_COMPANY+","+EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL_AND_COMPANY +")" :
				"("+EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL+","+EntityContext.UP_SHELF_STATE_UP_FOR_POINTS+","+EntityContext.UP_SHELF_STATE_UP_FOR_POINTS_AND_INDIVIDUAL+","+EntityContext.UP_SHELF_STATE_UP_FOR_ALL+","+EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL_AND_COMPANY +
						")");
		}
		//商铺用户登录后，根据商铺，返回对应的商品列表
		else if (null != userObj && userObj instanceof ShopUserEntity) {
			StoreEntity store = ((ShopUserEntity) userObj).getStore();
			req.setStoreId(store.getId());
			req.setOrderStr("createTime|desc");
		}else if(null == userObj) {
			req.setIsMarketableList(EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL+","+EntityContext.UP_SHELF_STATE_UP_FOR_INDIVIDUAL_AND_COMPANY);
		}else {
			req.setIsMarketableList(StringUtils.isNotEmpty(req.getIsMarketableList()) ? "("+req.getIsMarketableList()+")" : (null!=req.getIsMarketable() ? req.getIsMarketable()+"" : ""));
		}
		
		HashMap<String, Object> hm = getSpecificationList(req, userObj);
		List<ProductSpecificationEntity> specificationList = (List<ProductSpecificationEntity>) hm
				.get("specificationList");

		res.setSpecificationList(parseProductSpecificationFormList(null, null, specificationList,
				Boolean.parseBoolean(shopContextInfo.getStoreAudit()),
				Boolean.parseBoolean(shopContextInfo.getStoreSpecifications()),
				Boolean.parseBoolean(shopContextInfo.getStoreTemplete()),
				Boolean.parseBoolean(shopContextInfo.getStoreSku()),req.getIsMarketableList()));
		res.setTotalResult(res.getSpecificationList().size() > 0 ? (Integer) hm.get("totalResult") : 0);
		res.setPageSize((Integer) hm.get("pageSize"));
		res.setCurrentPage((Integer) hm.get("currentPage"));
		res.setTotal((Integer) hm.get("total"));
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	public IResponse addShopUser(ShopUserReq req) {
		AddShopUserRes res = new AddShopUserRes();
//		ShopUserEntity curUser = (ShopUserEntity) getPrincipal(true);
//		AreaEntity area = curUser.getArea();
//		String areaCode = area.getAreaCode();
//		Integer addAreaId = req.getAreaId() == null ? curUser.getArea().getId() : req.getAreaId();
//		AreaEntity beingAddedUserArea = areaDao.get(addAreaId);
//		if(!beingAddedUserArea.getAreaCode().startsWith(areaCode)) {
//			res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
//			res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO+"运营区域超出范围！！！");
//			return res;
//		}
		if (StringUtils.isNotEmpty(req.getUserCode()) && StringUtils.isNotEmpty(req.getUserName())
				&& StringUtils.isNotEmpty(req.getRoleIdList())) {
			StringBuilder tempHql = new StringBuilder(
					"select a from ShopUserEntity a where ( a.userCode=? or a.userName=? ) and userState = "
							+ EntityContext.RECORD_STATE_VALID);
			List<ShopUserEntity> userList = new ArrayList<ShopUserEntity>();
			userList = shopUserDao.getListByHql(tempHql.toString(), req.getUserCode(), req.getUserName());
			if (userList.size() > 0) {
				res.setRet(ResponseContext.RES_DATA_DUPLICATE_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_DUPLICATE_INFO + "用户编码或用户名已存在！！！");
				return res;
			}
			ShopUserEntity user = new ShopUserEntity();
			user.setUserCode(req.getUserCode());
			user.setUserName(StringUtils.isNotEmpty(req.getUserName()) ? req.getUserName() : "");
			try {
				user.setUserPassword(
						StringUtils.isNotEmpty(req.getUserPassword()) ? DigestUtil.sm3Digest(req.getUserPassword())
								: DigestUtil.sm3Digest(DigestUtil.sm3Digest("123456")));
				user.setLastPwdModifyTime(new Timestamp(new Date().getTime()));
			} catch (UnsupportedEncodingException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			user.setUserState(EntityContext.RECORD_STATE_VALID);
			user.setName(req.getName());
			user.setPhone(req.getPhone());
			
			List<RoleEntity> curRoles = (List<RoleEntity>) AuthUtil.getRoleList();
			List<RoleEntity> curRoleList = new ArrayList<RoleEntity>();
			if (curRoles.size() > 0) {
				curRoles.forEach(o -> {
					curRoleList.add(roleDao.get(o.getId()));
				});
			}
			List<Integer> ids = getAllSubRoles(curRoleList);
			List<Integer> idsList = Arrays.asList(req.getRoleIdList().split(",")).stream().map(s -> Integer.parseInt(s)) // .map(Integer::valueOf)
					.collect(Collectors.toList());

			// 限制分配角色
			boolean isContain = ids.containsAll(idsList);
			// Object curUser = getPrincipal(false);
			// if (curUser instanceof ShopUserEntity && !isContain) {
			if (!isContain) {
				res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO + "部分角色无权关联！");
				return res;
			}

			String roleHql = "from RoleEntity where roleState = " + EntityContext.RECORD_STATE_VALID + " and id in ("
					+ req.getRoleIdList() + ")";
			List<RoleEntity> roleList = roleDao.getListByHql(roleHql);
			user.setRoleList(roleList);
			if (null != req.getStoreId()) {
				StoreEntity store = storeDao.get(req.getStoreId());
				if (null != store) {
					if (store.getState().intValue() == EntityContext.RECORD_STATE_INVALID.intValue()) {
						res.setRet(ResponseContext.RES_DATA_NULL_CODE);
						res.setRetInfo("所关联的店铺已被删除！");
						return res;
					}
					user.setStore(store);
					RegionEntity region = store.getRegion();
					ArrayList<RegionEntity> regionList = new ArrayList<RegionEntity>();
					regionList.add(region);
					user.setRegionList(regionList);
				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo("所关联的店铺不存在！");
					return res;
				}
			}
			// 关联region
			PlatformUserEntity curUser = (PlatformUserEntity) getPrincipal(true);
			List<Integer> curRegionIds = getAllSubRegions(curUser.getRegionList());
			Integer regionId = req.getRegionId();
			if (regionId != null) {
				if (curRegionIds.contains(regionId)) {
					RegionEntity region = regionDao.get(regionId);
					if (region.getState() == EntityContext.RECORD_STATE_INVALID) {
						res.setRet(ResponseContext.RES_PERM_BEYOND_AUTHORITY_CODE);
						res.setRetInfo("所关联区域无效！");
						return res;
					}
					List<RegionEntity> regionList = new ArrayList<RegionEntity>();
					regionList.add(region);
					user.setRegionList(regionList);
				} else {
					res.setRet(ResponseContext.RES_PERM_BEYOND_AUTHORITY_CODE);
					res.setRetInfo("无权关联该区域！");
					return res;
				}
			}

			Integer userId = (Integer) userDao.save(user);
			res.setUserId(userId);
			res.setUserCode(user.getUserCode());
			res.setUserName(user.getUserName());
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
}
