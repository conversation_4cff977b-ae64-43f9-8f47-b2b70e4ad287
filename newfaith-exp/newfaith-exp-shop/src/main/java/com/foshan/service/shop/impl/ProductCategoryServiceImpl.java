package com.foshan.service.shop.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.dao.generic.Page;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.RegionEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.shop.MemberEntity;
import com.foshan.entity.shop.ProductCategoryEntity;
import com.foshan.entity.shop.ProductSpecificationEntity;
import com.foshan.entity.shop.ShopUserEntity;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.shop.ProductCategoryForm;
import com.foshan.form.shop.request.GetSpecificationListReq;
import com.foshan.form.shop.request.ProductCategoryReq;
import com.foshan.form.shop.response.category.AddProductCategoryRes;
import com.foshan.form.shop.response.category.GetProductCategoryInfoRes;
import com.foshan.form.shop.response.category.GetProductCategoryListRes;
import com.foshan.form.shop.response.category.ModifyProductCategoryRes;
import com.foshan.form.shop.response.specification.GetProductSpecificationListRes;
import com.foshan.service.shop.IProductCategoryService;
import com.foshan.util.PinYinUtil;
import com.foshan.util.ShopContextInfo;
import com.hazelcast.spring.cache.HazelcastCacheManager;
import org.springframework.cache.Cache;
//import net.sf.ehcache.Cache;
import net.sf.ehcache.CacheManager;
import net.sf.ehcache.Element;

@Transactional
@Service("productCategoryService")
public class ProductCategoryServiceImpl extends GenericShopService implements IProductCategoryService {
	private final static Logger logger = LoggerFactory.getLogger(ProductCategoryServiceImpl.class);
	@Resource
	private HazelcastCacheManager hazelcastCacheManager;

	@Override
	public IResponse addProductCategory(ProductCategoryReq req) {
		// TODO Auto-generated method stub
		AddProductCategoryRes res = new AddProductCategoryRes();
		if (StringUtils.isNotEmpty(req.getCategoryName())) {
			ProductCategoryEntity category = new ProductCategoryEntity();
			ProductCategoryForm categoryForm = new ProductCategoryForm();
			BeanUtils.copyProperties(req, category);
			BeanUtils.copyProperties(req, categoryForm);

			category.setCategoryKeywords(
					StringUtils.isNotEmpty(req.getCategoryName()) ? PinYinUtil.parseSimplePY(req.getCategoryName())
							: "");
			category.setState(EntityContext.RECORD_STATE_VALID);
			if (null != req.getAssetId()) {

				AssetEntity categoryImage = assetDao.get(req.getAssetId());
				if (null != categoryImage) {
					category.setCategoryImage(categoryImage);
					categoryForm.setCategoryImageFile(categoryImage.getImageFile());
					categoryForm.setCategorySmallImageFile(categoryImage.getSmallImageFile());
				}
			}
			if (null != req.getParentCategoryId()) {
				ProductCategoryEntity parentCategory = productCategoryDao.get(req.getParentCategoryId());
				if (null != parentCategory) {
					category.setParentCategory(parentCategory);
					category.setCategoryLevel(parentCategory.getCategoryLevel() + 1);
					category.setCategoryPath(parentCategory.getCategoryPath() + "|" + req.getCategoryName());
				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
					return res;
				}
			} else {
				category.setCategoryLevel(1);
				category.setCategoryPath(req.getCategoryName());
			}

			if (req.getSpecifications().size() > 0) {
				ObjectMapper mapper = new ObjectMapper();
				try {
					category.setSpecifications(
							mapper.writeValueAsString(req.getSpecifications()).replaceAll("null", "\"\""));
				} catch (Exception ex) {
					ex.printStackTrace();
					logger.error(ex.getLocalizedMessage());
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("参数格式错误！");
					return res;
				}
			}
			productCategoryDao.save(category);
			categoryForm.setCategoryId(category.getId());
			BeanUtils.copyProperties(category, categoryForm);
			if (null != category.getCategoryImage()) {
			}
			if (category.getCategoryLevel() == 1) {
				categoryForm.setLevel1Id(category.getId());
			} else if (category.getCategoryLevel() == 2) {
				categoryForm.setLevel2Id(category.getId());
				categoryForm.setLevel1Id(category.getParentCategory().getId());
				categoryForm.setParentCategoryId(categoryForm.getLevel1Id());
			} else {
				categoryForm.setLevel3Id(category.getId());
				categoryForm.setLevel2Id(category.getParentCategory().getId());
				categoryForm.setLevel1Id(category.getParentCategory().getParentCategory().getId());
				categoryForm.setParentCategoryId(categoryForm.getLevel2Id());
			}
			res.setProductCategory(categoryForm);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			
			CacheManager ehCacheManager = null;//cacheManager.getCacheManager();
//			Cache cache = ehCacheManager.getCache("productCategoryCache");
//			cache.removeAll();
			getProductCategoryList(new ProductCategoryReq());
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	public IResponse modifyProductCategory(ProductCategoryReq req) {
		// TODO Auto-generated method stub
		ModifyProductCategoryRes res = new ModifyProductCategoryRes();
		ProductCategoryForm categoryForm = new ProductCategoryForm();
		ObjectMapper mapper = new ObjectMapper();
		if (null != req.getCategoryId()) {
			ProductCategoryEntity category = productCategoryDao.get(req.getCategoryId());
			if (null != category) {
				String oldCategoryName = category.getCategoryName();
				if (null != req.getParentCategoryId()) {
					ProductCategoryEntity parentCategory = productCategoryDao.get(req.getParentCategoryId());
					if (null != parentCategory) {
						if ((null != category.getParentCategory()
								&& req.getParentCategoryId() != category.getParentCategory().getId())
								|| null == category.getParentCategory()) {
							category.setParentCategory(parentCategory);
							category.setCategoryPath(parentCategory.getCategoryPath() + "|"
									+ (StringUtils.isNotEmpty(req.getCategoryName()) ? req.getCategoryName()
											: category.getCategoryName()));
							category.setCategoryLevel(parentCategory.getCategoryLevel() + 1);
						}
					} else {
						res.setRet(ResponseContext.RES_DATA_NULL_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
						return res;
					}
				} else {
					if (null != category.getParentCategory()) {
						category.setParentCategory(null);
						category.setCategoryLevel(1);
						category.setCategoryPath(StringUtils.isNotEmpty(req.getCategoryName()) ? req.getCategoryName()
								: category.getCategoryName());

					}
				}

				if ((null != req.getAssetId() && null == category.getCategoryImage())
						|| (null != req.getAssetId() && null != category.getCategoryImage()
								&& req.getAssetId() != category.getCategoryImage().getId())) {
					AssetEntity categoryImage = assetDao.get(req.getAssetId());
					if (null != categoryImage) {
						category.setCategoryImage(categoryImage);
						categoryForm.setCategoryImageFile(categoryImage.getImageFile());
						categoryForm.setCategorySmallImageFile(categoryImage.getSmallImageFile());
					}
				}
				category.setCategoryInfo(StringUtils.isNotEmpty(req.getCategoryInfo()) ? req.getCategoryInfo()
						: category.getCategoryInfo());
				category.setCategoryPath(StringUtils.isNotEmpty(req.getCategoryName())
						? category.getCategoryPath().replaceAll(oldCategoryName, req.getCategoryName())
						: category.getCategoryPath());
				category.setCategoryName(
						StringUtils.isNotEmpty(req.getCategoryName()) ? req.getCategoryName() : oldCategoryName);
				category.setCategoryKeywords(PinYinUtil.parseSimplePY(
						StringUtils.isNotEmpty(req.getCategoryName()) ? req.getCategoryName() : oldCategoryName));
				category.setCategoryTitle(StringUtils.isNotEmpty(req.getCategoryTitle()) ? req.getCategoryTitle()
						: category.getCategoryTitle());
				category.setOrders(null != req.getOrders() ? req.getOrders() : category.getOrders());
				category.setTaxRate(
						StringUtils.isNotEmpty(req.getTaxRate()) ? req.getTaxRate() : category.getTaxRate());
//				if (null != req.getAssetId() && null != category.getCategoryImage()
//						&& category.getCategoryImage().getId() != req.getAssetId()) {
//					AssetEntity categoryImage = assetDao.get(req.getAssetId());
//					category.setCategoryImage(categoryImage);
//					categoryForm.setCategoryImageFile(null != categoryImage ? categoryImage.getImageFile() : null);
//				}
				// 当前类别状态修改后，需要同步修改相关的类别状态；
				if (null != req.getState() && req.getState() == EntityContext.RECORD_STATE_INVALID
						&& category.getState() == EntityContext.RECORD_STATE_VALID) {
					category.getSubCategoryList().forEach(o -> {
						o.setState(req.getState());
						o.getSubCategoryList().forEach(b -> {
							b.setState(req.getState());
						});
					});
				} else if (null != req.getState() && req.getState() == EntityContext.RECORD_STATE_VALID
						&& category.getState() == EntityContext.RECORD_STATE_INVALID
						&& category.getCategoryLevel() > 1) {
					if (category.getCategoryLevel() == 3
							&& category.getParentCategory().getState() == EntityContext.RECORD_STATE_INVALID) {

						if (category.getParentCategory().getParentCategory()
								.getState() == EntityContext.RECORD_STATE_INVALID) {
							ProductCategoryEntity grand = category.getParentCategory().getParentCategory();
							grand.setState(EntityContext.RECORD_STATE_VALID);
//							productCategoryDao.save(grand);
						}
						ProductCategoryEntity parent = category.getParentCategory();
						parent.setState(EntityContext.RECORD_STATE_VALID);
//						productCategoryDao.save(parent);

					} else if (category.getCategoryLevel() == 2
							&& category.getParentCategory().getState() == EntityContext.RECORD_STATE_INVALID) {
						ProductCategoryEntity parentCategory = category.getParentCategory();
						parentCategory.setState(EntityContext.RECORD_STATE_VALID);
//						productCategoryDao.save(parentCategory);
					}
				}
				category.setState(null != req.getState() ? req.getState() : category.getState());
				// 修改当前类别下所有子类别的路径资料
				if (StringUtils.isNotEmpty(req.getCategoryName())) {
					category.getSubCategoryList().forEach(o -> {
						o.setCategoryPath(o.getCategoryPath().replaceAll(oldCategoryName, req.getCategoryName()));
						o.getSubCategoryList().forEach(b -> {
							b.setCategoryPath(b.getCategoryPath().replaceAll(oldCategoryName, req.getCategoryName()));
						});
					});
				}

				// 修改产品类别属性参数
				if (req.getSpecifications().size() > 0) {
					try {
						category.setSpecifications(
								mapper.writeValueAsString(req.getSpecifications()).replaceAll("null", "\"\""));
					} catch (Exception ex) {
						ex.printStackTrace();
						logger.error(ex.getLocalizedMessage());
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo("参数格式错误！");
						return res;
					}
				} else {
					if (StringUtils.isNotEmpty(category.getSpecifications())) {
						category.setSpecifications(null);
					}
				}

				BeanUtils.copyProperties(category, categoryForm);

				try {
					categoryForm.setSpecifications(mapper.readValue(category.getSpecifications(), ArrayList.class));
				} catch (Exception ex) {
					logger.error("读取参数格式错误：" + ex.getMessage());
				}
				if (category.getCategoryLevel() == 1) {
					categoryForm.setLevel1Id(category.getId());
				} else if (category.getCategoryLevel() == 2) {
					categoryForm.setLevel2Id(category.getId());
					categoryForm.setLevel1Id(category.getParentCategory().getId());
					categoryForm.setParentCategoryId(categoryForm.getLevel1Id());
				} else {
					categoryForm.setLevel3Id(category.getId());
					categoryForm.setLevel2Id(category.getParentCategory().getId());
					categoryForm.setLevel1Id(category.getParentCategory().getParentCategory().getId());
					categoryForm.setParentCategoryId(categoryForm.getLevel2Id());
				}
				res.setProductCategory(categoryForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				CacheManager ehCacheManager = null;//cacheManager.getCacheManager();
//				Cache cache = ehCacheManager.getCache("productCategoryCache");
//				cache.removeAll();
				getProductCategoryList(new ProductCategoryReq());
			} else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse deleteProductCategory(ProductCategoryReq req) {
		// TODO Auto-generated method stub
		GenericResponse res = new GenericResponse();
		if (null != req.getCategoryId()) {
			ProductCategoryEntity category = productCategoryDao.get(req.getCategoryId());
			if (null != category) {
				category.setState(EntityContext.RECORD_STATE_INVALID);

				// 同步刪除改类别下的子类别
				category.getSubCategoryList().forEach(o -> {
					o.getSubCategoryList().forEach(b -> {
						b.setState(EntityContext.RECORD_STATE_INVALID);
					});
					o.setState(EntityContext.RECORD_STATE_INVALID);
				});
				CacheManager ehCacheManager = null;//cacheManager.getCacheManager();
//				Cache cache = ehCacheManager.getCache("productCategoryCache");
//				cache.removeAll();
				getProductCategoryList(new ProductCategoryReq());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse getProductCategoryList(ProductCategoryReq req) {
		// TODO Auto-generated method stub
		GetProductCategoryListRes res = new GetProductCategoryListRes();
		
		Cache cache =  hazelcastCacheManager.getCache("productCategoryCache");
		
		
		//cache.put(phone,map);
		
//		CacheManager ehCacheManager = null;//cacheManager.getCacheManager();
//		Cache cache = ehCacheManager.getCache("productCategoryCache");
		if (null != req.getCategoryId()) {
			//Element element = cache.get(req.getCategoryId());
			ProductCategoryForm categoryFormCache =  cache.get(req.getCategoryId(),ProductCategoryForm.class); 
			if (null == categoryFormCache) {
				ProductCategoryEntity category = productCategoryDao.get(req.getCategoryId());
				if (null != category && category.getState() == EntityContext.RECORD_STATE_VALID) {
					ProductCategoryForm categoryForm = new ProductCategoryForm();
					categoryForm = parseCategoryTree(categoryForm, category);
					res.getSubCategoryList().add(categoryForm);
					//element = new Element(req.getCategoryId(), categoryForm);
					//cache.put(element);
					cache.put(req.getCategoryId(),categoryForm);
				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
				}
			}else {
				//ProductCategoryForm categoryForm  =  (ProductCategoryForm) element.getObjectValue();
				res.getSubCategoryList().add(categoryFormCache);
			}

		} else {
			//Element element = cache.get("allProductCategory");
			List<ProductCategoryForm>  subCategoryList =  cache.get("allProductCategory",List.class); 
			if (null == subCategoryList) {
				List<ProductCategoryEntity> categoryList = productCategoryDao.getListByHql(
						"select a from ProductCategoryEntity a where a.parentCategory is null and a.state=1 order by a.orders");
				categoryList.forEach(o -> {
					if (o.getState() == EntityContext.RECORD_STATE_VALID) {
						ProductCategoryForm categoryForm = new ProductCategoryForm();
						categoryForm = parseCategoryTree(categoryForm, o);
						res.getSubCategoryList().add(categoryForm);
					}
				});

				cache.put("allProductCategory",res.getSubCategoryList());
			}else {
				res.getSubCategoryList().addAll(subCategoryList);
			}
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	/*
	 * 处理类别树状结构的工具类
	 */
	@SuppressWarnings("unchecked")
	private ProductCategoryForm parseCategoryTree(ProductCategoryForm categoryForm, ProductCategoryEntity category) {
		ObjectMapper mapper = new ObjectMapper();
		categoryForm.setCategoryId(category.getId());
		categoryForm.setCategoryInfo(category.getCategoryInfo());
		categoryForm.setCategoryKeywords(category.getCategoryKeywords());
		categoryForm.setCategoryName(category.getCategoryName());
		categoryForm.setCategoryTitle(category.getCategoryTitle());
		categoryForm.setTaxRate(category.getTaxRate());
		categoryForm.setCategoryPath(category.getCategoryPath());
		categoryForm.setCategoryLevel(category.getCategoryLevel());
		categoryForm.setOrders(category.getOrders());
		if (category.getCategoryLevel() == 1) {
			categoryForm.setLevel1Id(category.getId());
		} else if (category.getCategoryLevel() == 2) {
			categoryForm.setLevel2Id(category.getId());
			categoryForm.setLevel1Id(category.getParentCategory().getId());
			categoryForm.setParentCategoryId(categoryForm.getLevel1Id());
		} else {
			categoryForm.setLevel3Id(category.getId());
			categoryForm.setLevel2Id(category.getParentCategory().getId());
			categoryForm.setLevel1Id(category.getParentCategory().getParentCategory().getId());
			categoryForm.setParentCategoryId(categoryForm.getLevel2Id());
		}
		categoryForm.setCategoryImageFile(
				null != category.getCategoryImage() ? category.getCategoryImage().getImageFile() : null);
		try {
			if (StringUtils.isNotEmpty(category.getSpecifications())) {
				categoryForm.setSpecifications(mapper.readValue(category.getSpecifications(), ArrayList.class));
			}
		} catch (Exception ex) {
			logger.error("产品规格格式转换错误！！！" + ex.getMessage());
		}
	
		//子分类按getOrders升序排序
		Collections.sort(category.getSubCategoryList(),new Comparator<ProductCategoryEntity>(){
			public int compare(ProductCategoryEntity o1, ProductCategoryEntity o2) {
				if(null==o1.getOrders() || null==o2.getOrders()) {
					return -1;
				}
				if(o1.getOrders()<o2.getOrders()){
					return -1;
				}
				if(o1.getOrders()==o2.getOrders())
					return 0;
				return 1;
			}
		});
		Integer size = category.getSubCategoryList().size();
		while (size > 0) {
			for (ProductCategoryEntity o : category.getSubCategoryList()) {
				if (o.getState() == EntityContext.RECORD_STATE_VALID) {
					ProductCategoryForm subCategoryForm = new ProductCategoryForm();
					categoryForm.getSubList().add(parseCategoryTree(subCategoryForm, o));
				}
				size--;
			}
		}
		
		return categoryForm;
	}

	@Override
	public IResponse getProductCategoryInfo(ProductCategoryReq req) {
		// TODO Auto-generated method stub
		GetProductCategoryInfoRes res = new GetProductCategoryInfoRes();
		if (null != req.getCategoryId()) {
			ProductCategoryEntity category = productCategoryDao.get(req.getCategoryId());
			if (null != category && category.getState() == EntityContext.RECORD_STATE_VALID) {
				ProductCategoryForm categoryForm = new ProductCategoryForm();
				categoryForm = parseCategoryTree(categoryForm, category);
				res.setProductCategory(categoryForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	public IResponse getProductCategoryProductList(GetSpecificationListReq req) {

		GetProductSpecificationListRes res = new GetProductSpecificationListRes();
		// 获取登录用户信息
		Object userObj = getPrincipal(false);

		if (null == userObj || (null != userObj && userObj instanceof MemberEntity )) {
			MemberEntity member = (MemberEntity)userObj;
			if(member.getType() == EntityContext.ACCOUNT_TYPE_PRIVATE_ACCOUNT) {
				req.setAuditState(EntityContext.RECORD_AUDIT_PASS);
				//req.setIsMarketable(EntityContext.UP_SHELF_STATE_UP);
				req.setState(EntityContext.RECORD_STATE_VALID);
				req.setRegionCode(StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "1");
			}else {
				req.setRegionCode(member.getRegionCode());
				req.setIsCompanyBuy(EntityContext.IS_COMPANY_BUY);
			}

		} else if (null != userObj && userObj instanceof ShopUserEntity) {
			RegionEntity region = regionDao.get(((ShopUserEntity) userObj).getStore().getRegionId());
			req.setRegionCode(null != region ? region.getRegionCode() : "1");
		}
		HashMap<String, Object> hm = getSpecificationList(req, userObj);
		List<ProductSpecificationEntity> specificationList = (List<ProductSpecificationEntity>) hm
				.get("specificationList");

		res.setSpecificationList(parseProductSpecificationFormList(null, null, specificationList,
				Boolean.parseBoolean(shopContextInfo.getCategoryAudit()),
				Boolean.parseBoolean(shopContextInfo.getCategorySpecifications()),
				Boolean.parseBoolean(shopContextInfo.getCategoryTemplete()),
				Boolean.parseBoolean(shopContextInfo.getCategorySku()),null));
		res.setTotalResult(res.getSpecificationList().size() > 0 ? (Integer) hm.get("totalResult") : 0);
		res.setPageSize((Integer) hm.get("pageSize"));
		res.setCurrentPage((Integer) hm.get("currentPage"));
		res.setTotal((Integer) hm.get("total"));
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

}
