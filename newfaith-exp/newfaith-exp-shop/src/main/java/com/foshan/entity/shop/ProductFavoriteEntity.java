package com.foshan.entity.shop;


import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor	
@Entity
@Table(name = "t_product_favorite")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class ProductFavoriteEntity extends Shop {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -7187525060695271268L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@ManyToOne(targetEntity = MemberEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	private MemberEntity member;
	@ManyToOne(targetEntity = ProductEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "productId", referencedColumnName = "id")
	private ProductEntity product;
	
	public ProductFavoriteEntity(Timestamp createTime, Timestamp lastModifyTime, Integer version, Integer state) {
		super(createTime, lastModifyTime, version, state);
		// TODO Auto-generated constructor stub
	}

	public ProductFavoriteEntity(Integer id,MemberEntity member, ProductEntity product) {
		super();
		this.id = id;
		this.member = member;
		this.product = product;
	}
	
	
}
