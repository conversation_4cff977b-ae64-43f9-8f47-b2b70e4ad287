package com.foshan.entity.shop;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name="t_phone_location")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class PhoneLocationEntity {
    /**
     * 归属地信息
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(columnDefinition = "varchar(20) comment '号码'")
    private String number;
    @Column(columnDefinition = "varchar(20) comment '号段'")
    private String num;
    @Column(columnDefinition = "varchar(20) comment '运营商'")
    private String isp;
    @Column(columnDefinition = "varchar(20) comment '省'")
    private String province;
    @Column(columnDefinition = "varchar(20) comment '市'")
    private String city;
    @Column(columnDefinition = "varchar(20) comment '区号'")
    private String cityCode;
    @Column(columnDefinition = "varchar(20) comment '邮编'")
    private String zipCode;
    @Column(columnDefinition = "varchar(20) comment '经度'")
    private String longitude;
    @Column(columnDefinition = "varchar(20) comment '纬度'")
    private String latitude;

    public PhoneLocationEntity(String number, String num, String isp, String province, String city, String cityCode, String zipCode, String longitude, String latitude) {
        this.number = number;
        this.num = num;
        this.isp = isp;
        this.province = province;
        this.city = city;
        this.cityCode = cityCode;
        this.zipCode = zipCode;
        this.longitude = longitude;
        this.latitude = latitude;
    }

   
}

