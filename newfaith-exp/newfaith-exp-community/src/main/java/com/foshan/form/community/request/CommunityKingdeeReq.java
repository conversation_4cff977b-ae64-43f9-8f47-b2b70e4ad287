package com.foshan.form.community.request;



import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "金蝶对账(CommunityKingdeeReq)")
public class CommunityKingdeeReq extends BasePageRequest {
	/**
	* 
	*/
	private static final long serialVersionUID = -5856818473743631059L;
	@ApiModelProperty(value = "单据数据开始时间")
	private String startDate;
	@ApiModelProperty(value = "单据数据结束时间")
	private String endDate;
	@ApiModelProperty(value = "是否自动导入 0--手动 1--自动")
	private Integer importFlag=0;
	@ApiModelProperty(value = "账单导入状态  0--导入 1--成功 2--失败")
	private Integer importState;
	@ApiModelProperty(value = "账单类型：应收单、收据、基础数据、应收单减免")
	private String fileType;
	@ApiModelProperty(value = "导入操作开始时间")
	private String importStartDate;
	@ApiModelProperty(value = "导入操作结束时间")
	private String importEndDate;
	@ApiModelProperty(value = "导入文件名")
	private String fileName;
	@ApiModelProperty(value = "金蝶入账ID")
	private Integer kingdeeId;
	@ApiModelProperty(value = "刷新标识 0--默认 1--重新抓取并刷新缓存")
	private Integer refreshFlag=0;
	
}
