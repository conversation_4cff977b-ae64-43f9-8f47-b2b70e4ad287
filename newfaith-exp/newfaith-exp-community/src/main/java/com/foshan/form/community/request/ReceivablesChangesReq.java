package com.foshan.form.community.request;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "收款统计请求参数(ReceiptStatisticsReq)")
public class ReceivablesChangesReq extends BasePageRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = -7606904381464959269L;
	@ApiModelProperty(value = "楼盘id列表")
	private Integer[] districtIds;
	@ApiModelProperty(value = "应收开始日期")
	private String receivablesStartDate;
	@ApiModelProperty(value = "应收结束日期")
	private String receivablesEndDate;
	@ApiModelProperty(value = "减免开始日期")
	private String changeStartDate;
	@ApiModelProperty(value = "减免结束日期")
	private String changeEndDate;
	@ApiModelProperty(value = "刷新标识,默认为0")
	private Integer refreshFlag=0;
}
