package com.foshan.form.community.request;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.persistence.Column;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="事件请求参数(CommunityEventsReq)")
public class CommunityEventsReq extends BasePageRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = -6508599009376573592L;
	@ApiModelProperty(value = "事件ID",example="1")
	private Integer eventsId;
	@ApiModelProperty(value = "创建时间")
	private String createTime;
	@ApiModelProperty(value = "修改时间")
	private String lastModifyTime;
	@ApiModelProperty(value = "审核意见")
	private String auditOpinion;
	@ApiModelProperty(value = "说明")
	private String centent;
	@ApiModelProperty(value = "申请详情")
	private List<Object> itemsdetail;
	@ApiModelProperty(value = "标题")
	private String title;
	@ApiModelProperty(value = "状态：0初始化、1待审核、2审核通过（待派单）、3审核不通过、4已派单、5已完成、6已撤销、7待支付费用、8进行中、9装修完成、10验收中、11验收不通过、12验收通过、13申请退款",example="1")
	private Integer eventState;
	@ApiModelProperty(value = "状态：0初始化、1待审核、2审核通过（待派单）、3审核不通过、4已派单、5已完成、6已撤销、7待支付费用、8进行中、9装修完成、10提交验收申请、11验收不通过、12验收通过、13申请退款")
	private String eventStateList;
	@ApiModelProperty(value = "状态 0--无效数据  1--有效数据",example="1")
	private Integer state;
	@ApiModelProperty(value = "类型：0普通类型、1装修申请",example="1")
	private Integer eventType;
	@ApiModelProperty(value = "事件类型ID",example="1")
	private Integer eventCategoryId;
	@ApiModelProperty(value = "事件类型ID,多个以英文逗号隔开")
	private String eventCategoryIdList;
	@ApiModelProperty(value = "资产ID",example="1")
	private Integer propertyId;
	@ApiModelProperty(value = "小区ID",example="1")
	private Integer districtId;
	@ApiModelProperty(value = "会员ID",example="1")
	private Integer memberId;
	@ApiModelProperty(value = "处理人ID",example="1")
	private Integer handlerId;
	@ApiModelProperty(value = "创建时间")
	private String Time;
	@ApiModelProperty(value = "开始时间")
	private String startTime;
	@ApiModelProperty(value = "结束时间")
	private String endTime;
	@ApiModelProperty(value = "状态 0--无效数据  1--有效数据",example="1")
	private Integer eventCategoryState;
    @ApiModelProperty(value = "订单编号")
    private String orderCode;
	@ApiModelProperty(value = "发送状态:0未发送 1已发送成功 2发送失败",example="1")
	private Integer sendstatus;
	@ApiModelProperty(value = "云之家请求参数")
	private String parameterReq;
	@ApiModelProperty(value = "参数")
	private String widgetValue;
	@ApiModelProperty(value = "项目ID",example="1")
	private String decorationItemsId;
	@ApiModelProperty(value = "部门 0：工程部；1：客服；2：环境；3：部经办；",example="1")
	private Integer department;
    @ApiModelProperty(value = "验收详情")
    private String checkAcceptItems;
	@ApiModelProperty(value = "部门审核状态 2：审核通过，3：审核不通过（总经办审核工程客服环境不通过）；4:总经办审核工程部不通过；5：总经办审核客服不通过；6：总经办审核环境部不通过；"
			+ "7：总经办审核工程客服不通过；8：总经办审核工程环境不通过；9：总经办审核客服环境不通过；",example="1")
	private Integer departmentAuditState;
	@ApiModelProperty(value = "验收时间")
	private String acceptanceTime;
	@ApiModelProperty(value = "事件ID")
	private String eventsIdList;
	@ApiModelProperty(value = "退款方式:0、抵扣物业费 1、现金退回 2、银行卡",example="1")
	private Integer refundMethod;
	@ApiModelProperty(value = "银行号码")
	private String bankAccount;
	@ApiModelProperty(value = "银行名称")
	private String bankName;
	@ApiModelProperty(value = "开户人")
	private String accountName;
	@ApiModelProperty(value = "收据图片ID")
	private String receiptImageIds;
	@ApiModelProperty(value = "身份证图片ID")
	private String idCardImageIds;
	@ApiModelProperty(value = "银行图片ID")
	private String bankImageIds;
    @ApiModelProperty(value = "附件ID")
    private String attachmentIdList;
    @ApiModelProperty(value = "小区ID")
    private String districtIdList;
    @ApiModelProperty(value = "房产类型")
    private String estateTypeList;
    @ApiModelProperty(value = "装修项目")
    private Integer decorationItemId;
    @ApiModelProperty(value = "资料审核人")
    private String informationAuditor;
    @ApiModelProperty(value = "资料审核时间")
    private String informationAuditionTime;
    @ApiModelProperty(value = "审核级别")
    private String auditLevel;
    @ApiModelProperty(value = "审核内容")
    private String auditContent;
	@ApiModelProperty(value = "图片ID")
	private Integer imageId;
	@ApiModelProperty(value = "存在押金:0、无押金；1、押金未退;2:押金已退；" ,example="1")
	private Integer haveCashPledge;
	@ApiModelProperty(value = "押金的收费项目；数据格式为：\"payItemsList\":[{\"payItemsId\":\"70\",\"price\":\"10\",\"isEachCharge\":\"1\"},{\"payItemsId\":\"143\",\"price\":\"2000\",\"isEachCharge\":\"0\"}]"
			+ " 其中isEachCharge为必交，如果为1，不管之前的押金有没有退，都要交；若为0之前押金没退，则本次不交")
	private List<Map<String,String>> payItemsList = new ArrayList<>();
    
	
}
