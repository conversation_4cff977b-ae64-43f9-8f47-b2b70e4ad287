package com.foshan.form.community.request;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="预设策略请求参数(CommunityReservationStrategyReq)")
public class CommunityReservationStrategyReq extends BasePageRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = -8770590668592077450L;
	@ApiModelProperty(value = "预策略ID", example = "1")
	private Integer strategyId;
	@ApiModelProperty(value = "策略名称")
	private String strategyName;
	@ApiModelProperty(value = "1法定节假日 2工作日 2=0不限", example = "1")
	protected Integer periodType;	

}
