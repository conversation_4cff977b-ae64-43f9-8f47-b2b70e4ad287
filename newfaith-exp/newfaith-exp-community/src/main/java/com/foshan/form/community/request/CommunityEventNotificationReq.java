package com.foshan.form.community.request;


import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="事件通知请求参数(CommunityEventNotificationReq)")
public class CommunityEventNotificationReq extends BasePageRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = 8009835272361801671L;
	@ApiModelProperty(value = "通知ID",example="1")
	private Integer notificationId;
	@ApiModelProperty(value = "事件ID",example="1")
	private Integer eventsId;
	@ApiModelProperty(value = "分类：0：暂停施工通知书、1：安全告知函、2：复工通知书、3：违规整改告知函",example="1")
	private Integer category;
	@ApiModelProperty(value = "文件ID",example="1")
	private Integer fileId;
	@ApiModelProperty(value = "提交人")
	private String submitter;
	@ApiModelProperty(value = "标题")
	private String title;
	@ApiModelProperty(value = "订单编号")
	private String orderCode;
	
	
}
