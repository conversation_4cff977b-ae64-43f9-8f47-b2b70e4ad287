package com.foshan.form.community.request;

import com.foshan.form.community.RealEstateLeaseForm;
import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "发票请求参数(CommunityInvoiceReq)")
public class CommunityInvoiceReq extends BasePageRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = -7265461005579019113L;
	@ApiModelProperty(value = "发票Id",example="1")
	private Integer invoiceId;
	@ApiModelProperty(value = "收据Id",example="1")
	private Integer receiptId;
	@ApiModelProperty(value = "实收Id")
	private String receiptReceivablesIdList;
	@ApiModelProperty(value = "开票类型 0-蓝字发票，1-开红字发票,2-红字发票申请,3-撤消红票申请",example="1")
	private Integer invoiceType;
	@ApiModelProperty(value = "红冲原因 01:开票有误;02：销货退回；03:服务终止;04:销售折让")
	private String redDashedReason;
	@ApiModelProperty(value = "xml生成模式 0：只生成一个XML；1：根据税率生成不同XML；",example="1")
	private Integer generationMode;
	@ApiModelProperty(value = "备注")
	private String comment;
	@ApiModelProperty(value = "版本")
	private String version;
	@ApiModelProperty(value = "")
	private String spbmbbh;
	@ApiModelProperty(value = "")
	private String hsbz;
	@ApiModelProperty(value = "")
	private String sgbz;
	@ApiModelProperty(value = "收款人")
	private String payee;
	@ApiModelProperty(value = "复核人")
	private String checker;
	@ApiModelProperty(value = "购方邮箱")
	private String buyerEmail;
	@ApiModelProperty(value = "购方手机号")
	private String buyerMobile;
	@ApiModelProperty(value = "发票流水号")
	private String invoiceSn;
	@ApiModelProperty(value = "操作状态 0 不成功，1成功，2正在开（操作类型为 0,1的时候用）'", example = "1")
	private Integer invoiceState;
	@ApiModelProperty(value = "发票类型代码:81:数电专票;82:数电普票;85:纸质专票(数电)86:纸质普票(数电)")
	private String invoiceKindCode;
	@ApiModelProperty(value = "短信验证码")
	private String verifyCode;
	@ApiModelProperty(value = "不动产租、售服务")
	private RealEstateLeaseForm realEstateLease;
}
