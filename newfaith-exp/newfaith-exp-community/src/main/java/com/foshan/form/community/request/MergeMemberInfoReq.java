package com.foshan.form.community.request;


import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.StringJoiner;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="请求参数(CommunityMemberReq)")
public class MergeMemberInfoReq extends BasePageRequest  {

	/**
	 * 整合社区会员信息
	 */
	private static final long serialVersionUID = 4898081196832088197L;
	@ApiModelProperty(value = "被整合的会员Id",example="1")
	private Integer sourceMemberId;
	@ApiModelProperty(value = "整合到目标会员Id",example="1")
	private Integer targetMemberId;
}
