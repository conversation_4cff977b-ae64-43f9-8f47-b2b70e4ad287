package com.foshan.form.community.request;

import com.foshan.form.request.BaseRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "公式模版请求参数(CommunityEventsReq)")
public class CommunityFormulaTempleteReq extends BaseRequest {
	/**
	* 
	*/
	private static final long serialVersionUID = -3807863077997335753L;
	@ApiModelProperty(value = "公式模版Id", example = "1")
	private Integer templeteId;
	@ApiModelProperty(value = "公式模版名称")
	private String templeteName;
	@ApiModelProperty(value = "公式模版内容")
	private String templeteInfo;
	@ApiModelProperty(value = "公式动态参数")
	private String dynamicParameter;
}
