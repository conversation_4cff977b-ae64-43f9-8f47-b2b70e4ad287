package com.foshan.form.community.request;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;

import com.foshan.form.community.CommunityInstallmentSmsForm;
import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "账单分期请求参数(CommunityInstallmentSmsReq)")
public class CommunityInstallmentSmsReq extends BasePageRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = -8750562682930762262L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer installmentSmsId;
	@ApiModelProperty(value = "单元ID",example="1")
	private Integer estateId;
	@ApiModelProperty(value = "名称")
	private String installmentName;
	@ApiModelProperty(value = "单元编号")
	private String unitCode;
    @ApiModelProperty(value = "期数",example="1")
    private Integer periods;
    @ApiModelProperty(value = "金额",example="1")
    private String amount;
    @ApiModelProperty(value = "发送日期")
    private String sendDate;
    @ApiModelProperty(value = "发送状态，0：否；1：已发；",example="1")
    private Integer sendStatus;
    @ApiModelProperty(value = "支付状态，0：未支付；1：已支付；",example="1")
    private Integer payStatus;
    @ApiModelProperty(value = "使用状态，0：未使用；1：已使用；",example="1")
    private Integer useStatus;
    @ApiModelProperty(value = "管理ID",example="1")
    private String userIdList;
	@ApiModelProperty(value = "管家名称")
	private String userName;
	@ApiModelProperty(value = "管家手机")
	private String userPhone;
	@ApiModelProperty(value = "已选，如selected=1，installmentSmsId必填；0：未选；1：已选；",example="1")
	private Integer selected;
    @ApiModelProperty(value = "每期详情")
    private List<CommunityInstallmentSmsForm>  subInstallmentSmsList = new ArrayList<>();

}
