package com.foshan.form.party.request;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="统计请求参数(StatisticsReq)")
public class StatisticsReq extends BasePageRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = -2437742854229321215L;
	@ApiModelProperty(value = "开始时间")
	private String startTime;
	@ApiModelProperty(value = "结束时间")
	private String endTime;
	@ApiModelProperty(value = "支部Id",example="1")
	private Integer partyId;
	@ApiModelProperty(value = "党员ID",example="1")
	private Integer memberId;
}
