package com.foshan.form.party;


import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.ColumnForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="党建栏目对象(PartyColumnForm)")
@JsonInclude(Include.NON_NULL)
public class PartyColumnForm extends ColumnForm{
	/**
	 * 
	 */
	private static final long serialVersionUID = -3080960687027375131L;
	@ApiModelProperty(value = "支部组织", example = "1")
	private List<PartyOrganizationForm> partyOrganizationList = new ArrayList<PartyOrganizationForm>();
	@ApiModelProperty(value = "主题", example = "1")
	private List<PartyTopicForm> partyTopicFormList = new ArrayList<PartyTopicForm>();

}
