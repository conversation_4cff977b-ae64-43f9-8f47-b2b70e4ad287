package com.foshan.form.party.response.partyMember;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="新增党员返回对象(AddPartyMemberRes)")
@JsonInclude(Include.NON_NULL)
public class AddPartyMemberRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 3353356567264373054L;

}
