package com.foshan.entity.party;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.OneToMany;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AccountEntity;
import com.foshan.entity.AssetEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorValue("D")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class PartyMemberEntity extends AccountEntity {
	/**
	 * 党员
	 */
	private static final long serialVersionUID = -6800153538413904774L;
	@Column(columnDefinition = "int(2) comment '党内职务  1：书记；2：副书记；3：组织委员；4：宣传委员；5：纪检委员；6：党员；'")                     
	private Integer partyPosition;
	@Column(columnDefinition = "int(2) comment '在职状态'")                     
	private Integer workingState;
	@Column(columnDefinition = "int(2) comment '发展阶段 1：入党申请人，2：入党积极分子；3：发展对象；4：预备党员；5：正式党员；'")                     
	private Integer developmentStages;
	@Column(columnDefinition = "longtext comment '发展日志（json）'")
	private String developLog;
	@Column(columnDefinition = "longtext comment '备注'")
	private String meno;
	@Column(columnDefinition = "int(10) comment '排序'")  
	private Integer orders;
	
	@ManyToMany(targetEntity = PartyOrganizationEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_member_organization", joinColumns = @JoinColumn(name = "memberId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "partyId", referencedColumnName = "id"))
	private List<PartyOrganizationEntity> partyOrganizationList = new ArrayList<PartyOrganizationEntity>();
	
	@OneToMany(targetEntity = PartyActiveRecordsEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<PartyActiveRecordsEntity> activeRecordsList = new ArrayList<PartyActiveRecordsEntity>();
	
	@OneToMany(targetEntity = AssetEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<AssetEntity> assetList = new ArrayList<AssetEntity>();
	@ManyToMany(targetEntity = PartyRoleEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_member_role", joinColumns = @JoinColumn(name = "memberId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "roleId", referencedColumnName = "id"))
	private List<PartyRoleEntity> partyRoleList = new ArrayList<PartyRoleEntity>();
	
	@OneToMany(targetEntity = PartyAssetEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<PartyAssetEntity> operationLogList = new ArrayList<PartyAssetEntity>();
}
