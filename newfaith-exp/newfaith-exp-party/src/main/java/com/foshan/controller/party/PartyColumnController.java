package com.foshan.controller.party;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.party.request.PartyColumnReq;
import com.foshan.form.party.response.partyColumn.GetPartyColumnAssetList;
import com.foshan.form.party.response.partyColumn.GetPartyColumnListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.column.AddColumnRes;
import com.foshan.form.response.column.ModifyColumnRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "党建栏目模块")
@RestController
public class PartyColumnController extends BasePartyController {

	// 获取党建栏目列表
	@ApiOperation(value = "获取党建栏目列表(getPartyColumnList)", httpMethod = "POST", notes = "获取党建栏目列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getPartyColumnList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetPartyColumnListRes getPartyColumnList(@RequestBody PartyColumnReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetPartyColumnListRes res = (GetPartyColumnListRes) partyColumnService.getPartyColumnList(req);
		return res;
	}
	// 新增党建栏目
	@ApiOperation(value = "新增党建栏目列表(addPartyColumn)", httpMethod = "POST", notes = "新增党建栏目列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/addPartyColumn", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddColumnRes addPartyColumnList(@RequestBody PartyColumnReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddColumnRes res = (AddColumnRes) partyColumnService.addPartyColumn(req);
		return res;
	}
	// 修改党建栏目
	@ApiOperation(value = "修改党建栏目(modifyPartyColumn)", httpMethod = "POST", notes = "修改党建栏目，columnId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyPartyColumn", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyColumnRes modifyPartyColumn(@RequestBody PartyColumnReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyColumnRes res = (ModifyColumnRes) partyColumnService.modifyPartyColumn(req);
		return res;
	}
	// 删除党建栏目
	@ApiOperation(value = "删除党建栏目(deletePartyColumn)", httpMethod = "POST", notes = "删除党建栏目，columnId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deletePartyColumn", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deletePartyColumn(@RequestBody PartyColumnReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) partyColumnService.deletePartyColumn(req);
		return res;
	}
	// 获取详情党建栏目
	@ApiOperation(value = "获取详情党建栏目(getPartyColumnInfo)", httpMethod = "POST", notes = "获取详情党建栏目，PartyColumnId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getPartyColumnInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetPartyColumnListRes getPartyColumnInfo(@RequestBody PartyColumnReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetPartyColumnListRes res = (GetPartyColumnListRes) partyColumnService.getPartyColumnInfo(req);
		return res;
	}

	// 删除党建栏目和主题的关联
	@ApiOperation(value = "删除党建栏目和主题的关联(removeColumnTopicRelevance)", httpMethod = "POST", notes = "删除党建栏目和主题的关联，columnId和topicId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/removeColumnTopicRelevance", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse removeColumnTopicRelevance(@RequestBody PartyColumnReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) partyColumnService.removeColumnTopicRelevance(req);
		return res;
	}
	
	// 增加党建栏目和主题的关联
	@ApiOperation(value = "增加党建栏目和主题的关联(addColumnTopicRelevance)", httpMethod = "POST", notes = "增加党建栏目和主题的关联，columnId和topicIdList不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addColumnTopicRelevance", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addColumnTopicRelevance(@RequestBody PartyColumnReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) partyColumnService.addColumnTopicRelevance(req);
		return res;
	}
	
	// 删除党建栏目和支部组织的关联
	@ApiOperation(value = "删除党建栏目和支部组织的关联(removeColumnPartyOrganizationRelevance)", httpMethod = "POST", notes = "删除党建栏目和支部组织的关联，columnId和partyOrganizationId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/removeColumnPartyOrganizationRelevance", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse removeColumnPartyOrganizationRelevance(@RequestBody PartyColumnReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) partyColumnService.removeColumnPartyOrganizationRelevance(req);
		return res;
	}
	
	// 增加党建栏目和支部组织的关联
	@ApiOperation(value = "增加党建栏目和主题的关联(addColumnPartyOrganizationRelevance)", httpMethod = "POST", notes = "增加党建栏目和主题的关联，columnId和partyOrganizationIdList不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addColumnPartyOrganizationRelevance", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addColumnPartyOrganizationRelevance(@RequestBody PartyColumnReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) partyColumnService.addColumnPartyOrganizationRelevance(req);
		return res;
	}
	
	
	// 获取栏目文章
	@ApiOperation(value = "获取栏目文章(getPartyColumnAssetList)", httpMethod = "POST", notes = "获取栏目文章，columnId和assetName选填；")
	@ResponseBody
	@RequestMapping(value = "/getPartyColumnAssetList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetPartyColumnAssetList getPartyColumnAssetList(@RequestBody PartyColumnReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetPartyColumnAssetList res = (GetPartyColumnAssetList) partyColumnService.getPartyColumnAssetList(req);
		return res;
	}
	
}

